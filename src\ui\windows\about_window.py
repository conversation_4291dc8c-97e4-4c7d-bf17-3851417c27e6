#!/usr/bin/env python3
"""
About Window Module

This module defines the AboutWindow class, which displays information about the application
including version, authors, license, and dependencies.
"""

import sys
from pathlib import Path

# Try to import PyQt6, fall back to PySide6 if not available
try:
    from PyQt6.QtCore import Qt, QSize
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QTabWidget, QWidget, QTextBrowser, QDialogButtonBox
    )
    from PyQt6.QtGui import QPixmap, QFont
    PYQT6 = True
except ImportError:
    try:
        from PySide6.QtCore import Qt, QSize
        from PySide6.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
            QTabWidget, QWidget, QTextBrowser, QDialogButtonBox
        )
        from PySide6.QtGui import QPixmap, QFont
        PYQT6 = False
    except ImportError:
        print("Error: Neither PyQt6 nor PySide6 is installed.")
        print("Please install one of them using pip:")
        print("pip install PyQt6")
        print("or")
        print("pip install PySide6")
        sys.exit(1)

# Try to import qtawesome for icons
try:
    import qtawesome as qta
    HAS_QTA = True
except ImportError:
    HAS_QTA = False

# Import application configuration
try:
    from src.config.app_config import AppConfig
except ImportError:
    # Fallback for direct module execution
    sys.path.insert(0, str(Path(__file__).resolve().parents[3]))
    from src.config.app_config import AppConfig


class AboutWindow(QDialog):
    """
    About Window for displaying application information.
    
    This dialog displays information about the application including:
    - Version information
    - Authors and contributors
    - License information
    - Third-party dependencies
    - System information
    """
    
    def __init__(self, parent=None, config=None):
        """
        Initialize the about window.
        
        Args:
            parent: Parent widget
            config: AppConfig instance or None to use the default
        """
        super().__init__(parent)
        self.setWindowTitle("About YouTube Downloader V2")
        self.resize(600, 450)
        
        # Set window icon if qtawesome is available
        if HAS_QTA:
            self.setWindowIcon(qta.icon('fa5s.info-circle'))
        
        # Get application configuration
        self.config = config or AppConfig()
        
        # Application information
        self.app_name = "YouTube Downloader V2"
        self.app_version = "2.0.0"
        self.app_description = "A powerful YouTube video downloader with search, download, and library management capabilities."
        self.app_copyright = "© 2025 YouTube Downloader V2 Team"
        self.app_website = "https://github.com/youtube-downloader-v2/youtube-downloader-v2"
        
        # Initialize UI
        self._init_ui()
    
    def _init_ui(self):
        """
        Initialize the user interface components.
        """
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Header section
        header_layout = QHBoxLayout()
        
        # Application logo
        logo_label = QLabel()
        if HAS_QTA:
            logo_pixmap = qta.icon('fa5b.youtube', color='red').pixmap(QSize(64, 64))
            logo_label.setPixmap(logo_pixmap)
        else:
            # Fallback text if icon is not available
            logo_label.setText("YT")
            logo_label.setStyleSheet("font-size: 48px; color: red; font-weight: bold;")
        
        header_layout.addWidget(logo_label)
        
        # Application info
        info_layout = QVBoxLayout()
        
        # Application name with larger font
        app_name_label = QLabel(self.app_name)
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        app_name_label.setFont(font)
        info_layout.addWidget(app_name_label)
        
        # Version
        version_label = QLabel(f"Version {self.app_version}")
        info_layout.addWidget(version_label)
        
        # Description
        description_label = QLabel(self.app_description)
        description_label.setWordWrap(True)
        info_layout.addWidget(description_label)
        
        # Copyright
        copyright_label = QLabel(self.app_copyright)
        info_layout.addWidget(copyright_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        main_layout.addLayout(header_layout)
        
        # Separator line
        separator = QLabel()
        separator.setFrameShape(QLabel.Shape.HLine)
        separator.setFrameShadow(QLabel.Shadow.Sunken)
        main_layout.addWidget(separator)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create tabs
        self.about_tab = self._create_about_tab()
        self.authors_tab = self._create_authors_tab()
        self.license_tab = self._create_license_tab()
        self.dependencies_tab = self._create_dependencies_tab()
        self.system_tab = self._create_system_tab()
        
        # Add tabs to tab widget
        self.tab_widget.addTab(self.about_tab, "About")
        self.tab_widget.addTab(self.authors_tab, "Authors")
        self.tab_widget.addTab(self.license_tab, "License")
        self.tab_widget.addTab(self.dependencies_tab, "Dependencies")
        self.tab_widget.addTab(self.system_tab, "System Info")
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)
        
        # Add dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        
        main_layout.addWidget(button_box)
    
    def _create_about_tab(self):
        """
        Create the about tab with general information.
        
        Returns:
            QWidget: The about tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # About text browser
        about_text = QTextBrowser()
        about_text.setOpenExternalLinks(True)
        about_text.setHtml(f"""
        <h3>YouTube Downloader V2</h3>
        <p>Version {self.app_version}</p>
        <p>{self.app_description}</p>
        <p>{self.app_copyright}</p>
        <p>Website: <a href="{self.app_website}">{self.app_website}</a></p>
        <p>YouTube Downloader V2 is a desktop application that allows you to search, download, and manage YouTube videos.</p>
        <h4>Features:</h4>
        <ul>
            <li>Search for YouTube videos</li>
            <li>Download videos in various formats and qualities</li>
            <li>Manage downloaded videos in a library</li>
            <li>Create and manage playlists</li>
            <li>Download entire playlists or channels</li>
            <li>Extract audio from videos</li>
        </ul>
        <p>This application is built with Python and PyQt6/PySide6.</p>
        """)
        
        layout.addWidget(about_text)
        
        return tab
    
    def _create_authors_tab(self):
        """
        Create the authors tab with contributor information.
        
        Returns:
            QWidget: The authors tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Authors text browser
        authors_text = QTextBrowser()
        authors_text.setOpenExternalLinks(True)
        authors_text.setHtml("""
        <h3>Authors and Contributors</h3>
        <p>YouTube Downloader V2 is developed and maintained by the YouTube Downloader V2 Team:</p>
        <ul>
            <li><strong>Lead Developer</strong>: John Doe (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
            <li><strong>UI Designer</strong>: Jane Smith (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
            <li><strong>Backend Developer</strong>: Bob Johnson (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
        </ul>
        <h4>Contributors</h4>
        <ul>
            <li>Alice Williams - Documentation</li>
            <li>Charlie Brown - Testing</li>
            <li>David Miller - Localization</li>
        </ul>
        <p>We would like to thank all the contributors who have helped make this project possible.</p>
        <p>If you would like to contribute to the project, please visit our GitHub repository.</p>
        """)
        
        layout.addWidget(authors_text)
        
        return tab
    
    def _create_license_tab(self):
        """
        Create the license tab with license information.
        
        Returns:
            QWidget: The license tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # License text browser
        license_text = QTextBrowser()
        license_text.setHtml("""
        <h3>MIT License</h3>
        <p>Copyright (c) 2025 YouTube Downloader V2 Team</p>
        <p>Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:</p>
        <p>The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.</p>
        <p>THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.</p>
        """)
        
        layout.addWidget(license_text)
        
        return tab
    
    def _create_dependencies_tab(self):
        """
        Create the dependencies tab with third-party library information.
        
        Returns:
            QWidget: The dependencies tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Dependencies text browser
        dependencies_text = QTextBrowser()
        dependencies_text.setOpenExternalLinks(True)
        dependencies_text.setHtml("""
        <h3>Third-Party Dependencies</h3>
        <p>YouTube Downloader V2 uses the following third-party libraries:</p>
        <ul>
            <li><strong>PyQt6/PySide6</strong>: Qt for Python - <a href="https://www.qt.io/qt-for-python">https://www.qt.io/qt-for-python</a></li>
            <li><strong>yt-dlp</strong>: A youtube-dl fork with additional features - <a href="https://github.com/yt-dlp/yt-dlp">https://github.com/yt-dlp/yt-dlp</a></li>
            <li><strong>qtawesome</strong>: Iconic fonts in PyQt and PySide applications - <a href="https://github.com/spyder-ide/qtawesome">https://github.com/spyder-ide/qtawesome</a></li>
            <li><strong>qdarkstyle</strong>: A dark style sheet for PyQt/PySide applications - <a href="https://github.com/ColinDuquesnoy/QDarkStyleSheet">https://github.com/ColinDuquesnoy/QDarkStyleSheet</a></li>
            <li><strong>requests</strong>: HTTP for Humans - <a href="https://requests.readthedocs.io/">https://requests.readthedocs.io/</a></li>
            <li><strong>pillow</strong>: Python Imaging Library - <a href="https://python-pillow.org/">https://python-pillow.org/</a></li>
        </ul>
        <p>Each of these libraries has its own license. Please refer to their respective websites for more information.</p>
        """)
        
        layout.addWidget(dependencies_text)
        
        return tab
    
    def _create_system_tab(self):
        """
        Create the system info tab with system information.
        
        Returns:
            QWidget: The system info tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # System info text browser
        import platform
        import os
        
        # Get Qt version
        if PYQT6:
            from PyQt6.QtCore import QT_VERSION_STR
            qt_binding = f"PyQt6 (Qt {QT_VERSION_STR})"
        else:
            from PySide6 import __version__ as pyside_version
            qt_binding = f"PySide6 {pyside_version}"
        
        # Get Python version
        python_version = platform.python_version()
        python_implementation = platform.python_implementation()
        
        # Get system information
        system = platform.system()
        release = platform.release()
        version = platform.version()
        machine = platform.machine()
        processor = platform.processor()
        
        system_text = QTextBrowser()
        system_text.setHtml(f"""
        <h3>System Information</h3>
        <h4>Application</h4>
        <ul>
            <li><strong>Version</strong>: {self.app_version}</li>
            <li><strong>Qt Binding</strong>: {qt_binding}</li>
        </ul>
        <h4>Python</h4>
        <ul>
            <li><strong>Version</strong>: {python_version}</li>
            <li><strong>Implementation</strong>: {python_implementation}</li>
            <li><strong>Executable</strong>: {sys.executable}</li>
        </ul>
        <h4>System</h4>
        <ul>
            <li><strong>OS</strong>: {system}</li>
            <li><strong>Release</strong>: {release}</li>
            <li><strong>Version</strong>: {version}</li>
            <li><strong>Machine</strong>: {machine}</li>
            <li><strong>Processor</strong>: {processor}</li>
        </ul>
        """)
        
        layout.addWidget(system_text)
        
        return tab


if __name__ == "__main__":
    # Test the about window
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    window = AboutWindow()
    window.show()
    sys.exit(app.exec())