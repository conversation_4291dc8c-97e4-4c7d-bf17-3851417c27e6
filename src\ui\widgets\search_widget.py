#!/usr/bin/env python3
"""
Search Widget Module

This module defines the search widget for finding YouTube videos.
"""

import sys
from typing import List, Dict, Any, Optional

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton,
        QLabel, QScrollArea, QComboBox, QSpinBox, QGridLayout,
        QFrame, QSizePolicy
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QSize
    from PyQt6.QtGui import QIcon
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton,
            QLabel, QScrollArea, QComboBox, QSpinBox, QGridLayout,
            QFrame, QSizePolicy
        )
        from PySide6.QtCore import Qt, Signal as pyqtSignal, QSize
        from PySide6.QtGui import QIcon
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required.")
        sys.exit(1)

try:
    import qtawesome as qta
    HAS_QTAWESOME = True
except ImportError:
    HAS_QTAWESOME = False

from src.config.app_config import AppConfig
from src.services.service_factory import ServiceFactory
from src.services.youtube_service import YouTubeService
from src.ui.components.video_card import VideoCard
from src.ui.utils.ui_validation import validate_search_query


class SearchWidget(QWidget):
    """Widget for searching YouTube videos."""
    
    # Signals
    search_started = pyqtSignal(str)
    search_completed = pyqtSignal(list)
    search_error = pyqtSignal(str)
    video_selected = pyqtSignal(dict)
    
    def __init__(self, config: AppConfig):
        """Initialize the search widget.
        
        Args:
            config: Application configuration
        """
        super().__init__()
        self.config = config
        
        # Initialize services
        self.service_factory = ServiceFactory()
        self.youtube_service = self.service_factory.get_service('youtube')
        
        # Initialize UI components
        self._init_ui()
        
        # Connect signals and slots
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.layout = QVBoxLayout(self)
        
        # Search controls
        self._create_search_controls()
        
        # Search filters
        self._create_search_filters()
        
        # Results area
        self._create_results_area()
        
        # Status label
        self.status_label = QLabel("Enter a search query to find videos")
        self.layout.addWidget(self.status_label)
    
    def _create_search_controls(self):
        """Create the search input and button."""
        search_layout = QHBoxLayout()
        
        # Search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search for videos...")
        search_layout.addWidget(self.search_input)
        
        # Search button
        self.search_button = QPushButton("Search")
        if HAS_QTAWESOME:
            self.search_button.setIcon(qta.icon("fa5s.search"))
        self.search_button.clicked.connect(self._on_search_clicked)
        search_layout.addWidget(self.search_button)
        
        self.layout.addLayout(search_layout)
    
    def _create_search_filters(self):
        """Create the search filter controls."""
        filters_frame = QFrame()
        filters_frame.setFrameShape(QFrame.Shape.StyledPanel)
        filters_layout = QGridLayout(filters_frame)
        
        # Sort by
        filters_layout.addWidget(QLabel("Sort by:"), 0, 0)
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["Relevance", "Date", "Rating", "Title", "View Count"])
        filters_layout.addWidget(self.sort_combo, 0, 1)
        
        # Max results
        filters_layout.addWidget(QLabel("Max results:"), 0, 2)
        self.max_results_spin = QSpinBox()
        self.max_results_spin.setRange(5, 50)
        self.max_results_spin.setValue(10)
        self.max_results_spin.setSingleStep(5)
        filters_layout.addWidget(self.max_results_spin, 0, 3)
        
        # Video type
        filters_layout.addWidget(QLabel("Type:"), 1, 0)
        self.type_combo = QComboBox()
        self.type_combo.addItems(["All", "Video", "Channel", "Playlist"])
        filters_layout.addWidget(self.type_combo, 1, 1)
        
        # Duration
        filters_layout.addWidget(QLabel("Duration:"), 1, 2)
        self.duration_combo = QComboBox()
        self.duration_combo.addItems(["Any", "Short", "Medium", "Long"])
        filters_layout.addWidget(self.duration_combo, 1, 3)
        
        self.layout.addWidget(filters_frame)
    
    def _create_results_area(self):
        """Create the search results area."""
        # Scroll area for results
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Container for results
        self.results_container = QWidget()
        self.results_layout = QVBoxLayout(self.results_container)
        
        self.scroll_area.setWidget(self.results_container)
        self.layout.addWidget(self.scroll_area)
    
    def _connect_signals(self):
        """Connect signals and slots."""
        # Connect search input enter key to search button
        self.search_input.returnPressed.connect(self.search_button.click)
        
        # Connect search signals
        self.search_started.connect(self._on_search_started)
        self.search_completed.connect(self._on_search_completed)
        self.search_error.connect(self._on_search_error)
    
    def _on_search_clicked(self):
        """Handle search button click."""
        query = self.search_input.text().strip()
        
        # Validate search query
        validation_result = validate_search_query(query)
        if not validation_result['valid']:
            self.status_label.setText(f"Error: {validation_result['message']}")
            return
        
        # Get search parameters
        max_results = self.max_results_spin.value()
        sort_by = self.sort_combo.currentText().lower()
        video_type = self.type_combo.currentText().lower()
        duration = self.duration_combo.currentText().lower()
        
        # Emit search started signal
        self.search_started.emit(query)
        
        # Perform search in a separate thread (to be implemented)
        self._perform_search(query, max_results, sort_by, video_type, duration)
    
    def _perform_search(self, query: str, max_results: int, sort_by: str, 
                        video_type: str, duration: str):
        """Perform the search operation.
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return
            sort_by: Sort order for results
            video_type: Type of videos to search for
            duration: Duration filter
        """
        # This would typically be done in a worker thread
        # For now, we'll just call the service directly
        try:
            # Convert UI parameters to service parameters
            search_params = {
                'max_results': max_results,
                'order': sort_by if sort_by != 'relevance' else None,
                'type': video_type if video_type != 'all' else None,
                'duration': duration if duration != 'any' else None
            }
            
            # Remove None values
            search_params = {k: v for k, v in search_params.items() if v is not None}
            
            # Call the service
            results = self.youtube_service.search_videos(query, **search_params)
            
            # Emit search completed signal
            self.search_completed.emit(results)
        except Exception as e:
            # Emit search error signal
            self.search_error.emit(str(e))
    
    def _on_search_started(self, query: str):
        """Handle search started event.
        
        Args:
            query: Search query string
        """
        # Clear previous results
        self._clear_results()
        
        # Update status
        self.status_label.setText(f"Searching for '{query}'...")
    
    def _on_search_completed(self, results: List[Dict[str, Any]]):
        """Handle search completed event.
        
        Args:
            results: List of search result dictionaries
        """
        # Clear previous results
        self._clear_results()
        
        if not results:
            self.status_label.setText("No results found")
            return
        
        # Display results
        for result in results:
            video_card = VideoCard(result)
            video_card.video_selected.connect(self._on_video_selected)
            self.results_layout.addWidget(video_card)
        
        # Add stretch to push cards to the top
        self.results_layout.addStretch()
        
        # Update status
        self.status_label.setText(f"Found {len(results)} results")
    
    def _on_search_error(self, error_message: str):
        """Handle search error event.
        
        Args:
            error_message: Error message string
        """
        self.status_label.setText(f"Error: {error_message}")
    
    def _on_video_selected(self, video_data: Dict[str, Any]):
        """Handle video selection event.
        
        Args:
            video_data: Dictionary containing video data
        """
        # Emit video selected signal
        self.video_selected.emit(video_data)
    
    def _clear_results(self):
        """Clear the search results."""
        # Remove all widgets from the results layout
        while self.results_layout.count() > 0:
            item = self.results_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()