#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for the LoggingService implementation.

This script demonstrates how to use the LoggingService through the ServiceFactory.
"""

import asyncio
import os
from pathlib import Path

from src.services.service_factory import AsyncServiceFactory
from src.services.service_interfaces import ILoggingService
from src.utils.logging_service import LogLevel, LogFormat, LogDestination


async def test_logging_service():
    """Test the LoggingService implementation."""
    print("Testing LoggingService...")
    
    # Create factory
    factory = AsyncServiceFactory()
    
    # Get logging service
    logging_service = await factory.create_logging_service_async()
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("./logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Test different logging configurations
    await test_console_logging(logging_service)
    await test_file_logging(logging_service, logs_dir)
    await test_both_logging(logging_service, logs_dir)
    await test_json_logging(logging_service, logs_dir)
    await test_context_logging(logging_service)
    
    print("\nAll tests completed successfully!")
    print(f"Log files can be found in: {logs_dir.absolute()}")


async def test_console_logging(logging_service: ILoggingService):
    """Test console logging."""
    print("\n1. Testing console logging...")
    
    # Set up logging with console output
    await logging_service.setup(
        log_level="DEBUG",
        log_format="text",
        log_destination="console",
        log_dir=None
    )
    
    # Get a logger
    logger = logging_service.get_logger("console_test")
    
    # Log messages at different levels
    logger.debug("This is a debug message (console)")
    logger.info("This is an info message (console)")
    logger.warning("This is a warning message (console)")
    logger.error("This is an error message (console)")
    
    # Shutdown logging
    await logging_service.shutdown()


async def test_file_logging(logging_service: ILoggingService, logs_dir: Path):
    """Test file logging."""
    print("\n2. Testing file logging...")
    
    # Set up logging with file output
    await logging_service.setup(
        log_level="INFO",
        log_format="text",
        log_destination="file",
        log_dir=str(logs_dir)
    )
    
    # Get a logger
    logger = logging_service.get_logger("file_test")
    
    # Log messages at different levels
    logger.debug("This is a debug message (file) - should not appear")
    logger.info("This is an info message (file)")
    logger.warning("This is a warning message (file)")
    logger.error("This is an error message (file)")
    
    # Shutdown logging
    await logging_service.shutdown()
    
    print(f"  Log messages written to file in {logs_dir}")


async def test_both_logging(logging_service: ILoggingService, logs_dir: Path):
    """Test logging to both console and file."""
    print("\n3. Testing both console and file logging...")
    
    # Set up logging with both console and file output
    await logging_service.setup(
        log_level="WARNING",
        log_format="text",
        log_destination="both",
        log_dir=str(logs_dir)
    )
    
    # Get a logger
    logger = logging_service.get_logger("both_test")
    
    # Log messages at different levels
    logger.debug("This is a debug message (both) - should not appear")
    logger.info("This is an info message (both) - should not appear")
    logger.warning("This is a warning message (both)")
    logger.error("This is an error message (both)")
    
    # Shutdown logging
    await logging_service.shutdown()


async def test_json_logging(logging_service: ILoggingService, logs_dir: Path):
    """Test JSON format logging."""
    print("\n4. Testing JSON format logging...")
    
    # Set up logging with JSON format
    await logging_service.setup(
        log_level="DEBUG",
        log_format="json",
        log_destination="both",
        log_dir=str(logs_dir)
    )
    
    # Get a logger
    logger = logging_service.get_logger("json_test")
    
    # Log messages at different levels
    logger.debug("This is a debug message (JSON)")
    logger.info("This is an info message (JSON)")
    logger.warning("This is a warning message (JSON)")
    logger.error("This is an error message (JSON)")
    
    # Log with extra data
    logger.info("Message with extra data", extra={"user_id": "test123", "action": "login"})
    
    # Shutdown logging
    await logging_service.shutdown()


async def test_context_logging(logging_service: ILoggingService):
    """Test context logging."""
    print("\n5. Testing context logging...")
    
    # Set up logging
    await logging_service.setup(
        log_level="INFO",
        log_format="text",
        log_destination="console",
        log_dir=None
    )
    
    # Get a logger
    logger = logging_service.get_logger("context_test")
    
    # Log without context
    logger.info("Log message without context")
    
    # Add context
    logging_service.add_context(user_id="user123", session_id="abc456")
    
    # Log with context
    logger.info("Log message with context")
    
    # Add more context
    logging_service.add_context(request_id="req789")
    
    # Log with additional context
    logger.info("Log message with additional context")
    
    # Shutdown logging
    await logging_service.shutdown()


if __name__ == "__main__":
    asyncio.run(test_logging_service())