import unittest
from datetime import datetime, timedelta
from pathlib import Path
import json

from src.core.domain.models.base import (
    Priority, Status, SizeInfo, ErrorInfo, Metadata, BaseEntity, WindowsPathMixin
)

class TestEnums(unittest.TestCase):
    def test_priority_enum(self):
        self.assertEqual(Priority.LOW, "low")
        self.assertEqual(Priority.NORMAL, "normal")
        self.assertEqual(Priority.HIGH, "high")
        self.assertEqual(Priority.URGENT, "urgent")
        
        # Test string conversion
        self.assertEqual(str(Priority.LOW), "low")
    
    def test_status_enum(self):
        self.assertEqual(Status.PENDING, "pending")
        self.assertEqual(Status.IN_PROGRESS, "in_progress")
        self.assertEqual(Status.COMPLETED, "completed")
        self.assertEqual(Status.FAILED, "failed")
        self.assertEqual(Status.CANCELLED, "cancelled")
        
        # Test string conversion
        self.assertEqual(str(Status.PENDING), "pending")

class TestSizeInfo(unittest.TestCase):
    def test_size_conversions(self):
        # Test with 1 MB
        size = SizeInfo(bytes=1024 * 1024)
        self.assertEqual(size.bytes, 1024 * 1024)
        self.assertEqual(size.kb, 1024)
        self.assertEqual(size.mb, 1)
        self.assertAlmostEqual(size.gb, 0.0009765625)
        
        # Test with 1 GB
        size = SizeInfo(bytes=1024 * 1024 * 1024)
        self.assertEqual(size.bytes, 1024 * 1024 * 1024)
        self.assertEqual(size.kb, 1024 * 1024)
        self.assertEqual(size.mb, 1024)
        self.assertEqual(size.gb, 1)
    
    def test_format_size(self):
        # Test bytes
        size = SizeInfo(bytes=500)
        self.assertEqual(size.format_size(), "500 bytes")
        
        # Test KB
        size = SizeInfo(bytes=1500)
        self.assertEqual(size.format_size(), "1.46 KB")
        
        # Test MB
        size = SizeInfo(bytes=1024 * 1024 * 2.5)
        self.assertEqual(size.format_size(), "2.50 MB")
        
        # Test GB
        size = SizeInfo(bytes=1024 * 1024 * 1024 * 3.75)
        self.assertEqual(size.format_size(), "3.75 GB")

class TestErrorInfo(unittest.TestCase):
    def test_error_info_creation(self):
        error = ErrorInfo(code="test_error", message="Test error message")
        self.assertEqual(error.code, "test_error")
        self.assertEqual(error.message, "Test error message")
        self.assertIsNone(error.details)
        self.assertIsInstance(error.timestamp, datetime)
        
        # Test with details
        details = {"field": "test_field", "value": "invalid"}
        error = ErrorInfo(code="validation_error", message="Validation failed", details=details)
        self.assertEqual(error.details, details)

class TestMetadata(unittest.TestCase):
    def test_metadata_defaults(self):
        metadata = Metadata()
        self.assertIsInstance(metadata.created_at, datetime)
        self.assertIsInstance(metadata.updated_at, datetime)
        self.assertEqual(metadata.version, 1)
        self.assertEqual(metadata.tags, [])
        self.assertEqual(metadata.custom_fields, {})
        
        # Test with custom values
        now = datetime.now()
        metadata = Metadata(
            created_at=now,
            version=2,
            tags=["test", "important"],
            custom_fields={"priority": "high"}
        )
        self.assertEqual(metadata.created_at, now)
        self.assertEqual(metadata.version, 2)
        self.assertEqual(metadata.tags, ["test", "important"])
        self.assertEqual(metadata.custom_fields, {"priority": "high"})

class TestBaseEntity(unittest.TestCase):
    def test_entity_creation(self):
        entity = BaseEntity()
        self.assertIsNotNone(entity.id)
        self.assertIsInstance(entity.metadata, Metadata)
        
        # Test with custom ID
        custom_id = "test-123"
        entity = BaseEntity(id=custom_id)
        self.assertEqual(entity.id, custom_id)
    
    def test_update_timestamp(self):
        entity = BaseEntity()
        original_updated_at = entity.metadata.updated_at
        original_version = entity.metadata.version
        
        # Wait a moment to ensure timestamp changes
        import time
        time.sleep(0.001)
        
        entity.update_timestamp()
        self.assertGreater(entity.metadata.updated_at, original_updated_at)
        self.assertEqual(entity.metadata.version, original_version + 1)
    
    def test_tag_operations(self):
        entity = BaseEntity()
        
        # Add tag
        entity.add_tag("test")
        self.assertIn("test", entity.metadata.tags)
        
        # Add duplicate tag (should not change)
        original_version = entity.metadata.version
        entity.add_tag("test")
        self.assertEqual(len(entity.metadata.tags), 1)
        self.assertEqual(entity.metadata.version, original_version)
        
        # Remove tag
        entity.remove_tag("test")
        self.assertNotIn("test", entity.metadata.tags)
        
        # Remove non-existent tag (should not change)
        original_version = entity.metadata.version
        entity.remove_tag("nonexistent")
        self.assertEqual(entity.metadata.version, original_version)
    
    def test_json_serialization(self):
        entity = BaseEntity()
        entity.add_tag("test")
        
        # Serialize to JSON
        json_str = entity.json()
        data = json.loads(json_str)
        
        # Check fields
        self.assertEqual(data["id"], entity.id)
        self.assertIn("metadata", data)
        self.assertIn("test", data["metadata"]["tags"])

class TestWindowsPathMixin(unittest.TestCase):
    def test_sanitize_windows_filename(self):
        mixin = WindowsPathMixin()
        
        # Test invalid characters
        self.assertEqual(mixin.sanitize_windows_filename("file<>:\"/\\|?*name"), "file__________name")
        
        # Test trailing dots and spaces
        self.assertEqual(mixin.sanitize_windows_filename("filename. "), "filename")
        
        # Test reserved names
        self.assertEqual(mixin.sanitize_windows_filename("CON"), "_CON")
        self.assertEqual(mixin.sanitize_windows_filename("CON.txt"), "_CON.txt")
        self.assertEqual(mixin.sanitize_windows_filename("NUL"), "_NUL")
        self.assertEqual(mixin.sanitize_windows_filename("COM1"), "_COM1")
        
        # Test length limitation
        long_name = "a" * 250 + ".txt"
        sanitized = mixin.sanitize_windows_filename(long_name)
        self.assertLessEqual(len(sanitized), 200)
        self.assertTrue(sanitized.endswith(".txt"))
    
    def test_validate_windows_path(self):
        mixin = WindowsPathMixin()
        
        # Valid paths
        self.assertTrue(mixin.validate_windows_path(Path("C:/Users/<USER>/Documents")))
        self.assertTrue(mixin.validate_windows_path(Path("D:/Downloads/file.txt")))
        
        # Invalid paths (with invalid characters)
        self.assertFalse(mixin.validate_windows_path(Path("C:/Users/<USER>/file?.txt")))
        self.assertFalse(mixin.validate_windows_path(Path("C:/Users/<USER>/file*.txt")))
        
        # Test path length validation
        long_path = Path("C:/" + "a" * 260)
        self.assertFalse(mixin.validate_windows_path(long_path))

if __name__ == "__main__":
    unittest.main()