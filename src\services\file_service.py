#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
File Service Implementation

This module implements the IFileService interface to handle file system operations
such as creating directories, validating paths, getting file sizes, moving files,
and deleting files.
"""

import asyncio
import ctypes
import os
import platform
import re
import shutil
import stat
import winreg
from pathlib import Path
from typing import List, Optional, Union, Dict, Any

from src.services.service_interfaces import IFileService


class FileServiceException(Exception):
    """Base exception for file service errors."""
    pass


class InvalidPathException(FileServiceException):
    """Raised when a path is invalid."""
    pass


class FileNotFoundException(FileServiceException):
    """Raised when a file is not found."""
    pass


class DirectoryNotFoundException(FileServiceException):
    """Raised when a directory is not found."""
    pass


class PermissionDeniedException(FileServiceException):
    """Raised when permission is denied for a file operation."""
    pass


class FileService(IFileService):
    """Implementation of the file service interface."""
    
    def __init__(self):
        """Initialize file service."""
        self.is_windows = platform.system() == "Windows"
        
    async def create_directory(self, directory_path: Union[str, Path]) -> bool:
        """Create a directory if it doesn't exist.
        
        Args:
            directory_path: Path to the directory to create
            
        Returns:
            True if directory was created or already exists
            
        Raises:
            FileServiceException: If directory cannot be created
        """
        try:
            # Convert to Path object if it's a string
            path = Path(directory_path)
            
            # Create directory if it doesn't exist
            if not path.exists():
                path.mkdir(parents=True, exist_ok=True)
                return True
                
            # If path exists but is not a directory, raise exception
            if not path.is_dir():
                raise FileServiceException(f"{directory_path} exists but is not a directory")
                
            return True
            
        except PermissionError:
            raise PermissionDeniedException(f"Permission denied when creating directory {directory_path}")
        except Exception as e:
            raise FileServiceException(f"Failed to create directory {directory_path}: {str(e)}")
    
    async def validate_path(self, path: Union[str, Path]) -> bool:
        """Validate if a path is valid and accessible.
        
        Args:
            path: Path to validate
            
        Returns:
            True if path is valid and accessible
            
        Raises:
            InvalidPathException: If path is invalid or inaccessible
        """
        try:
            # Convert to Path object if it's a string
            path_obj = Path(path)
            
            # Check if path is absolute
            if not path_obj.is_absolute():
                raise InvalidPathException(f"Path {path} is not absolute")
                
            # Check for invalid characters in Windows paths
            if self.is_windows:
                # Check for invalid Windows path characters (excluding the normal path separators)
                invalid_chars = re.compile(r'[<>:"|?*]')
                if invalid_chars.search(str(path)):
                    raise InvalidPathException(f"Path {path} contains invalid characters")
                    
                # Handle Windows long path if needed
                if len(str(path)) >= 260:
                    path = self._get_long_path(path)
            
            # Check if parent directory exists for non-existing paths
            if not path_obj.exists() and path_obj.parent:
                if not path_obj.parent.exists():
                    raise InvalidPathException(f"Parent directory {path_obj.parent} does not exist")
                    
            return True
            
        except InvalidPathException:
            raise
        except Exception as e:
            raise InvalidPathException(f"Invalid path {path}: {str(e)}")
    
    async def get_file_size(self, file_path: Union[str, Path]) -> int:
        """Get the size of a file in bytes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Size of the file in bytes
            
        Raises:
            FileNotFoundException: If file does not exist
            FileServiceException: If file size cannot be determined
        """
        try:
            # Convert to Path object if it's a string
            path = Path(file_path)
            
            # Check if file exists
            if not path.exists():
                raise FileNotFoundException(f"File {file_path} does not exist")
                
            # Check if it's a file
            if not path.is_file():
                raise FileServiceException(f"{file_path} is not a file")
                
            # Get file size
            return path.stat().st_size
            
        except FileNotFoundException:
            raise
        except PermissionError:
            raise PermissionDeniedException(f"Permission denied when accessing file {file_path}")
        except Exception as e:
            raise FileServiceException(f"Failed to get file size for {file_path}: {str(e)}")
    
    async def move_file(self, source_path: Union[str, Path], destination_path: Union[str, Path]) -> bool:
        """Move a file from source to destination.
        
        Args:
            source_path: Path to the source file
            destination_path: Path to the destination file
            
        Returns:
            True if file was moved successfully
            
        Raises:
            FileNotFoundException: If source file does not exist
            FileServiceException: If file cannot be moved
        """
        try:
            # Convert to Path objects if they're strings
            source = Path(source_path)
            destination = Path(destination_path)
            
            # Check if source exists
            if not source.exists():
                raise FileNotFoundException(f"Source file {source_path} does not exist")
                
            # Create destination directory if it doesn't exist
            await self.create_directory(destination.parent)
            
            # Move file
            shutil.move(str(source), str(destination))
            return True
            
        except FileNotFoundException:
            raise
        except PermissionError:
            raise PermissionDeniedException(f"Permission denied when moving file from {source_path} to {destination_path}")
        except Exception as e:
            raise FileServiceException(f"Failed to move file from {source_path} to {destination_path}: {str(e)}")
    
    async def delete_file(self, path: Union[str, Path]) -> bool:
        """Delete a file or directory.
        
        Args:
            path: Path to the file or directory to delete
            
        Returns:
            True if file or directory was deleted successfully
            
        Raises:
            FileNotFoundException: If file or directory does not exist
            FileServiceException: If file or directory cannot be deleted
        """
        try:
            # Convert to Path object if it's a string
            path_obj = Path(path)
            
            # Check if path exists
            if not path_obj.exists():
                raise FileNotFoundException(f"Path {path} does not exist")
                
            # Handle read-only files
            if path_obj.is_file() and not os.access(path, os.W_OK):
                # Make file writable
                os.chmod(path, stat.S_IWRITE)
                
            # Delete file or directory
            if path_obj.is_file():
                path_obj.unlink()
            else:
                shutil.rmtree(path)
                
            return True
            
        except FileNotFoundException:
            raise
        except PermissionError:
            raise PermissionDeniedException(f"Permission denied when deleting {path}")
        except Exception as e:
            raise FileServiceException(f"Failed to delete {path}: {str(e)}")
    
    async def list_files(self, directory_path: Union[str, Path], pattern: Optional[str] = None) -> List[Path]:
        """List files in a directory, optionally filtered by a pattern.
        
        Args:
            directory_path: Path to the directory
            pattern: Optional glob pattern to filter files
            
        Returns:
            List of file paths
            
        Raises:
            DirectoryNotFoundException: If directory does not exist
            FileServiceException: If files cannot be listed
        """
        try:
            # Convert to Path object if it's a string
            path = Path(directory_path)
            
            # Check if directory exists
            if not path.exists():
                raise DirectoryNotFoundException(f"Directory {directory_path} does not exist")
                
            # Check if it's a directory
            if not path.is_dir():
                raise FileServiceException(f"{directory_path} is not a directory")
                
            # List files with or without pattern
            if pattern:
                return list(path.glob(pattern))
            else:
                return [p for p in path.iterdir() if p.is_file()]
                
        except DirectoryNotFoundException:
            raise
        except PermissionError:
            raise PermissionDeniedException(f"Permission denied when listing files in {directory_path}")
        except Exception as e:
            raise FileServiceException(f"Failed to list files in {directory_path}: {str(e)}")
    
    async def ensure_long_path_support(self) -> bool:
        """Ensure support for long paths on Windows.
        
        Returns:
            True if long path support is enabled or not needed
            
        Raises:
            FileServiceException: If long path support cannot be enabled
        """
        # Only needed on Windows
        if not self.is_windows:
            return True
            
        try:
            # Check if long path support is already enabled
            if self._is_long_path_enabled():
                return True
                
            # Try to enable long path support
            return self._enable_long_paths_windows()
            
        except Exception as e:
            raise FileServiceException(f"Failed to ensure long path support: {str(e)}")
    
    # Helper methods
    
    def _format_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Human-readable size string
        """
        # Define size units
        units = ["B", "KB", "MB", "GB", "TB"]
        unit_index = 0
        size = float(size_bytes)
        
        # Convert to appropriate unit
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
            
        # Format with appropriate precision
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.2f} {units[unit_index]}"
    
    def _enable_long_paths_windows(self) -> bool:
        """Enable long path support on Windows by modifying registry.
        
        Returns:
            True if long path support was enabled successfully
        """
        try:
            # Path to the registry key
            key_path = r"SYSTEM\CurrentControlSet\Control\FileSystem"
            value_name = "LongPathsEnabled"
            
            # Open the registry key
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_WRITE) as key:
                # Set the value to 1 to enable long path support
                winreg.SetValueEx(key, value_name, 0, winreg.REG_DWORD, 1)
                
            return True
            
        except PermissionError:
            # If we don't have permission to modify registry, inform user
            print("Warning: Cannot enable long path support. Run as administrator to enable.")
            return False
        except Exception as e:
            print(f"Warning: Failed to enable long path support: {str(e)}")
            return False
    
    def _is_long_path_enabled(self) -> bool:
        """Check if long path support is enabled on Windows.
        
        Returns:
            True if long path support is enabled
        """
        try:
            # Path to the registry key
            key_path = r"SYSTEM\CurrentControlSet\Control\FileSystem"
            value_name = "LongPathsEnabled"
            
            # Open the registry key
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_READ) as key:
                # Get the value
                value, _ = winreg.QueryValueEx(key, value_name)
                return value == 1
                
        except Exception:
            # If we can't read the registry, assume it's not enabled
            return False
    
    def _get_long_path(self, path: Union[str, Path]) -> str:
        """Convert a path to Windows long path format.
        
        Args:
            path: Path to convert
            
        Returns:
            Path in Windows long path format
        """
        # Convert to string if it's a Path object
        path_str = str(path)
        
        # If path is already in long path format, return it
        if path_str.startswith("\\\\?\\"):
            return path_str
            
        # Convert to absolute path
        abs_path = os.path.abspath(path_str)
        
        # Add the long path prefix
        if abs_path.startswith("\\\\"):
            # UNC path
            return "\\\\?\\UNC\\" + abs_path[2:]
        else:
            # Local path
            return "\\\\?\\" + abs_path


# Example usage
def main():
    """Example usage of FileService."""
    async def example():
        # Create file service
        service = FileService()
        
        # Create a directory
        directory = Path("./test_directory")
        await service.create_directory(directory)
        print(f"Created directory: {directory}")
        
        # Validate a path
        file_path = directory / "test_file.txt"
        is_valid = await service.validate_path(file_path)
        print(f"Path {file_path} is valid: {is_valid}")
        
        # Create a test file
        with open(file_path, "w") as f:
            f.write("Hello, world!")
            
        # Get file size
        size = await service.get_file_size(file_path)
        print(f"File size: {service._format_size(size)}")
        
        # List files
        files = await service.list_files(directory)
        print(f"Files in directory: {[f.name for f in files]}")
        
        # Move file
        new_path = directory / "moved_file.txt"
        await service.move_file(file_path, new_path)
        print(f"Moved file to: {new_path}")
        
        # Delete file
        await service.delete_file(new_path)
        print(f"Deleted file: {new_path}")
        
        # Delete directory
        await service.delete_file(directory)
        print(f"Deleted directory: {directory}")
        
        # Ensure long path support
        if service.is_windows:
            long_path_enabled = await service.ensure_long_path_support()
            print(f"Long path support enabled: {long_path_enabled}")
    
    # Run the async example
    asyncio.run(example())


if __name__ == "__main__":
    main()