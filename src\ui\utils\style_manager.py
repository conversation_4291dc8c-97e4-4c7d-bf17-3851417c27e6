"""Style Manager for YouTube Downloader V2.

This module provides utilities for managing application styles and themes.
It handles loading CSS files, applying themes, and switching between light and dark modes.
"""

import os
import sys
from pathlib import Path

# Try to import from either PyQt6 or PySide6
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import QFile, QTextStream
    from PyQt6.QtGui import QPalette, QColor
    USING_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QFile, QTextStream
        from PySide6.QtGui import QPalette, QColor
        USING_PYQT = False
    except ImportError:
        raise ImportError("Neither PyQt6 nor PySide6 could be imported. Please install one of them.")

# Try to import qdarkstyle for additional theme options
try:
    import qdarkstyle
    HAS_QDARKSTYLE = True
except ImportError:
    HAS_QDARKSTYLE = False


class StyleManager:
    """Manages application styles and themes."""

    # Theme constants
    THEME_LIGHT = "light"
    THEME_DARK = "dark"
    THEME_SYSTEM = "system"
    THEME_QDARKSTYLE = "qdarkstyle"
    
    # Style file names
    STYLE_CUSTOM = "custom.css"
    STYLE_DARK = "dark_theme.css"
    
    @staticmethod
    def get_resources_path():
        """Get the absolute path to the resources directory."""
        # Get the directory of the current file
        current_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        # Navigate to the resources directory
        resources_dir = current_dir.parent / "resources"
        return resources_dir
    
    @staticmethod
    def get_styles_path():
        """Get the absolute path to the styles directory."""
        return StyleManager.get_resources_path() / "styles"
    
    @staticmethod
    def load_stylesheet(filename):
        """Load a stylesheet from a CSS file.
        
        Args:
            filename (str): The name of the CSS file to load.
            
        Returns:
            str: The content of the CSS file, or an empty string if the file could not be loaded.
        """
        try:
            styles_path = StyleManager.get_styles_path()
            file_path = styles_path / filename
            
            if not file_path.exists():
                print(f"Warning: Style file {file_path} not found.")
                return ""
            
            with open(file_path, "r", encoding="utf-8") as file:
                return file.read()
        except Exception as e:
            print(f"Error loading stylesheet {filename}: {e}")
            return ""
    
    @staticmethod
    def apply_theme(app, theme=THEME_SYSTEM):
        """Apply a theme to the application.
        
        Args:
            app (QApplication): The application instance.
            theme (str): The theme to apply. Can be one of: 'light', 'dark', 'system', 'qdarkstyle'.
            
        Returns:
            bool: True if the theme was applied successfully, False otherwise.
        """
        if not isinstance(app, QApplication):
            print("Error: app must be a QApplication instance.")
            return False
        
        # Reset any previously applied stylesheet
        app.setStyleSheet("")
        
        # Apply the selected theme
        if theme == StyleManager.THEME_LIGHT:
            # Apply light theme (default Qt style + custom stylesheet)
            app.setStyle("Fusion")  # Use Fusion style as a base
            stylesheet = StyleManager.load_stylesheet(StyleManager.STYLE_CUSTOM)
            app.setStyleSheet(stylesheet)
            return True
            
        elif theme == StyleManager.THEME_DARK:
            # Apply custom dark theme
            app.setStyle("Fusion")  # Use Fusion style as a base
            stylesheet = StyleManager.load_stylesheet(StyleManager.STYLE_DARK)
            app.setStyleSheet(stylesheet)
            return True
            
        elif theme == StyleManager.THEME_QDARKSTYLE:
            # Apply QDarkStyle if available
            if HAS_QDARKSTYLE:
                app.setStyle("Fusion")  # Use Fusion style as a base
                if USING_PYQT:
                    app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
                else:
                    app.setStyleSheet(qdarkstyle.load_stylesheet_pyside6())
                return True
            else:
                print("QDarkStyle is not installed. Falling back to custom dark theme.")
                return StyleManager.apply_theme(app, StyleManager.THEME_DARK)
                
        elif theme == StyleManager.THEME_SYSTEM:
            # Try to detect system theme and apply appropriate theme
            # This is a simple implementation and might not work on all platforms
            try:
                # Check if we can detect dark mode from system
                is_dark_mode = StyleManager._is_system_dark_mode()
                if is_dark_mode:
                    return StyleManager.apply_theme(app, StyleManager.THEME_DARK)
                else:
                    return StyleManager.apply_theme(app, StyleManager.THEME_LIGHT)
            except Exception as e:
                print(f"Error detecting system theme: {e}")
                # Fall back to light theme
                return StyleManager.apply_theme(app, StyleManager.THEME_LIGHT)
        else:
            print(f"Unknown theme: {theme}. Using light theme.")
            return StyleManager.apply_theme(app, StyleManager.THEME_LIGHT)
    
    @staticmethod
    def _is_system_dark_mode():
        """Detect if the system is using dark mode.
        
        Returns:
            bool: True if the system is using dark mode, False otherwise.
        """
        # This is a simple implementation and might not work on all platforms
        if sys.platform == "win32":
            try:
                import winreg
                registry = winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER)
                key = winreg.OpenKey(registry, r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize")
                value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
                return value == 0  # 0 means dark mode is enabled
            except Exception:
                return False
        elif sys.platform == "darwin":  # macOS
            try:
                import subprocess
                result = subprocess.run(
                    ["defaults", "read", "-g", "AppleInterfaceStyle"],
                    capture_output=True, text=True, check=False
                )
                return "Dark" in result.stdout
            except Exception:
                return False
        else:  # Linux and others - this is very environment-specific
            # This is a very basic check that might work for some desktop environments
            try:
                import subprocess
                # Try to get GTK theme name
                result = subprocess.run(
                    ["gsettings", "get", "org.gnome.desktop.interface", "gtk-theme"],
                    capture_output=True, text=True, check=False
                )
                return "dark" in result.stdout.lower()
            except Exception:
                return False
    
    @staticmethod
    def get_available_themes():
        """Get a list of available themes.
        
        Returns:
            list: A list of available theme names.
        """
        themes = [StyleManager.THEME_LIGHT, StyleManager.THEME_DARK, StyleManager.THEME_SYSTEM]
        if HAS_QDARKSTYLE:
            themes.append(StyleManager.THEME_QDARKSTYLE)
        return themes
    
    @staticmethod
    def get_theme_display_name(theme):
        """Get a user-friendly display name for a theme.
        
        Args:
            theme (str): The theme identifier.
            
        Returns:
            str: A user-friendly display name for the theme.
        """
        theme_names = {
            StyleManager.THEME_LIGHT: "Light Theme",
            StyleManager.THEME_DARK: "Dark Theme",
            StyleManager.THEME_SYSTEM: "System Theme",
            StyleManager.THEME_QDARKSTYLE: "QDarkStyle Theme"
        }
        return theme_names.get(theme, theme)