<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Deployment Workflow Diagram for YouTube Downloader V2 -->
  
  <!-- Title -->
  <text x="400" y="30" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">YouTube Downloader V2 - Deployment Workflow</text>
  
  <!-- Source Code -->
  <rect x="100" y="80" width="150" height="60" rx="10" ry="10" fill="#f9d5e5" stroke="#333" stroke-width="2"/>
  <text x="175" y="115" font-family="Arial" font-size="16" text-anchor="middle">Source Code</text>
  
  <!-- Build Process -->
  <rect x="350" y="80" width="150" height="60" rx="10" ry="10" fill="#d5f9e6" stroke="#333" stroke-width="2"/>
  <text x="425" y="115" font-family="Arial" font-size="16" text-anchor="middle">Build Process</text>
  
  <!-- Executable -->
  <rect x="600" y="80" width="150" height="60" rx="10" ry="10" fill="#e6f9d5" stroke="#333" stroke-width="2"/>
  <text x="675" y="115" font-family="Arial" font-size="16" text-anchor="middle">Executable</text>
  
  <!-- Testing -->
  <rect x="100" y="200" width="150" height="60" rx="10" ry="10" fill="#d5e6f9" stroke="#333" stroke-width="2"/>
  <text x="175" y="235" font-family="Arial" font-size="16" text-anchor="middle">Testing</text>
  
  <!-- Documentation -->
  <rect x="350" y="200" width="150" height="60" rx="10" ry="10" fill="#f9e6d5" stroke="#333" stroke-width="2"/>
  <text x="425" y="235" font-family="Arial" font-size="16" text-anchor="middle">Documentation</text>
  
  <!-- Packaging -->
  <rect x="600" y="200" width="150" height="60" rx="10" ry="10" fill="#e6d5f9" stroke="#333" stroke-width="2"/>
  <text x="675" y="235" font-family="Arial" font-size="16" text-anchor="middle">Packaging</text>
  
  <!-- Installer -->
  <rect x="350" y="320" width="150" height="60" rx="10" ry="10" fill="#f9d5d5" stroke="#333" stroke-width="2"/>
  <text x="425" y="355" font-family="Arial" font-size="16" text-anchor="middle">Installer</text>
  
  <!-- Distribution -->
  <rect x="350" y="440" width="150" height="60" rx="10" ry="10" fill="#d5d5f9" stroke="#333" stroke-width="2"/>
  <text x="425" y="475" font-family="Arial" font-size="16" text-anchor="middle">Distribution</text>
  
  <!-- Connections -->
  <!-- Source to Build -->
  <line x1="250" y1="110" x2="350" y2="110" stroke="#333" stroke-width="2"/>
  <polygon points="350,110 340,105 340,115" fill="#333"/>
  
  <!-- Build to Executable -->
  <line x1="500" y1="110" x2="600" y2="110" stroke="#333" stroke-width="2"/>
  <polygon points="600,110 590,105 590,115" fill="#333"/>
  
  <!-- Source to Testing -->
  <line x1="175" y1="140" x2="175" y2="200" stroke="#333" stroke-width="2"/>
  <polygon points="175,200 170,190 180,190" fill="#333"/>
  
  <!-- Source to Documentation -->
  <line x1="175" y1="140" x2="350" y2="200" stroke="#333" stroke-width="2"/>
  <polygon points="350,200 340,195 345,185" fill="#333"/>
  
  <!-- Executable to Packaging -->
  <line x1="675" y1="140" x2="675" y2="200" stroke="#333" stroke-width="2"/>
  <polygon points="675,200 670,190 680,190" fill="#333"/>
  
  <!-- Documentation to Packaging -->
  <line x1="500" y1="230" x2="600" y2="230" stroke="#333" stroke-width="2"/>
  <polygon points="600,230 590,225 590,235" fill="#333"/>
  
  <!-- Testing to Packaging -->
  <line x1="250" y1="230" x2="600" y2="230" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>
  <polygon points="600,230 590,225 590,235" fill="#333"/>
  
  <!-- Packaging to Installer -->
  <line x1="675" y1="260" x2="425" y2="320" stroke="#333" stroke-width="2"/>
  <polygon points="425,320 425,310 435,315" fill="#333"/>
  
  <!-- Installer to Distribution -->
  <line x1="425" y1="380" x2="425" y2="440" stroke="#333" stroke-width="2"/>
  <polygon points="425,440 420,430 430,430" fill="#333"/>
  
  <!-- Legend -->
  <rect x="600" y="500" width="20" height="20" fill="#f9d5e5" stroke="#333" stroke-width="1"/>
  <text x="625" y="515" font-family="Arial" font-size="12" text-anchor="start">Source Code</text>
  
  <rect x="600" y="525" width="20" height="20" fill="#d5f9e6" stroke="#333" stroke-width="1"/>
  <text x="625" y="540" font-family="Arial" font-size="12" text-anchor="start">Build Process</text>
  
  <rect x="600" y="550" width="20" height="20" fill="#e6f9d5" stroke="#333" stroke-width="1"/>
  <text x="625" y="565" font-family="Arial" font-size="12" text-anchor="start">Executable</text>
  
  <rect x="600" y="575" width="20" height="20" fill="#f9d5d5" stroke="#333" stroke-width="1"/>
  <text x="625" y="590" font-family="Arial" font-size="12" text-anchor="start">Installer</text>
  
  <!-- Process Description -->
  <text x="100" y="520" font-family="Arial" font-size="14" font-weight="bold">Deployment Process:</text>
  <text x="100" y="545" font-family="Arial" font-size="12">1. Source code is built into executable</text>
  <text x="100" y="565" font-family="Arial" font-size="12">2. Documentation is generated from source</text>
  <text x="100" y="585" font-family="Arial" font-size="12">3. Testing validates the build</text>
  <text x="100" y="605" font-family="Arial" font-size="12">4. Packaging combines executable and docs</text>
  <text x="100" y="625" font-family="Arial" font-size="12">5. Installer is created for distribution</text>
</svg>