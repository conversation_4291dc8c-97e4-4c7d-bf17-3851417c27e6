#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Download Service Implementation

This module implements the IDownloadService interface to manage download tasks,
track progress, and handle concurrent downloads using asyncio and yt-dlp.
"""

import asyncio
import datetime
import os
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Set, Callable, Any

import aiohttp
import yt_dlp

from src.services.service_interfaces import (
    IDownloadService, DownloadTask, ProgressInfo, DownloadStatus, VideoQuality
)
from src.utils.validation_framework import Val<PERSON><PERSON>R<PERSON>ult, FilePathValidator


class DownloadServiceException(Exception):
    """Base exception for download service errors."""
    pass


class TaskNotFoundException(DownloadServiceException):
    """Raised when a task with the given ID is not found."""
    pass


class InvalidTaskStateException(DownloadServiceException):
    """Raised when a task is in an invalid state for the requested operation."""
    pass


class DownloadFailedException(DownloadServiceException):
    """Raised when a download fails."""
    pass


class ProgressTracker:
    """Tracks download progress for a task."""
    
    def __init__(self, task_id: str):
        """Initialize progress tracker.
        
        Args:
            task_id: The ID of the task to track
        """
        self.task_id = task_id
        self.bytes_downloaded = 0
        self.total_bytes = 0
        self.speed = 0.0  # bytes per second
        self.eta = 0  # seconds
        self.status = DownloadStatus.QUEUED
        self.start_time = None
        self.last_update_time = None
        self.last_bytes = 0
        
    def start(self):
        """Mark the download as started."""
        self.status = DownloadStatus.DOWNLOADING
        self.start_time = time.time()
        self.last_update_time = self.start_time
        
    def update(self, bytes_downloaded: int, total_bytes: int):
        """Update progress information.
        
        Args:
            bytes_downloaded: Number of bytes downloaded so far
            total_bytes: Total number of bytes to download
        """
        now = time.time()
        self.bytes_downloaded = bytes_downloaded
        self.total_bytes = total_bytes
        
        # Calculate speed
        if self.last_update_time and now > self.last_update_time:
            time_diff = now - self.last_update_time
            bytes_diff = bytes_downloaded - self.last_bytes
            
            if time_diff > 0:
                self.speed = bytes_diff / time_diff
        
        # Calculate ETA
        if self.speed > 0 and total_bytes > bytes_downloaded:
            self.eta = int((total_bytes - bytes_downloaded) / self.speed)
        else:
            self.eta = 0
            
        self.last_bytes = bytes_downloaded
        self.last_update_time = now
        
    def pause(self):
        """Mark the download as paused."""
        self.status = DownloadStatus.PAUSED
        
    def resume(self):
        """Mark the download as resumed."""
        self.status = DownloadStatus.DOWNLOADING
        
    def complete(self):
        """Mark the download as completed."""
        self.status = DownloadStatus.COMPLETED
        self.bytes_downloaded = self.total_bytes
        self.eta = 0
        
    def fail(self):
        """Mark the download as failed."""
        self.status = DownloadStatus.FAILED
        
    def cancel(self):
        """Mark the download as canceled."""
        self.status = DownloadStatus.CANCELED
        
    def get_progress_info(self) -> ProgressInfo:
        """Get current progress information.
        
        Returns:
            ProgressInfo object with current progress details
        """
        # Calculate progress percentage
        if self.total_bytes > 0:
            progress_percentage = (self.bytes_downloaded / self.total_bytes) * 100
        else:
            progress_percentage = 0
            
        # Calculate time elapsed
        if self.start_time:
            time_elapsed = int(time.time() - self.start_time)
        else:
            time_elapsed = 0
            
        return ProgressInfo(
            bytes_downloaded=self.bytes_downloaded,
            total_bytes=self.total_bytes,
            speed=self.speed,
            eta=self.eta,
            status=self.status,
            progress_percentage=progress_percentage,
            time_elapsed=time_elapsed
        )


class DownloadService(IDownloadService):
    """Implementation of the download service interface."""
    
    def __init__(self, max_concurrent_downloads: int = 3):
        """Initialize download service.
        
        Args:
            max_concurrent_downloads: Maximum number of concurrent downloads
        """
        self.max_concurrent_downloads = max_concurrent_downloads
        self.tasks: Dict[str, DownloadTask] = {}
        self.progress_trackers: Dict[str, ProgressTracker] = {}
        self.active_downloads: Set[str] = set()
        self.download_queue: List[str] = []
        self.download_processes: Dict[str, asyncio.Task] = {}
        self.semaphore = asyncio.Semaphore(max_concurrent_downloads)
        self.path_validator = FilePathValidator()
        
    async def start_download(self, task: DownloadTask) -> str:
        """Start a download task and return task ID.
        
        Args:
            task: Download task to start
            
        Returns:
            Task ID
            
        Raises:
            DownloadServiceException: If the task cannot be started
        """
        # Validate destination path
        validation_result = self.path_validator.validate(str(task.destination))
        if not validation_result.is_valid:
            raise DownloadServiceException(f"Invalid destination path: {validation_result.message}")
        
        # Generate task ID if not provided
        if not task.task_id:
            task.task_id = str(uuid.uuid4())
            
        # Create progress tracker
        progress_tracker = ProgressTracker(task.task_id)
        self.progress_trackers[task.task_id] = progress_tracker
        
        # Store task
        self.tasks[task.task_id] = task
        
        # Add to queue and process queue
        self.download_queue.append(task.task_id)
        asyncio.create_task(self._process_queue())
        
        return task.task_id
        
    async def pause_download(self, task_id: str) -> bool:
        """Pause a running download.
        
        Args:
            task_id: ID of the task to pause
            
        Returns:
            True if paused successfully, False otherwise
            
        Raises:
            TaskNotFoundException: If task with given ID is not found
            InvalidTaskStateException: If task is not in a pausable state
        """
        if task_id not in self.tasks:
            raise TaskNotFoundException(f"Task with ID {task_id} not found")
            
        task = self.tasks[task_id]
        
        if task.status != DownloadStatus.DOWNLOADING:
            raise InvalidTaskStateException(f"Task is not in downloading state: {task.status}")
            
        # If task is active, cancel the download process
        if task_id in self.active_downloads and task_id in self.download_processes:
            # Cancel the task but don't wait for it to complete
            self.download_processes[task_id].cancel()
            
            # Update status
            task.status = DownloadStatus.PAUSED
            if task_id in self.progress_trackers:
                self.progress_trackers[task_id].pause()
                
            # Remove from active downloads
            self.active_downloads.discard(task_id)
            
            return True
            
        return False
        
    async def resume_download(self, task_id: str) -> bool:
        """Resume a paused download.
        
        Args:
            task_id: ID of the task to resume
            
        Returns:
            True if resumed successfully, False otherwise
            
        Raises:
            TaskNotFoundException: If task with given ID is not found
            InvalidTaskStateException: If task is not in a resumable state
        """
        if task_id not in self.tasks:
            raise TaskNotFoundException(f"Task with ID {task_id} not found")
            
        task = self.tasks[task_id]
        
        if task.status != DownloadStatus.PAUSED:
            raise InvalidTaskStateException(f"Task is not in paused state: {task.status}")
            
        # Add back to queue and process
        self.download_queue.append(task_id)
        asyncio.create_task(self._process_queue())
        
        return True
        
    async def cancel_download(self, task_id: str) -> bool:
        """Cancel a download.
        
        Args:
            task_id: ID of the task to cancel
            
        Returns:
            True if canceled successfully, False otherwise
            
        Raises:
            TaskNotFoundException: If task with given ID is not found
        """
        if task_id not in self.tasks:
            raise TaskNotFoundException(f"Task with ID {task_id} not found")
            
        task = self.tasks[task_id]
        
        # Remove from queue if it's there
        if task_id in self.download_queue:
            self.download_queue.remove(task_id)
            
        # If task is active, cancel the download process
        if task_id in self.active_downloads and task_id in self.download_processes:
            # Cancel the task
            self.download_processes[task_id].cancel()
            
        # Update status
        task.status = DownloadStatus.CANCELED
        if task_id in self.progress_trackers:
            self.progress_trackers[task_id].cancel()
            
        # Remove from active downloads
        self.active_downloads.discard(task_id)
        
        return True
        
    async def get_progress(self, task_id: str) -> Optional[ProgressInfo]:
        """Get download progress.
        
        Args:
            task_id: ID of the task to get progress for
            
        Returns:
            ProgressInfo object or None if task not found
            
        Raises:
            TaskNotFoundException: If task with given ID is not found
        """
        if task_id not in self.tasks:
            raise TaskNotFoundException(f"Task with ID {task_id} not found")
            
        if task_id in self.progress_trackers:
            return self.progress_trackers[task_id].get_progress_info()
            
        # If no progress tracker exists, create a basic one based on task status
        task = self.tasks[task_id]
        progress = ProgressInfo(
            bytes_downloaded=0,
            total_bytes=0,
            speed=0.0,
            eta=0,
            status=task.status,
            progress_percentage=0.0,
            time_elapsed=0
        )
        
        return progress
        
    async def get_all_tasks(self) -> List[DownloadTask]:
        """Get all download tasks.
        
        Returns:
            List of all download tasks
        """
        # Update progress information for all tasks
        for task_id, task in self.tasks.items():
            if task_id in self.progress_trackers:
                progress = self.progress_trackers[task_id].get_progress_info()
                task.progress = progress
                task.status = progress.status
                
        return list(self.tasks.values())
        
    async def get_task(self, task_id: str) -> Optional[DownloadTask]:
        """Get a specific download task.
        
        Args:
            task_id: ID of the task to get
            
        Returns:
            DownloadTask object or None if not found
            
        Raises:
            TaskNotFoundException: If task with given ID is not found
        """
        if task_id not in self.tasks:
            raise TaskNotFoundException(f"Task with ID {task_id} not found")
            
        task = self.tasks[task_id]
        
        # Update progress information
        if task_id in self.progress_trackers:
            progress = self.progress_trackers[task_id].get_progress_info()
            task.progress = progress
            task.status = progress.status
            
        return task
    
    # Helper methods
    
    async def _process_queue(self):
        """Process download queue."""
        # Process all queued tasks
        while self.download_queue:
            # Check if we can start more downloads
            if len(self.active_downloads) >= self.max_concurrent_downloads:
                # Wait for active downloads to complete
                await asyncio.sleep(1)
                continue
                
            # Get next task from queue
            task_id = self.download_queue.pop(0)
            
            # Skip if task no longer exists
            if task_id not in self.tasks:
                continue
                
            task = self.tasks[task_id]
            
            # Skip if task is not in QUEUED or PAUSED state
            if task.status not in [DownloadStatus.QUEUED, DownloadStatus.PAUSED]:
                continue
                
            # Start download
            self.active_downloads.add(task_id)
            download_task = asyncio.create_task(self._download_task(task_id))
            self.download_processes[task_id] = download_task
            
            # Wait a bit before starting next download to avoid overwhelming the system
            await asyncio.sleep(0.5)
    
    async def _download_task(self, task_id: str):
        """Download a task.
        
        Args:
            task_id: ID of the task to download
        """
        if task_id not in self.tasks:
            return
            
        task = self.tasks[task_id]
        progress_tracker = self.progress_trackers.get(task_id)
        
        if not progress_tracker:
            progress_tracker = ProgressTracker(task_id)
            self.progress_trackers[task_id] = progress_tracker
            
        # Update task status
        task.status = DownloadStatus.DOWNLOADING
        progress_tracker.start()
        
        try:
            # Ensure destination directory exists
            os.makedirs(os.path.dirname(task.destination), exist_ok=True)
            
            # Configure yt-dlp options
            ydl_opts = {
                'format': self._get_format_for_quality(task.quality),
                'outtmpl': str(task.destination),
                'progress_hooks': [lambda d: self._progress_hook(d, task_id)],
                'quiet': True,
                'no_warnings': True,
            }
            
            # Download using yt-dlp in a separate thread
            await self._run_yt_dlp(task.url, ydl_opts)
            
            # Mark as completed
            task.status = DownloadStatus.COMPLETED
            progress_tracker.complete()
            
        except asyncio.CancelledError:
            # Task was cancelled (paused or canceled)
            pass
            
        except Exception as e:
            # Mark as failed
            task.status = DownloadStatus.FAILED
            progress_tracker.fail()
            print(f"Download failed: {e}")
            
        finally:
            # Remove from active downloads
            self.active_downloads.discard(task_id)
            if task_id in self.download_processes:
                del self.download_processes[task_id]
                
            # Process queue again in case there are waiting tasks
            asyncio.create_task(self._process_queue())
    
    async def _run_yt_dlp(self, url: str, options: Dict[str, Any]):
        """Run yt-dlp in a separate thread to avoid blocking the event loop."""
        def _download():
            with yt_dlp.YoutubeDL(options) as ydl:
                return ydl.download([url])
        
        # Run in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _download)
    
    def _progress_hook(self, d: Dict[str, Any], task_id: str):
        """Progress hook for yt-dlp.
        
        Args:
            d: Progress information from yt-dlp
            task_id: ID of the task being downloaded
        """
        if task_id not in self.progress_trackers:
            return
            
        progress_tracker = self.progress_trackers[task_id]
        
        if d['status'] == 'downloading':
            # Update progress
            downloaded = d.get('downloaded_bytes', 0)
            total = d.get('total_bytes') or d.get('total_bytes_estimate', 0)
            
            progress_tracker.update(downloaded, total)
            
        elif d['status'] == 'finished':
            # Download finished, now post-processing
            pass
            
        elif d['status'] == 'error':
            # Download error
            progress_tracker.fail()
    
    def _get_format_for_quality(self, quality: VideoQuality) -> str:
        """Get yt-dlp format string for the given quality.
        
        Args:
            quality: VideoQuality enum value
            
        Returns:
            Format string for yt-dlp
        """
        if quality == VideoQuality.VERY_HIGH:
            return 'bestvideo[height>=1080]+bestaudio/best[height>=1080]/best'
        elif quality == VideoQuality.HIGH:
            return 'bestvideo[height>=720][height<1080]+bestaudio/best[height>=720][height<1080]/best'
        elif quality == VideoQuality.MEDIUM:
            return 'bestvideo[height>=480][height<720]+bestaudio/best[height>=480][height<720]/best'
        else:  # LOW
            return 'bestvideo[height<480]+bestaudio/best[height<480]/worst'


# Example usage
def main():
    """Example usage of DownloadService."""
    async def example():
        # Create download service
        service = DownloadService(max_concurrent_downloads=2)
        
        # Create a download task
        task = DownloadTask(
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            destination=Path("./downloads/video.mp4"),
            quality=VideoQuality.MEDIUM
        )
        
        try:
            # Start download
            task_id = await service.start_download(task)
            print(f"Started download with task ID: {task_id}")
            
            # Monitor progress
            for _ in range(10):
                await asyncio.sleep(1)
                progress = await service.get_progress(task_id)
                print(f"Progress: {progress.progress_percentage:.1f}% - Speed: {progress.speed/1024:.1f} KB/s")
                
            # Pause download
            await service.pause_download(task_id)
            print("Download paused")
            
            await asyncio.sleep(2)
            
            # Resume download
            await service.resume_download(task_id)
            print("Download resumed")
            
            # Wait for completion or cancel
            await asyncio.sleep(5)
            await service.cancel_download(task_id)
            print("Download canceled")
            
            # Get all tasks
            all_tasks = await service.get_all_tasks()
            print(f"Total tasks: {len(all_tasks)}")
            
        except DownloadServiceException as e:
            print(f"Error: {e}")
    
    # Run the async example
    asyncio.run(example())


if __name__ == "__main__":
    main()