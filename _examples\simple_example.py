#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple Example of YouTube Downloader

This script demonstrates a simple usage of the YouTube Downloader services
without the full command-line interface.
"""

import asyncio
import os
from pathlib import Path

from src.services.service_interfaces import VideoQuality, DownloadStatus
from src.services.service_factory import AsyncServiceFactory


async def main():
    """Simple example of using the YouTube Downloader services."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    youtube_service = await factory.create_youtube_service_async()
    download_service = await factory.create_download_service_async()
    file_service = await factory.create_file_service_async()
    config_service = await factory.create_config_service_async()
    
    # Load configuration
    config = await config_service.load_config()
    print(f"Download directory: {config.download_dir}")
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    
    # Example video URL
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick <PERSON> - Never Gonna Give You Up
    
    # Validate URL
    validation_result = await youtube_service.validate_url(video_url)
    if not validation_result.is_valid:
        print(f"Error: {validation_result.message}")
        return
    
    # Get video information
    print(f"Fetching video information for {video_url}...")
    video_info = await youtube_service.extract_video_info(video_url)
    
    if not video_info:
        print("Error: Failed to extract video information.")
        return
    
    print(f"Video title: {video_info.title}")
    print(f"Channel: {video_info.channel}")
    print(f"Duration: {video_info.duration} seconds")
    
    # Create safe filename
    safe_title = video_info.title.replace(' ', '_').replace('/', '_').replace('\\', '_')
    safe_title = ''.join(c for c in safe_title if c.isalnum() or c in '_-')
    filename = f"{safe_title}.mp4"
    
    # Set destination path
    destination = Path(config.download_dir) / filename
    
    # Create download task
    from src.services.service_interfaces import DownloadTask
    task = DownloadTask(
        url=video_url,
        destination=str(destination),
        quality=VideoQuality.MEDIUM,  # You can change this to any quality
        video_info=video_info
    )
    
    # Start download
    task_id = await download_service.start_download(task)
    print(f"Started download with task ID: {task_id}")
    
    # Monitor progress
    try:
        while True:
            # Get progress
            progress = await download_service.get_progress(task_id)
            
            if not progress:
                print("Error: Failed to get progress information.")
                break
            
            # Print progress
            status_text = progress.status.name
            if progress.status == DownloadStatus.DOWNLOADING:
                # Calculate speed in MB/s
                speed_mb = progress.speed / (1024 * 1024)
                
                # Format ETA
                eta_min = progress.eta // 60
                eta_sec = progress.eta % 60
                eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                
                # Print progress
                print(f"\rProgress: {progress.progress_percentage:.1f}% - Speed: {speed_mb:.2f} MB/s - ETA: {eta_text}", end="")
            else:
                # Print status for non-downloading states
                print(f"\rStatus: {status_text}", end="")
            
            # Check if download is complete, failed, or canceled
            if progress.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELED]:
                print("\nDownload finished with status:", status_text)
                break
            
            # Wait before checking again
            await asyncio.sleep(1)
    
    except KeyboardInterrupt:
        # Cancel download if user presses Ctrl+C
        print("\nCanceling download...")
        await download_service.cancel_download(task_id)
        print("Download canceled.")
    
    except Exception as e:
        print(f"\nError: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())