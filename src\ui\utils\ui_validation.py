#!/usr/bin/env python3
"""
UI Validation Utilities

This module provides validation functions for UI inputs.
"""

import re
from typing import Dict, Any, Union

# Import validation framework
from src.utils.validation_framework import validate, ValidationRule


def validate_search_query(query: str) -> Dict[str, Union[bool, str]]:
    """Validate a search query.
    
    Args:
        query: Search query string
        
    Returns:
        Dictionary with validation result
    """
    # Define validation rules
    rules = [
        ValidationRule(
            lambda q: bool(q.strip()),
            "Search query cannot be empty"
        ),
        ValidationRule(
            lambda q: len(q.strip()) >= 2,
            "Search query must be at least 2 characters long"
        ),
        ValidationRule(
            lambda q: len(q.strip()) <= 100,
            "Search query must be at most 100 characters long"
        )
    ]
    
    # Validate query
    return validate(query, rules)


def validate_download_options(options: Dict[str, Any]) -> Dict[str, Union[bool, str]]:
    """Validate download options.
    
    Args:
        options: Dictionary containing download options
        
    Returns:
        Dictionary with validation result
    """
    # Check if URL is present
    if 'url' not in options:
        return {
            'valid': False,
            'message': "URL is required"
        }
    
    url = options['url']
    
    # Define validation rules for URL
    url_rules = [
        ValidationRule(
            lambda u: bool(u.strip()),
            "URL cannot be empty"
        ),
        ValidationRule(
            lambda u: re.match(r'^https?://', u),
            "URL must start with http:// or https://"
        ),
        ValidationRule(
            lambda u: 'youtube.com' in u or 'youtu.be' in u,
            "URL must be a YouTube URL"
        )
    ]
    
    # Validate URL
    url_validation = validate(url, url_rules)
    if not url_validation['valid']:
        return url_validation
    
    # Validate other options if present
    if 'output_dir' in options and options['output_dir']:
        output_dir = options['output_dir']
        output_dir_rules = [
            ValidationRule(
                lambda d: bool(d.strip()),
                "Output directory cannot be empty"
            ),
            # Add more rules for output directory if needed
        ]
        output_dir_validation = validate(output_dir, output_dir_rules)
        if not output_dir_validation['valid']:
            return output_dir_validation
    
    if 'filename_template' in options and options['filename_template']:
        filename_template = options['filename_template']
        filename_template_rules = [
            ValidationRule(
                lambda t: bool(t.strip()),
                "Filename template cannot be empty"
            ),
            ValidationRule(
                lambda t: '%(title)s' in t or '%(id)s' in t,
                "Filename template must include %(title)s or %(id)s"
            )
        ]
        filename_template_validation = validate(filename_template, filename_template_rules)
        if not filename_template_validation['valid']:
            return filename_template_validation
    
    # All validations passed
    return {
        'valid': True,
        'message': "Validation successful"
    }


def validate_playlist_name(name: str) -> Dict[str, Union[bool, str]]:
    """Validate a playlist name.
    
    Args:
        name: Playlist name string
        
    Returns:
        Dictionary with validation result
    """
    # Define validation rules
    rules = [
        ValidationRule(
            lambda n: bool(n.strip()),
            "Playlist name cannot be empty"
        ),
        ValidationRule(
            lambda n: len(n.strip()) >= 2,
            "Playlist name must be at least 2 characters long"
        ),
        ValidationRule(
            lambda n: len(n.strip()) <= 50,
            "Playlist name must be at most 50 characters long"
        ),
        ValidationRule(
            lambda n: n not in ["All Videos", "Recently Added", "Favorites"],
            "Cannot use reserved playlist name"
        ),
        ValidationRule(
            lambda n: re.match(r'^[\w\s\-\.]+$', n),
            "Playlist name can only contain letters, numbers, spaces, hyphens, and periods"
        )
    ]
    
    # Validate name
    return validate(name, rules)


def validate_settings(settings: Dict[str, Any]) -> Dict[str, Union[bool, str, Dict[str, Union[bool, str]]]]:
    """Validate application settings.
    
    Args:
        settings: Dictionary containing settings
        
    Returns:
        Dictionary with validation results
    """
    # Initialize results
    results = {
        'valid': True,
        'message': "All settings are valid",
        'fields': {}
    }
    
    # Validate download directory
    if 'download_dir' in settings:
        download_dir = settings['download_dir']
        download_dir_rules = [
            ValidationRule(
                lambda d: bool(d.strip()),
                "Download directory cannot be empty"
            )
            # Add more rules for download directory if needed
        ]
        download_dir_validation = validate(download_dir, download_dir_rules)
        results['fields']['download_dir'] = download_dir_validation
        if not download_dir_validation['valid']:
            results['valid'] = False
    
    # Validate max concurrent downloads
    if 'max_concurrent_downloads' in settings:
        max_concurrent = settings['max_concurrent_downloads']
        try:
            max_concurrent = int(max_concurrent)
            if max_concurrent < 1 or max_concurrent > 10:
                results['fields']['max_concurrent_downloads'] = {
                    'valid': False,
                    'message': "Max concurrent downloads must be between 1 and 10"
                }
                results['valid'] = False
            else:
                results['fields']['max_concurrent_downloads'] = {
                    'valid': True,
                    'message': "Validation successful"
                }
        except (ValueError, TypeError):
            results['fields']['max_concurrent_downloads'] = {
                'valid': False,
                'message': "Max concurrent downloads must be a number"
            }
            results['valid'] = False
    
    # Update overall message if any field is invalid
    if not results['valid']:
        results['message'] = "Some settings are invalid"
    
    return results