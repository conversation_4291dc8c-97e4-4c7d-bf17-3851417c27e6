# Service Layer Documentation

## Overview

The service layer provides the core business logic for the YouTube Downloader application. It implements a clean architecture pattern with well-defined interfaces, comprehensive validation, and robust error handling.

## Architecture

### Service Interfaces

All services implement well-defined interfaces that establish clear contracts:

- `IYouTubeService`: YouTube data extraction and validation
- `IDownloadService`: Download management and progress tracking
- `IFileService`: File system operations
- `IConfigService`: Configuration management
- `ILoggingService`: Logging infrastructure
- `IServiceFactory`: Service creation and dependency injection

### Service Implementations

Each interface has a corresponding implementation:

- `YouTubeService`: Uses yt-dlp for YouTube integration
- `DownloadService`: Manages concurrent downloads with asyncio
- `FileService`: Cross-platform file operations
- `ConfigService`: JSON-based configuration persistence
- `LoggingService`: Structured logging with rotation
- `ServiceFactory`: Singleton-based service management

## Key Features

### 1. Comprehensive Validation

The service layer includes a robust validation framework:

```python
from src.services.validation import YouTubeServiceValidator

validator = YouTubeServiceValidator()
result = validator.validate_video_extraction(url)
if not result.is_valid:
    print(f"Validation failed: {result.get_error_messages()}")
```

### 2. Progress Tracking

Advanced progress tracking with persistence and callbacks:

```python
from src.services.utils.progress_tracker import ProgressTracker

tracker = ProgressTracker(storage_path=Path("./progress"))
tracker.register_callback(lambda event: print(f"Progress: {event.bytes_downloaded}"))
tracker.update_progress("task_1", 1024, 10240)
```

### 3. Exception Handling

Comprehensive exception hierarchy for different error types:

```python
from src.services.exceptions import YouTubeServiceException, VideoExtractionException

try:
    video_info = await youtube_service.extract_video_info(url)
except VideoExtractionException as e:
    print(f"Failed to extract video: {e.message}")
except YouTubeServiceException as e:
    print(f"YouTube service error: {e.message}")
```

### 4. Service Factory Pattern

Centralized service creation with dependency injection:

```python
from src.services.service_factory import AsyncServiceFactory

factory = AsyncServiceFactory()
youtube_service = await factory.create_youtube_service_async()
download_service = await factory.create_download_service_async()
```

## Usage Examples

### Basic Video Information Extraction

```python
import asyncio
from src.services.service_factory import AsyncServiceFactory

async def extract_video_info():
    factory = AsyncServiceFactory()
    youtube_service = await factory.create_youtube_service_async()
    
    url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    video_info = await youtube_service.extract_video_info(url)
    
    print(f"Title: {video_info.title}")
    print(f"Duration: {video_info.duration} seconds")
    print(f"Author: {video_info.author}")

asyncio.run(extract_video_info())
```

### Download Management

```python
import asyncio
from pathlib import Path
from src.services.service_factory import AsyncServiceFactory
from src.services.service_interfaces import DownloadTask, VideoQuality

async def download_video():
    factory = AsyncServiceFactory()
    download_service = await factory.create_download_service_async()
    youtube_service = await factory.create_youtube_service_async()
    
    url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    video_info = await youtube_service.extract_video_info(url)
    
    task = DownloadTask(
        url=url,
        destination=Path("./downloads/video.mp4"),
        quality=VideoQuality.HIGH,
        video_info=video_info
    )
    
    task_id = await download_service.start_download(task)
    print(f"Download started with ID: {task_id}")
    
    # Monitor progress
    while True:
        progress = await download_service.get_progress(task_id)
        if progress and progress.is_complete:
            break
        await asyncio.sleep(1)

asyncio.run(download_video())
```

### Configuration Management

```python
import asyncio
from pathlib import Path
from src.services.service_factory import AsyncServiceFactory

async def manage_config():
    factory = AsyncServiceFactory()
    config_service = await factory.create_config_service_async()
    
    # Load configuration
    config = await config_service.load_config()
    print(f"Download directory: {config.download_dir}")
    
    # Update setting
    await config_service.update_setting("max_concurrent_downloads", 5)
    
    # Save configuration
    await config_service.save_config(config)

asyncio.run(manage_config())
```

### File Operations

```python
import asyncio
from pathlib import Path
from src.services.service_factory import AsyncServiceFactory

async def file_operations():
    factory = AsyncServiceFactory()
    file_service = await factory.create_file_service_async()
    
    # Create directory
    download_dir = Path("./downloads")
    await file_service.create_directory(download_dir)
    
    # Validate path
    is_valid = await file_service.validate_path(download_dir)
    print(f"Path is valid: {is_valid}")
    
    # Get file size
    test_file = download_dir / "test.txt"
    if test_file.exists():
        size_info = await file_service.get_file_size(test_file)
        print(f"File size: {size_info.formatted}")

asyncio.run(file_operations())
```

## Validation Framework

### Service Validators

Each service has dedicated validators:

```python
from src.services.validation import (
    YouTubeServiceValidator,
    DownloadServiceValidator,
    FileServiceValidator,
    ConfigServiceValidator
)

# YouTube service validation
youtube_validator = YouTubeServiceValidator()
result = youtube_validator.validate_video_extraction(url)

# Download service validation
download_validator = DownloadServiceValidator()
result = download_validator.validate_download_start(task_data)
```

### Custom Validation

Create custom validators for specific needs:

```python
from src.services.validation import ServiceValidator, ServiceValidationResult

class CustomValidator(ServiceValidator):
    def __init__(self):
        super().__init__("CustomService")
    
    def validate(self, data, operation="unknown"):
        result = self.create_result(operation)
        
        if not data:
            result.add_error("Data is required")
        
        return result
```

## Error Handling

### Exception Hierarchy

```
ServiceException
├── ServiceValidationException
├── ServiceConfigurationException
├── ServiceConnectionException
├── ServiceTimeoutException
├── YouTubeServiceException
│   ├── VideoExtractionException
│   └── PlaylistExtractionException
├── DownloadServiceException
│   ├── TaskNotFoundException
│   ├── InvalidTaskStateException
│   └── DownloadFailedException
├── FileServiceException
│   ├── FileNotFoundException
│   ├── FilePermissionException
│   └── DiskSpaceException
├── ConfigServiceException
│   ├── ConfigNotFoundException
│   └── ConfigValidationException
└── LoggingServiceException
```

### Error Handling Best Practices

```python
from src.services.exceptions import ServiceException, YouTubeServiceException

try:
    result = await service.perform_operation(data)
except YouTubeServiceException as e:
    # Handle YouTube-specific errors
    print(f"YouTube error: {e.message}")
    print(f"Error code: {e.code}")
    print(f"Details: {e.details}")
except ServiceException as e:
    # Handle general service errors
    print(f"Service error: {e.message}")
except Exception as e:
    # Handle unexpected errors
    print(f"Unexpected error: {e}")
```

## Testing

### Unit Tests

Each service component has comprehensive unit tests:

```bash
# Run all service tests
pytest tests/test_services/

# Run specific service tests
pytest tests/test_services/test_youtube_service.py
pytest tests/test_services/test_download_service.py
```

### Integration Tests

Integration tests verify service interactions:

```bash
# Run integration tests
pytest tests/test_services/test_service_integration.py
```

### Mock Testing

Use mocks for external dependencies:

```python
from unittest.mock import AsyncMock, patch
import pytest

@pytest.mark.asyncio
async def test_youtube_service_with_mock():
    with patch('yt_dlp.YoutubeDL') as mock_ydl:
        mock_ydl.return_value.extract_info = AsyncMock(return_value={
            'title': 'Test Video',
            'duration': 120
        })
        
        youtube_service = YouTubeService()
        video_info = await youtube_service.extract_video_info(test_url)
        
        assert video_info.title == 'Test Video'
        assert video_info.duration == 120
```

## Performance Considerations

### Caching

Services implement intelligent caching:

- Video information caching with TTL
- Configuration caching
- Progress state persistence

### Async Operations

All I/O operations are asynchronous:

```python
# Good: Concurrent operations
tasks = [
    youtube_service.extract_video_info(url1),
    youtube_service.extract_video_info(url2),
    youtube_service.extract_video_info(url3)
]
results = await asyncio.gather(*tasks)

# Avoid: Sequential operations
results = []
for url in urls:
    result = await youtube_service.extract_video_info(url)
    results.append(result)
```

### Resource Management

Proper resource cleanup:

```python
try:
    service = await factory.create_service_async()
    result = await service.perform_operation()
finally:
    await service.cleanup()  # If applicable
```

## Configuration

### Service Configuration

Services can be configured through the configuration system:

```python
from src.config import settings_manager

# Configure YouTube service
settings_manager.update_setting("youtube.cache_size", 200)
settings_manager.update_setting("youtube.cache_ttl", 7200)

# Configure download service
settings_manager.update_setting("downloads.max_concurrent", 5)
settings_manager.update_setting("downloads.timeout", 60)
```

### Environment-Specific Settings

Different configurations for different environments:

```python
from src.config import create_development_config, create_production_config

# Development
dev_config = create_development_config()
dev_config.app.log_level = "DEBUG"

# Production
prod_config = create_production_config()
prod_config.app.log_level = "INFO"
```

## Extending the Service Layer

### Adding New Services

1. Define the interface:

```python
from abc import ABC, abstractmethod

class INewService(ABC):
    @abstractmethod
    async def new_operation(self, data: str) -> str:
        pass
```

2. Implement the service:

```python
class NewService(INewService):
    async def new_operation(self, data: str) -> str:
        # Implementation
        return processed_data
```

3. Add to service factory:

```python
def create_new_service(self) -> INewService:
    return self._get_or_create_service(INewService, NewService)
```

### Adding Validation

1. Create validator:

```python
class NewServiceValidator(ServiceValidator):
    def __init__(self):
        super().__init__("NewService")
    
    def validate(self, data, operation="unknown"):
        result = self.create_result(operation)
        # Add validation logic
        return result
```

2. Integrate with service:

```python
class NewService(INewService):
    def __init__(self):
        self._validator = NewServiceValidator()
    
    async def new_operation(self, data: str) -> str:
        validation_result = self._validator.validate(data, "new_operation")
        if not validation_result.is_valid:
            raise ServiceValidationException("Validation failed")
        # Continue with operation
```

This documentation provides a comprehensive guide to using and extending the service layer of the YouTube Downloader application.
