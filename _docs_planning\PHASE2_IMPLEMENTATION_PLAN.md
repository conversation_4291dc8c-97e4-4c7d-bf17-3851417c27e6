# Phase 2 Implementation Plan: Core Domain Models

## Overview
Phase 2 focuses on implementing the core domain models that form the foundation of the YouTube Downloader V3 application. These models represent the essential business entities and value objects that encapsulate the application's core business logic, with an emphasis on type safety, validation, and Windows optimization.

## Goals
- Define foundational data structures using Pydantic for type safety and validation
- Implement business logic within domain models
- Create Windows-optimized utilities for file handling
- Establish validation rules and constraints
- Provide comprehensive error handling
- Enable serialization and deserialization capabilities

## File Structure to Create

```
src/core/
├── __init__.py
├── domain/
│   ├── __init__.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── video.py
│   │   └── download.py
│   └── exceptions/
│       ├── __init__.py
│       └── domain_exceptions.py
tests/test_core/
├── __init__.py
└── test_domain/
    ├── __init__.py
    ├── test_base.py
    ├── test_video.py
    └── test_download.py
```

## Enhanced Implementation Workflow

### 1. Workflow Overview

```mermaid
graph TD
    A[Define Domain Models] --> B[Implement Base Models]
    B --> C[Implement Video Models]
    C --> D[Implement Download Models]
    D --> E[Implement Domain Exceptions]
    E --> F[Create Tests]
    F --> G[Validate Implementation]
    
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style B fill:#eeeeee,stroke:#333,stroke-width:2px
    style C fill:#d5f9e5,stroke:#333,stroke-width:2px
    style D fill:#e5d5f9,stroke:#333,stroke-width:2px
    style E fill:#f9e5d5,stroke:#333,stroke-width:2px
    style F fill:#d5e5f9,stroke:#333,stroke-width:2px
    style G fill:#f5f5f5,stroke:#333,stroke-width:2px
```

### 2. Quality Gates

#### Entry Criteria for Phase 2
- Phase 1 (Foundation and Configuration) completed and validated
- Configuration system implemented and tested
- Development environment configured
- Required dependencies available

#### Exit Criteria for Phase 2
- All domain models implemented
- Business logic encapsulated in models
- Windows-optimized utilities implemented
- Unit test coverage > 90%
- Documentation complete
- Code quality checks passing

### 3. Implementation Steps with Validation

#### Step 1: Implement Base Models
1. Create `src/core/domain/models/` directory structure
2. Implement core enumerations (Priority, Status)
3. Create value objects (SizeInfo, ErrorInfo, Metadata)
4. Implement BaseEntity with metadata support
5. Create Windows path utilities
6. **Validation**: Verify base models meet requirements
7. **Quality Gate**: Base models code review

#### Step 2: Implement Video Models
1. Create video-related enumerations
2. Implement VideoFormat model
3. Create VideoInfo model with validation
4. Add format selection logic
5. Implement Windows filename sanitization
6. **Validation**: Verify video models handle all use cases
7. **Quality Gate**: Video models code review

#### Step 3: Implement Download Models
1. Create download-related enumerations
2. Implement progress tracking models
3. Create download configuration model
4. Implement download task model with state management
5. Add validation for all inputs
6. **Validation**: Verify download models support all states
7. **Quality Gate**: Download models code review

#### Step 4: Implement Domain Exceptions
1. Create base domain exception
2. Implement validation exceptions
3. Add video-specific exceptions
4. Create download-related exceptions
5. Implement file system exceptions
6. **Validation**: Verify exception hierarchy is consistent
7. **Quality Gate**: Exception handling review

#### Step 5: Create Tests
1. Implement base model tests
2. Create video model tests
3. Add download model tests
4. Implement exception tests
5. Create integration tests
6. **Validation**: Verify test coverage meets requirements
7. **Quality Gate**: Test quality review

#### Step 6: Documentation
1. Add comprehensive docstrings
2. Create API documentation
3. Document Windows-specific utilities
4. Add usage examples
5. Document validation rules
6. **Validation**: Verify documentation completeness
7. **Quality Gate**: Documentation review

## Detailed Implementation Plan

### 1. Base Models (`src/core/domain/models/base.py`)

#### 1.1 Core Enumerations
```python
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
import uuid
from pathlib import Path

class Priority(str, Enum):
    """Task priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class Status(str, Enum):
    """General status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
```

#### 1.2 Value Objects
```python
class SizeInfo(BaseModel):
    """Represents file size information"""
    bytes: int = Field(ge=0, description="Size in bytes")
    
    @property
    def kb(self) -> float:
        return self.bytes / 1024
    
    @property
    def mb(self) -> float:
        return self.bytes / (1024 * 1024)
    
    @property
    def gb(self) -> float:
        return self.bytes / (1024 * 1024 * 1024)
    
    def format_size(self) -> str:
        """Format size in human-readable format"""
        if self.gb >= 1:
            return f"{self.gb:.2f} GB"
        elif self.mb >= 1:
            return f"{self.mb:.2f} MB"
        elif self.kb >= 1:
            return f"{self.kb:.2f} KB"
        else:
            return f"{self.bytes} bytes"

class ErrorInfo(BaseModel):
    """Represents error information"""
    code: str = Field(description="Error code")
    message: str = Field(description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="When the error occurred")

class Metadata(BaseModel):
    """Base metadata for all entities"""
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    version: int = Field(default=1, ge=1)
    tags: List[str] = Field(default_factory=list)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)
```

#### 1.3 Base Entity
```python
class BaseEntity(BaseModel):
    """Base class for all domain entities"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    metadata: Metadata = Field(default_factory=Metadata)
    
    class Config:
        validate_assignment = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Path: lambda v: str(v)
        }
    
    def update_timestamp(self):
        """Update the last modified timestamp"""
        self.metadata.updated_at = datetime.now()
        self.metadata.version += 1
    
    def add_tag(self, tag: str):
        """Add a tag to the entity"""
        if tag not in self.metadata.tags:
            self.metadata.tags.append(tag)
            self.update_timestamp()
    
    def remove_tag(self, tag: str):
        """Remove a tag from the entity"""
        if tag in self.metadata.tags:
            self.metadata.tags.remove(tag)
            self.update_timestamp()
```

#### 1.4 Windows Path Utilities
```python
class WindowsPathMixin:
    """Mixin for Windows-specific path operations"""
    
    @staticmethod
    def sanitize_windows_filename(filename: str) -> str:
        """Sanitize filename for Windows compatibility"""
        # Remove invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove trailing dots and spaces
        filename = filename.rstrip('. ')
        
        # Handle reserved names
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        
        name_without_ext = filename.rsplit('.', 1)[0] if '.' in filename else filename
        if name_without_ext.upper() in reserved_names:
            filename = f"_{filename}"
        
        # Limit length (Windows has 260 character path limit)
        if len(filename) > 200:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:200-len(ext)-1] + ('.' + ext if ext else '')
        
        return filename
    
    @staticmethod
    def validate_windows_path(path: Path) -> bool:
        """Validate if path is valid for Windows"""
        try:
            # Check path length
            if len(str(path)) > 260:
                return False
            
            # Check for invalid characters in path components
            invalid_chars = '<>:"|?*'
            for part in path.parts:
                if any(char in part for char in invalid_chars):
                    return False
            
            return True
        except Exception:
            return False
```

### 2. Video Models (`src/core/domain/models/video.py`)

#### 2.1 Video Enumerations
```python
from enum import Enum
from typing import Dict, List, Optional, Any, Set, Union
from pydantic import BaseModel, Field, validator, root_validator
from datetime import datetime, timedelta
from pathlib import Path
import re

from .base import BaseEntity, SizeInfo, Status, WindowsPathMixin

class VideoQuality(str, Enum):
    """Video quality options"""
    AUDIO_ONLY = "audio_only"
    LOW_144P = "144p"
    LOW_240P = "240p"
    MEDIUM_360P = "360p"
    MEDIUM_480P = "480p"
    HIGH_720P = "720p"
    HIGH_1080P = "1080p"
    ULTRA_1440P = "1440p"
    ULTRA_2160P = "2160p"
    ULTRA_4320P = "4320p"

class VideoCodec(str, Enum):
    """Video codec options"""
    H264 = "h264"
    H265 = "h265"
    VP9 = "vp9"
    AV1 = "av1"
    WEBM = "webm"

class AudioCodec(str, Enum):
    """Audio codec options"""
    MP3 = "mp3"
    AAC = "aac"
    OPUS = "opus"
    VORBIS = "vorbis"
    FLAC = "flac"

class VideoContainer(str, Enum):
    """Video container options"""
    MP4 = "mp4"
    MKV = "mkv"
    WEBM = "webm"
    AVI = "avi"
    FLV = "flv"
```

#### 2.2 Video Format Model
```python
class VideoFormat(BaseModel):
    """Represents a video format option"""
    format_id: str = Field(description="Format ID from YouTube")
    quality: VideoQuality = Field(description="Video quality")
    container: VideoContainer = Field(description="Container format")
    video_codec: Optional[VideoCodec] = Field(default=None, description="Video codec")
    audio_codec: Optional[AudioCodec] = Field(default=None, description="Audio codec")
    is_audio_only: bool = Field(default=False, description="Whether this is an audio-only format")
    file_size: Optional[SizeInfo] = Field(default=None, description="Estimated file size")
    bitrate: Optional[int] = Field(default=None, description="Bitrate in kbps")
    fps: Optional[int] = Field(default=None, description="Frames per second")
    width: Optional[int] = Field(default=None, description="Video width")
    height: Optional[int] = Field(default=None, description="Video height")
    
    @property
    def resolution(self) -> Optional[str]:
        """Get the resolution as a string"""
        if self.width and self.height:
            return f"{self.width}x{self.height}"
        return None
    
    @property
    def display_name(self) -> str:
        """Get a user-friendly display name"""
        if self.is_audio_only:
            return f"Audio only ({self.audio_codec or 'unknown'}, {self.bitrate or '?'} kbps)"
        else:
            return f"{self.quality} ({self.container}, {self.video_codec or 'unknown'})"
```

#### 2.3 Video Information Model
```python
class VideoInfo(BaseEntity, WindowsPathMixin):
    """Represents information about a YouTube video"""
    video_id: str = Field(description="YouTube video ID")
    url: str = Field(description="Full YouTube URL")
    title: str = Field(description="Video title")
    description: Optional[str] = Field(default=None, description="Video description")
    channel_id: Optional[str] = Field(default=None, description="Channel ID")
    channel_name: Optional[str] = Field(default=None, description="Channel name")
    duration: Optional[int] = Field(default=None, description="Duration in seconds")
    view_count: Optional[int] = Field(default=None, description="View count")
    upload_date: Optional[datetime] = Field(default=None, description="Upload date")
    thumbnail_url: Optional[str] = Field(default=None, description="Thumbnail URL")
    available_formats: List[VideoFormat] = Field(default_factory=list, description="Available formats")
    tags: List[str] = Field(default_factory=list, description="Video tags")
    categories: List[str] = Field(default_factory=list, description="Video categories")
    is_live: bool = Field(default=False, description="Whether this is a live stream")
    
    @validator('video_id')
    def validate_video_id(cls, v):
        """Validate YouTube video ID format"""
        if not re.match(r'^[A-Za-z0-9_-]{11}$', v):
            raise ValueError("Invalid YouTube video ID format")
        return v
    
    @validator('url')
    def validate_url(cls, v):
        """Validate YouTube URL format"""
        if not re.match(r'^https?://(www\.)?(youtube\.com|youtu\.be)/.+$', v):
            raise ValueError("Invalid YouTube URL format")
        return v
    
    @property
    def duration_formatted(self) -> str:
        """Get formatted duration (HH:MM:SS)"""
        if not self.duration:
            return "Unknown"
        
        td = timedelta(seconds=self.duration)
        hours, remainder = divmod(td.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def get_safe_filename(self) -> str:
        """Get a safe filename based on the video title"""
        if not self.title:
            return f"video_{self.video_id}"
        
        # Sanitize the title
        safe_title = self.sanitize_windows_filename(self.title)
        
        # Add video ID to ensure uniqueness
        return f"{safe_title}_{self.video_id}"
    
    def get_best_format(self, quality: Optional[VideoQuality] = None, 
                       container: Optional[VideoContainer] = None,
                       audio_only: bool = False) -> Optional[VideoFormat]:
        """Get the best format matching the criteria"""
        if not self.available_formats:
            return None
        
        # Filter formats
        matching_formats = self.available_formats
        
        if audio_only:
            matching_formats = [f for f in matching_formats if f.is_audio_only]
        elif quality:
            matching_formats = [f for f in matching_formats if f.quality == quality]
        
        if container:
            matching_formats = [f for f in matching_formats if f.container == container]
        
        if not matching_formats:
            return None
        
        # Sort by quality and bitrate
        if audio_only:
            return sorted(matching_formats, key=lambda f: f.bitrate or 0, reverse=True)[0]
        else:
            # Sort by resolution and then by bitrate
            return sorted(matching_formats, 
                          key=lambda f: ((f.height or 0) * (f.width or 0), f.bitrate or 0), 
                          reverse=True)[0]
```

### 3. Download Models (`src/core/domain/models/download.py`)

#### 3.1 Download Enumerations
```python
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable
from pydantic import BaseModel, Field, validator, root_validator
from datetime import datetime
from pathlib import Path
import uuid

from .base import BaseEntity, Status, Priority, SizeInfo
from .video import VideoInfo, VideoFormat, VideoQuality, VideoContainer

class DownloadStatus(str, Enum):
    """Download status options"""
    QUEUED = "queued"
    PREPARING = "preparing"
    DOWNLOADING = "downloading"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DownloadSpeed(BaseModel):
    """Represents download speed information"""
    bytes_per_second: float = Field(ge=0, description="Download speed in bytes per second")
    
    @property
    def kb_per_second(self) -> float:
        return self.bytes_per_second / 1024
    
    @property
    def mb_per_second(self) -> float:
        return self.bytes_per_second / (1024 * 1024)
    
    def format_speed(self) -> str:
        """Format speed in human-readable format"""
        if self.mb_per_second >= 1:
            return f"{self.mb_per_second:.2f} MB/s"
        elif self.kb_per_second >= 1:
            return f"{self.kb_per_second:.2f} KB/s"
        else:
            return f"{self.bytes_per_second:.2f} B/s"
```

#### 3.2 Download Progress Model
```python
class ProgressInfo(BaseModel):
    """Represents download progress information"""
    bytes_downloaded: int = Field(ge=0, description="Bytes downloaded")
    total_bytes: Optional[int] = Field(default=None, description="Total bytes to download")
    percent_complete: float = Field(default=0, ge=0, le=100, description="Percent complete")
    speed: Optional[DownloadSpeed] = Field(default=None, description="Download speed")
    eta_seconds: Optional[int] = Field(default=None, description="Estimated time remaining in seconds")
    started_at: datetime = Field(default_factory=datetime.now, description="When download started")
    last_updated: datetime = Field(default_factory=datetime.now, description="When progress was last updated")
    
    @root_validator
    def calculate_percent(cls, values):
        """Calculate percent complete if not provided"""
        bytes_downloaded = values.get('bytes_downloaded', 0)
        total_bytes = values.get('total_bytes')
        percent_complete = values.get('percent_complete')
        
        if percent_complete is None and total_bytes and total_bytes > 0:
            values['percent_complete'] = min(100.0, (bytes_downloaded / total_bytes) * 100)
        
        return values
    
    @property
    def eta_formatted(self) -> str:
        """Get formatted ETA"""
        if not self.eta_seconds:
            return "Unknown"
        
        hours, remainder = divmod(self.eta_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
```

#### 3.3 Download Configuration Model
```python
class DownloadConfig(BaseModel):
    """Configuration for a download task"""
    preferred_quality: VideoQuality = Field(default=VideoQuality.HIGH_1080P, description="Preferred video quality")
    preferred_container: VideoContainer = Field(default=VideoContainer.MP4, description="Preferred container format")
    download_path: Path = Field(description="Download directory path")
    create_thumbnail: bool = Field(default=True, description="Whether to download thumbnail")
    create_info_json: bool = Field(default=False, description="Whether to create info JSON file")
    audio_only: bool = Field(default=False, description="Whether to download audio only")
    max_retries: int = Field(default=3, ge=0, le=10, description="Maximum retry attempts")
    timeout_seconds: int = Field(default=30, ge=5, le=300, description="Connection timeout in seconds")
    rate_limit: Optional[str] = Field(default=None, description="Download rate limit (e.g., '1M')")
    
    @validator('download_path')
    def validate_download_path(cls, v):
        """Validate download path"""
        if not isinstance(v, Path):
            v = Path(v)
        
        # Ensure path is absolute
        if not v.is_absolute():
            raise ValueError("Download path must be absolute")
        
        return v
```

#### 3.4 Download Task Model
```python
class DownloadTask(BaseEntity):
    """Represents a download task"""
    video_info: VideoInfo = Field(description="Video information")
    selected_format: Optional[VideoFormat] = Field(default=None, description="Selected format")
    config: DownloadConfig = Field(description="Download configuration")
    status: DownloadStatus = Field(default=DownloadStatus.QUEUED, description="Current status")
    progress: ProgressInfo = Field(default_factory=ProgressInfo, description="Download progress")
    priority: Priority = Field(default=Priority.NORMAL, description="Task priority")
    output_filename: Optional[str] = Field(default=None, description="Output filename")
    output_path: Optional[Path] = Field(default=None, description="Complete output path")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    retry_count: int = Field(default=0, ge=0, description="Current retry count")
    
    @root_validator
    def set_output_path(cls, values):
        """Set output path based on filename and download path"""
        video_info = values.get('video_info')
        config = values.get('config')
        output_filename = values.get('output_filename')
        
        if video_info and config and not output_filename:
            # Generate safe filename from video title
            safe_filename = video_info.get_safe_filename()
            
            # Add appropriate extension
            selected_format = values.get('selected_format')
            if selected_format and selected_format.container:
                extension = selected_format.container.value
            elif config.audio_only:
                extension = "mp3"
            else:
                extension = "mp4"
            
            values['output_filename'] = f"{safe_filename}.{extension}"
        
        if config and values.get('output_filename'):
            values['output_path'] = config.download_path / values['output_filename']
        
        return values
    
    def update_progress(self, bytes_downloaded: int, total_bytes: Optional[int] = None, 
                       speed_bps: Optional[float] = None):
        """Update download progress"""
        now = datetime.now()
        
        # Calculate speed if not provided
        if speed_bps is None and self.progress.last_updated:
            time_diff = (now - self.progress.last_updated).total_seconds()
            if time_diff > 0:
                bytes_diff = bytes_downloaded - self.progress.bytes_downloaded
                speed_bps = bytes_diff / time_diff
        
        # Create speed object if we have speed data
        speed = DownloadSpeed(bytes_per_second=speed_bps) if speed_bps is not None else self.progress.speed
        
        # Calculate ETA
        eta_seconds = None
        if total_bytes and speed and speed.bytes_per_second > 0:
            remaining_bytes = total_bytes - bytes_downloaded
            eta_seconds = int(remaining_bytes / speed.bytes_per_second)
        
        # Update progress
        self.progress = ProgressInfo(
            bytes_downloaded=bytes_downloaded,
            total_bytes=total_bytes or self.progress.total_bytes,
            speed=speed,
            eta_seconds=eta_seconds,
            started_at=self.progress.started_at,
            last_updated=now
        )
        
        # Update status if needed
        if self.status == DownloadStatus.QUEUED and bytes_downloaded > 0:
            self.status = DownloadStatus.DOWNLOADING
        
        # Update entity timestamp
        self.update_timestamp()
    
    def complete(self):
        """Mark download as complete"""
        self.status = DownloadStatus.COMPLETED
        if self.progress.total_bytes:
            self.progress.bytes_downloaded = self.progress.total_bytes
            self.progress.percent_complete = 100.0
        self.progress.eta_seconds = 0
        self.progress.last_updated = datetime.now()
        self.update_timestamp()
    
    def fail(self, error_message: str):
        """Mark download as failed"""
        self.status = DownloadStatus.FAILED
        self.error_message = error_message
        self.progress.last_updated = datetime.now()
        self.update_timestamp()
    
    def pause(self):
        """Pause the download"""
        if self.status == DownloadStatus.DOWNLOADING:
            self.status = DownloadStatus.PAUSED
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False
    
    def resume(self):
        """Resume the download"""
        if self.status == DownloadStatus.PAUSED:
            self.status = DownloadStatus.DOWNLOADING
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False
    
    def cancel(self):
        """Cancel the download"""
        if self.status in [DownloadStatus.QUEUED, DownloadStatus.PREPARING, 
                          DownloadStatus.DOWNLOADING, DownloadStatus.PAUSED]:
            self.status = DownloadStatus.CANCELLED
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False
    
    def retry(self):
        """Retry the download"""
        if self.status == DownloadStatus.FAILED and self.retry_count < self.config.max_retries:
            self.status = DownloadStatus.QUEUED
            self.retry_count += 1
            self.error_message = None
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False
```

### 4. Domain Exceptions (`src/core/domain/exceptions/domain_exceptions.py`)

```python
class DomainException(Exception):
    """Base exception for domain errors"""
    def __init__(self, message: str, code: str = "domain_error"):
        self.message = message
        self.code = code
        super().__init__(message)

class ValidationException(DomainException):
    """Exception for validation errors"""
    def __init__(self, message: str, field: str = None):
        code = "validation_error"
        self.field = field
        super().__init__(message, code)

class VideoInfoException(DomainException):
    """Exception for video info errors"""
    def __init__(self, message: str, video_id: str = None):
        code = "video_info_error"
        self.video_id = video_id
        super().__init__(message, code)

class DownloadException(DomainException):
    """Exception for download errors"""
    def __init__(self, message: str, task_id: str = None):
        code = "download_error"
        self.task_id = task_id
        super().__init__(message, code)

class FileSystemException(DomainException):
    """Exception for file system errors"""
    def __init__(self, message: str, path: str = None):
        code = "filesystem_error"
        self.path = path
        super().__init__(message, code)
```

### 5. Module Exports

#### 5.1 Base Models (`src/core/domain/models/__init__.py`)
```python
from .base import (
    Priority, Status, SizeInfo, ErrorInfo, Metadata, BaseEntity, WindowsPathMixin
)
from .video import (
    VideoQuality, VideoCodec, AudioCodec, VideoContainer,
    VideoFormat, VideoInfo
)
from .download import (
    DownloadStatus, DownloadSpeed, ProgressInfo, DownloadConfig, DownloadTask
)

__all__ = [
    'Priority', 'Status', 'SizeInfo', 'ErrorInfo', 'Metadata', 'BaseEntity', 'WindowsPathMixin',
    'VideoQuality', 'VideoCodec', 'AudioCodec', 'VideoContainer', 'VideoFormat', 'VideoInfo',
    'DownloadStatus', 'DownloadSpeed', 'ProgressInfo', 'DownloadConfig', 'DownloadTask'
]
```

#### 5.2 Domain Exceptions (`src/core/domain/exceptions/__init__.py`)
```python
from .domain_exceptions import (
    DomainException, ValidationException, VideoInfoException,
    DownloadException, FileSystemException
)

__all__ = [
    'DomainException', 'ValidationException', 'VideoInfoException',
    'DownloadException', 'FileSystemException'
]
```

#### 5.3 Domain Module (`src/core/domain/__init__.py`)
```python
from .models import *
from .exceptions import *

__all__ = [
    # Re-export from models
    'Priority', 'Status', 'SizeInfo', 'ErrorInfo', 'Metadata', 'BaseEntity', 'WindowsPathMixin',
    'VideoQuality', 'VideoCodec', 'AudioCodec', 'VideoContainer', 'VideoFormat', 'VideoInfo',
    'DownloadStatus', 'DownloadSpeed', 'ProgressInfo', 'DownloadConfig', 'DownloadTask',
    
    # Re-export from exceptions
    'DomainException', 'ValidationException', 'VideoInfoException',
    'DownloadException', 'FileSystemException'
]
```

#### 5.4 Core Module (`src/core/__init__.py`)
```python
from .domain import *

__all__ = [
    # Re-export from domain
    'Priority', 'Status', 'SizeInfo', 'ErrorInfo', 'Metadata', 'BaseEntity', 'WindowsPathMixin',
    'VideoQuality', 'VideoCodec', 'AudioCodec', 'VideoContainer', 'VideoFormat', 'VideoInfo',
    'DownloadStatus', 'DownloadSpeed', 'ProgressInfo', 'DownloadConfig', 'DownloadTask',
    'DomainException', 'ValidationException', 'VideoInfoException',
    'DownloadException', 'FileSystemException'
]
```

## Testing Strategy

### 1. Unit Tests

#### 1.1 Base Models Tests (`tests/test_core/test_domain/test_base.py`)
- Test enum values
- Test value objects (SizeInfo, ErrorInfo, Metadata)
- Test BaseEntity functionality
- Test Windows path utilities

#### 1.2 Video Models Tests (`tests/test_core/test_domain/test_video.py`)
- Test video enumerations
- Test VideoFormat model
- Test VideoInfo model and validators
- Test format selection logic

#### 1.3 Download Models Tests (`tests/test_core/test_domain/test_download.py`)
- Test download enumerations
- Test ProgressInfo model
- Test DownloadConfig model and validators
- Test DownloadTask model and state transitions

### 2. Integration Tests
- Test serialization and deserialization
- Test model interactions
- Test Windows path handling with real file system

## Success Criteria

- [ ] All domain models implemented
- [ ] Business logic encapsulated in models
- [ ] Windows-optimized utilities implemented
- [ ] Unit test coverage > 90%
- [ ] Documentation complete
- [ ] Code quality checks passing

## Next Phase Preparation

Phase 2 completion will enable:
- Phase 3: Service Layer with Enhanced Validation

## Risk Mitigation

### Technical Risks
- **Complex domain logic**: Implement comprehensive unit tests
- **Windows path handling**: Test on Windows with long paths and special characters
- **Serialization issues**: Implement custom serializers and deserializers

### Performance Risks
- **Large model instances**: Optimize memory usage
- **Validation overhead**: Implement lazy validation where appropriate
- **File operations**: Implement streaming and chunked processing

## Timeline Estimate

- **Week 1**: Base models and video models
- **Week 2**: Download models and domain exceptions
- **Week 3**: Testing and documentation

This implementation plan provides a comprehensive roadmap for Phase 2, ensuring robust domain models that form the foundation of the YouTube Downloader V3 application with type safety, validation, and Windows optimization.