#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for path manager.
"""

import unittest
import os
import platform
import tempfile
import shutil
from pathlib import Path

from src.config.paths import PathManager


class TestPathManager(unittest.TestCase):
    """Test PathManager class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a new instance for each test
        self.path_manager = PathManager()
        
        # Reset the singleton instance
        PathManager._instance = None
        PathManager._instance = self.path_manager
        
        # Create a temporary directory for testing
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create a test file
        self.test_file = self.temp_dir / "test_file.txt"
        with open(self.test_file, 'w', encoding='utf-8') as f:
            f.write("Test content")
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove temporary directory and its contents
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_singleton_behavior(self):
        """Test that PathManager is a singleton."""
        manager1 = PathManager()
        manager2 = PathManager()
        self.assertIs(manager1, manager2)
    
    def test_os_detection(self):
        """Test OS detection methods."""
        current_os = platform.system().lower()
        
        if current_os == "windows":
            self.assertTrue(self.path_manager.is_windows())
            self.assertFalse(self.path_manager.is_macos())
            self.assertFalse(self.path_manager.is_linux())
        elif current_os == "darwin":
            self.assertFalse(self.path_manager.is_windows())
            self.assertTrue(self.path_manager.is_macos())
            self.assertFalse(self.path_manager.is_linux())
        elif current_os == "linux":
            self.assertFalse(self.path_manager.is_windows())
            self.assertFalse(self.path_manager.is_macos())
            self.assertTrue(self.path_manager.is_linux())
    
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        # Test invalid characters
        invalid_chars = "<>:\"/\\|?*"
        for char in invalid_chars:
            sanitized = self.path_manager.sanitize_filename(f"test{char}file")
            self.assertNotIn(char, sanitized)
        
        # Test reserved Windows names
        for reserved in ["CON", "PRN", "AUX", "NUL", "COM1", "LPT1"]:
            sanitized = self.path_manager.sanitize_filename(reserved)
            self.assertNotEqual(sanitized.upper(), reserved)
        
        # Test length limit
        long_name = "a" * 300
        sanitized = self.path_manager.sanitize_filename(long_name)
        self.assertLessEqual(len(sanitized), 255)
        
        # Test valid filename
        valid_name = "valid-filename_123.txt"
        sanitized = self.path_manager.sanitize_filename(valid_name)
        self.assertEqual(sanitized, valid_name)
    
    def test_ensure_directory(self):
        """Test directory creation."""
        # Test creating a new directory
        new_dir = self.temp_dir / "new_directory"
        result = self.path_manager.ensure_directory(new_dir)
        self.assertTrue(result)
        self.assertTrue(new_dir.exists())
        self.assertTrue(new_dir.is_dir())
        
        # Test with existing directory
        result = self.path_manager.ensure_directory(new_dir)
        self.assertTrue(result)
        
        # Test with file path
        result = self.path_manager.ensure_directory(self.test_file)
        self.assertFalse(result)
    
    def test_is_path_writable(self):
        """Test path writability check."""
        # Test writable directory
        self.assertTrue(self.path_manager.is_path_writable(self.temp_dir))
        
        # Test writable file
        self.assertTrue(self.path_manager.is_path_writable(self.test_file))
        
        # Test non-existent path
        non_existent = self.temp_dir / "non_existent"
        self.assertFalse(self.path_manager.is_path_writable(non_existent))
        
        if os.name == 'posix' and os.geteuid() != 0:  # Skip if running as root
            # Test non-writable directory (only on non-Windows systems)
            if not self.path_manager.is_windows():
                try:
                    readonly_dir = self.temp_dir / "readonly"
                    readonly_dir.mkdir()
                    os.chmod(readonly_dir, 0o444)  # Read-only
                    self.assertFalse(self.path_manager.is_path_writable(readonly_dir))
                except (PermissionError, OSError):
                    pass  # Skip if we can't set permissions
    
    def test_get_free_space(self):
        """Test getting free space."""
        # Test with existing directory
        free_space = self.path_manager.get_free_space(self.temp_dir)
        self.assertIsInstance(free_space, int)
        self.assertGreater(free_space, 0)
        
        # Test with existing file (should return space on parent directory)
        file_space = self.path_manager.get_free_space(self.test_file)
        self.assertEqual(free_space, file_space)
        
        # Test with non-existent path
        with self.assertRaises(ValueError):
            self.path_manager.get_free_space(self.temp_dir / "non_existent")
    
    def test_temp_file_management(self):
        """Test temporary file creation and cleanup."""
        # Create a temporary file
        temp_file = self.path_manager.create_temp_file()
        self.assertTrue(temp_file.exists())
        
        # Check that it's in the list of temp files
        self.assertIn(temp_file, self.path_manager._temp_files)
        
        # Create another temp file with specific content
        content = "Test content"
        temp_file2 = self.path_manager.create_temp_file(content=content)
        self.assertTrue(temp_file2.exists())
        with open(temp_file2, 'r', encoding='utf-8') as f:
            self.assertEqual(f.read(), content)
        
        # Create a temp file with specific suffix
        suffix = ".json"
        temp_file3 = self.path_manager.create_temp_file(suffix=suffix)
        self.assertTrue(temp_file3.exists())
        self.assertTrue(temp_file3.name.endswith(suffix))
        
        # Clean up temp files
        self.path_manager.cleanup_temp_files()
        
        # Check that all temp files were removed
        self.assertFalse(temp_file.exists())
        self.assertFalse(temp_file2.exists())
        self.assertFalse(temp_file3.exists())
        self.assertEqual(len(self.path_manager._temp_files), 0)


if __name__ == "__main__":
    unittest.main()