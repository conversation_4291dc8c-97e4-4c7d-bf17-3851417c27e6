#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuration Example of YouTube Downloader

This script demonstrates how to manage configuration settings using the
YouTube Downloader services.
"""

import asyncio
import os
from pathlib import Path

from src.services.service_interfaces import VideoQuality
from src.services.service_factory import AsyncServiceFactory


async def main():
    """Example of managing configuration settings."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    config_service = await factory.create_config_service_async()
    file_service = await factory.create_file_service_async()
    
    # Load current configuration
    print("Loading current configuration...")
    config = await config_service.load_config()
    
    # Display current configuration
    print("\nCurrent configuration:")
    print(f"  Download directory: {config.download_dir}")
    print(f"  Maximum concurrent downloads: {config.max_concurrent_downloads}")
    print(f"  Default format: {config.default_format.name}")
    
    # Example: Update download directory
    new_download_dir = os.path.join(Path.home(), "Downloads", "YouTubeDownloader_Custom")
    print(f"\nUpdating download directory to: {new_download_dir}")
    
    # Validate and create directory
    try:
        await file_service.validate_path(new_download_dir)
        await file_service.create_directory(new_download_dir)
        await config_service.update_setting('download_dir', new_download_dir)
        print("Download directory updated successfully.")
    except Exception as e:
        print(f"Error updating download directory: {e}")
    
    # Example: Update maximum concurrent downloads
    new_max_concurrent = 5
    print(f"\nUpdating maximum concurrent downloads to: {new_max_concurrent}")
    
    try:
        await config_service.update_setting('max_concurrent_downloads', new_max_concurrent)
        print("Maximum concurrent downloads updated successfully.")
    except Exception as e:
        print(f"Error updating maximum concurrent downloads: {e}")
    
    # Example: Update default format
    new_format = VideoQuality.HIGH
    print(f"\nUpdating default format to: {new_format.name}")
    
    try:
        await config_service.update_setting('default_format', new_format)
        print("Default format updated successfully.")
    except Exception as e:
        print(f"Error updating default format: {e}")
    
    # Load updated configuration
    print("\nLoading updated configuration...")
    updated_config = await config_service.load_config()
    
    # Display updated configuration
    print("\nUpdated configuration:")
    print(f"  Download directory: {updated_config.download_dir}")
    print(f"  Maximum concurrent downloads: {updated_config.max_concurrent_downloads}")
    print(f"  Default format: {updated_config.default_format.name}")
    
    # Example: Reset to defaults
    print("\nResetting configuration to defaults...")
    
    try:
        await config_service.reset_to_defaults()
        print("Configuration reset to defaults successfully.")
    except Exception as e:
        print(f"Error resetting configuration: {e}")
    
    # Load default configuration
    print("\nLoading default configuration...")
    default_config = await config_service.load_config()
    
    # Display default configuration
    print("\nDefault configuration:")
    print(f"  Download directory: {default_config.download_dir}")
    print(f"  Maximum concurrent downloads: {default_config.max_concurrent_downloads}")
    print(f"  Default format: {default_config.default_format.name}")
    
    # Example: Get individual settings
    print("\nGetting individual settings:")
    
    try:
        download_dir = await config_service.get_setting('download_dir')
        max_concurrent = await config_service.get_setting('max_concurrent_downloads')
        default_format = await config_service.get_setting('default_format')
        
        print(f"  Download directory: {download_dir}")
        print(f"  Maximum concurrent downloads: {max_concurrent}")
        print(f"  Default format: {default_format.name}")
    except Exception as e:
        print(f"Error getting settings: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())