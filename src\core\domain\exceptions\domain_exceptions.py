class DomainException(Exception):
    """Base exception for domain errors"""
    def __init__(self, message: str, code: str = "domain_error"):
        self.message = message
        self.code = code
        super().__init__(message)

class ValidationException(DomainException):
    """Exception for validation errors"""
    def __init__(self, message: str, field: str = None):
        code = "validation_error"
        self.field = field
        super().__init__(message, code)

class VideoInfoException(DomainException):
    """Exception for video info errors"""
    def __init__(self, message: str, video_id: str = None):
        code = "video_info_error"
        self.video_id = video_id
        super().__init__(message, code)

class DownloadException(DomainException):
    """Exception for download errors"""
    def __init__(self, message: str, task_id: str = None):
        code = "download_error"
        self.task_id = task_id
        super().__init__(message, code)

class FileSystemException(DomainException):
    """Exception for file system errors"""
    def __init__(self, message: str, path: str = None):
        code = "filesystem_error"
        self.path = path
        super().__init__(message, code)