#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Error Handling Example of YouTube Downloader

This script demonstrates how to handle various errors and exceptions
that may occur when using the YouTube Downloader services.
"""

import asyncio
import os
from pathlib import Path
import sys

from src.services.service_interfaces import VideoQuality, DownloadStatus, DownloadTask
from src.services.service_factory import AsyncServiceFactory


async def main():
    """Example of handling errors and exceptions in YouTube Downloader."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    youtube_service = await factory.create_youtube_service_async()
    download_service = await factory.create_download_service_async()
    file_service = await factory.create_file_service_async()
    config_service = await factory.create_config_service_async()
    
    # Example 1: Handle invalid URL
    print("\nExample 1: Handling invalid URL")
    invalid_url = "https://www.youtube.com/invalid"
    
    try:
        validation_result = await youtube_service.validate_url(invalid_url)
        if not validation_result.is_valid:
            print(f"URL validation failed: {validation_result.message}")
        else:
            print(f"URL is valid: {invalid_url}")
    except Exception as e:
        print(f"Error validating URL: {e}")
    
    # Example 2: Handle non-existent video
    print("\nExample 2: Handling non-existent video")
    non_existent_video = "https://www.youtube.com/watch?v=non_existent_video_id"
    
    try:
        validation_result = await youtube_service.validate_url(non_existent_video)
        if validation_result.is_valid:
            video_info = await youtube_service.extract_video_info(non_existent_video)
            if video_info:
                print(f"Video info: {video_info.title}")
            else:
                print("Failed to extract video info (video may not exist)")
        else:
            print(f"URL validation failed: {validation_result.message}")
    except Exception as e:
        print(f"Error extracting video info: {e}")
    
    # Example 3: Handle invalid download directory
    print("\nExample 3: Handling invalid download directory")
    invalid_dir = "Z:\\non\\existent\\directory"
    
    try:
        await file_service.validate_path(invalid_dir)
        await file_service.create_directory(invalid_dir)
        print(f"Created directory: {invalid_dir}")
    except Exception as e:
        print(f"Error creating directory: {e}")
        
        # Try with a valid directory instead
        valid_dir = Path.home() / "Downloads" / "YouTubeDownloader"
        print(f"Trying with valid directory: {valid_dir}")
        try:
            await file_service.validate_path(str(valid_dir))
            await file_service.create_directory(str(valid_dir))
            print(f"Created directory: {valid_dir}")
        except Exception as e:
            print(f"Error creating directory: {e}")
    
    # Example 4: Handle invalid configuration
    print("\nExample 4: Handling invalid configuration")
    
    try:
        # Try to update with invalid value
        await config_service.update_setting('max_concurrent_downloads', -5)
        print("Updated max_concurrent_downloads to -5")
    except Exception as e:
        print(f"Error updating configuration: {e}")
        
        # Try with valid value
        try:
            await config_service.update_setting('max_concurrent_downloads', 5)
            print("Updated max_concurrent_downloads to 5")
        except Exception as e:
            print(f"Error updating configuration: {e}")
    
    # Example 5: Handle download errors
    print("\nExample 5: Handling download errors")
    
    # Load configuration
    config = await config_service.load_config()
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    
    # Try to download with invalid URL
    invalid_url = "https://www.youtube.com/watch?v=invalid_id"
    
    try:
        # Create download task with invalid URL
        task = DownloadTask(
            url=invalid_url,
            destination=os.path.join(config.download_dir, "invalid_video.mp4"),
            quality=VideoQuality.MEDIUM
        )
        
        # Start download
        task_id = await download_service.start_download(task)
        print(f"Started download with task ID: {task_id}")
        
        # Wait for a moment to let the download start and fail
        await asyncio.sleep(2)
        
        # Check task status
        task = await download_service.get_task(task_id)
        if task and task.progress:
            print(f"Task status: {task.progress.status.name}")
            if task.progress.error_message:
                print(f"Error message: {task.progress.error_message}")
        else:
            print("Task not found or no progress information")
            
    except Exception as e:
        print(f"Error starting download: {e}")
    
    # Example 6: Handle task not found
    print("\nExample 6: Handling task not found")
    
    non_existent_task_id = "non_existent_task_id"
    
    try:
        task = await download_service.get_task(non_existent_task_id)
        if task:
            print(f"Task found: {task.task_id}")
        else:
            print(f"Task not found: {non_existent_task_id}")
    except Exception as e:
        print(f"Error getting task: {e}")
    
    # Example 7: Handle cancellation of non-existent task
    print("\nExample 7: Handling cancellation of non-existent task")
    
    try:
        await download_service.cancel_download(non_existent_task_id)
        print(f"Cancelled task: {non_existent_task_id}")
    except Exception as e:
        print(f"Error cancelling task: {e}")


if __name__ == "__main__":
    try:
        # Run the async main function
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\nUnhandled error: {e}")
        sys.exit(1)