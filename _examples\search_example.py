#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Search Example of YouTube Downloader

This script demonstrates how to search for YouTube videos using the
YouTube Downloader services.
"""

import asyncio
import os
from pathlib import Path
from typing import List

from src.services.service_interfaces import VideoInfo, VideoQuality, DownloadTask
from src.services.service_factory import AsyncServiceFactory


async def main():
    """Example of searching for YouTube videos."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    youtube_service = await factory.create_youtube_service_async()
    download_service = await factory.create_download_service_async()
    file_service = await factory.create_file_service_async()
    config_service = await factory.create_config_service_async()
    
    # Load configuration
    config = await config_service.load_config()
    print(f"Download directory: {config.download_dir}")
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    
    # Example search query
    search_query = "Python programming tutorial short"
    max_results = 5
    
    print(f"Searching for: '{search_query}'...")
    search_results = await youtube_service.search_videos(search_query, max_results)
    
    if not search_results:
        print("No results found.")
        return
    
    print(f"Found {len(search_results)} results:")
    print("-" * 80)
    
    # Display search results
    for i, video in enumerate(search_results):
        print(f"{i+1}. {video.title}")
        print(f"   URL: {video.url}")
        print(f"   Duration: {format_duration(video.duration)}")
        print(f"   Channel: {video.channel}")
        print("-" * 80)
    
    # Ask user to select a video to download
    selected_index = await ask_user_selection(len(search_results))
    if selected_index is None:
        print("No video selected. Exiting.")
        return
    
    selected_video = search_results[selected_index]
    print(f"\nSelected video: {selected_video.title}")
    
    # Ask user to select quality
    quality = await ask_user_quality()
    if quality is None:
        print("No quality selected. Exiting.")
        return
    
    # Create safe filename
    filename = create_safe_filename(selected_video.title) + ".mp4"
    destination = Path(config.download_dir) / filename
    
    # Create download task
    task = DownloadTask(
        url=selected_video.url,
        destination=str(destination),
        quality=quality,
        video_info=selected_video
    )
    
    # Start download
    task_id = await download_service.start_download(task)
    print(f"Started download with task ID: {task_id}")
    
    # Monitor progress
    await monitor_download_progress(download_service, task_id)


def create_safe_filename(title: str) -> str:
    """Create a safe filename from a title.
    
    Args:
        title: The title to convert to a safe filename
        
    Returns:
        A safe filename
    """
    # Remove invalid characters
    safe_title = ''.join(c for c in title if c.isalnum() or c in ' -_.')
    
    # Replace spaces with underscores
    safe_title = safe_title.replace(' ', '_')
    
    # Limit length
    if len(safe_title) > 100:
        safe_title = safe_title[:100]
        
    return safe_title


def format_duration(seconds: int) -> str:
    """Format duration in seconds to a human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds}s"
    
    minutes = seconds // 60
    remaining_seconds = seconds % 60
    
    if minutes < 60:
        return f"{minutes}m {remaining_seconds}s"
    
    hours = minutes // 60
    remaining_minutes = minutes % 60
    
    return f"{hours}h {remaining_minutes}m {remaining_seconds}s"


async def ask_user_selection(max_index: int) -> int:
    """Ask user to select a video from the search results.
    
    Args:
        max_index: Maximum index of search results
        
    Returns:
        Selected index (0-based) or None if invalid
    """
    while True:
        try:
            selection = input(f"\nEnter number to download (1-{max_index}) or 'q' to quit: ")
            
            if selection.lower() == 'q':
                return None
            
            index = int(selection) - 1
            if 0 <= index < max_index:
                return index
            else:
                print(f"Please enter a number between 1 and {max_index}.")
        except ValueError:
            print("Please enter a valid number.")


async def ask_user_quality() -> VideoQuality:
    """Ask user to select a video quality.
    
    Returns:
        Selected VideoQuality or None if invalid
    """
    quality_options = {
        '1': (VideoQuality.LOW, "Low (360p)"),
        '2': (VideoQuality.MEDIUM, "Medium (720p)"),
        '3': (VideoQuality.HIGH, "High (1080p)"),
        '4': (VideoQuality.VERY_HIGH, "Very High (1440p+)")
    }
    
    print("\nSelect quality:")
    for key, (_, description) in quality_options.items():
        print(f"{key}. {description}")
    
    while True:
        selection = input("Enter quality number or 'q' to quit: ")
        
        if selection.lower() == 'q':
            return None
        
        if selection in quality_options:
            return quality_options[selection][0]
        else:
            print(f"Please enter a number between 1 and {len(quality_options)}.")


async def monitor_download_progress(download_service, task_id: str):
    """Monitor download progress for a task.
    
    Args:
        download_service: Download service instance
        task_id: Download task ID
    """
    try:
        # Monitor progress until task is complete, failed, or canceled
        while True:
            # Get task
            task = await download_service.get_task(task_id)
            if not task:
                print(f"Task {task_id} not found.")
                return
            
            progress = task.progress
            if not progress:
                print("No progress information.")
                await asyncio.sleep(1)
                continue
            
            # Clear line
            print('\r' + ' ' * 80, end='\r')
            
            # Print progress
            if progress.status == progress.status.DOWNLOADING:
                # Calculate speed in MB/s
                speed_mb = progress.speed / (1024 * 1024)
                
                # Format ETA
                eta_min = progress.eta // 60
                eta_sec = progress.eta % 60
                eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                
                # Print progress bar
                bar_length = 30
                filled_length = int(bar_length * progress.progress_percentage / 100)
                bar = '█' * filled_length + '░' * (bar_length - filled_length)
                
                print(f"\r[{bar}] {progress.progress_percentage:.1f}% - {speed_mb:.2f} MB/s - ETA: {eta_text}", end='')
            else:
                # Print status for non-downloading states
                print(f"\rStatus: {progress.status.name}", end='')
            
            # Check if download is complete, failed, or canceled
            if progress.status in [progress.status.COMPLETED, progress.status.FAILED, progress.status.CANCELED]:
                print()  # New line
                print(f"Download {progress.status.name.lower()}.")
                
                if progress.status == progress.status.COMPLETED:
                    print(f"File saved to: {task.destination}")
                elif progress.status == progress.status.FAILED and progress.error_message:
                    print(f"Error: {progress.error_message}")
                
                return
            
            # Wait before checking again
            await asyncio.sleep(0.5)
            
    except KeyboardInterrupt:
        # Cancel download if user presses Ctrl+C
        print("\nCanceling download...")
        try:
            await download_service.cancel_download(task_id)
            print("Download canceled.")
        except Exception as e:
            print(f"Error canceling download: {e}")
        
    except Exception as e:
        print(f"\nError monitoring download: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())