#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Exception Hierarchy

This module defines a comprehensive exception hierarchy for all service-related errors
in the YouTube Downloader application. Each service has its own specific exceptions
that inherit from the base ServiceException class.
"""

from typing import Optional, Dict, Any
from datetime import datetime


class ServiceException(Exception):
    """Base exception for all service-related errors."""
    
    def __init__(self, message: str, code: str = "service_error", 
                 details: Optional[Dict[str, Any]] = None):
        """Initialize service exception.
        
        Args:
            message: Human-readable error message
            code: Error code for programmatic handling
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.code = code
        self.details = details or {}
        self.timestamp = datetime.now()
    
    def __str__(self) -> str:
        return f"[{self.code}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for serialization."""
        return {
            'type': self.__class__.__name__,
            'message': self.message,
            'code': self.code,
            'details': self.details,
            'timestamp': self.timestamp.isoformat()
        }


class ServiceValidationException(ServiceException):
    """Exception for service validation errors."""
    
    def __init__(self, message: str, field: Optional[str] = None, 
                 value: Optional[Any] = None):
        super().__init__(message, "validation_error")
        self.field = field
        self.value = value
        if field:
            self.details['field'] = field
        if value is not None:
            self.details['value'] = str(value)


class ServiceConfigurationException(ServiceException):
    """Exception for service configuration errors."""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(message, "configuration_error")
        self.config_key = config_key
        if config_key:
            self.details['config_key'] = config_key


class ServiceConnectionException(ServiceException):
    """Exception for service connection errors."""
    
    def __init__(self, message: str, url: Optional[str] = None, 
                 status_code: Optional[int] = None):
        super().__init__(message, "connection_error")
        self.url = url
        self.status_code = status_code
        if url:
            self.details['url'] = url
        if status_code:
            self.details['status_code'] = status_code


class ServiceTimeoutException(ServiceException):
    """Exception for service timeout errors."""
    
    def __init__(self, message: str, timeout_seconds: Optional[float] = None):
        super().__init__(message, "timeout_error")
        self.timeout_seconds = timeout_seconds
        if timeout_seconds:
            self.details['timeout_seconds'] = timeout_seconds


# YouTube Service Exceptions

class YouTubeServiceException(ServiceException):
    """Base exception for YouTube service errors."""
    
    def __init__(self, message: str, code: str = "youtube_error", 
                 video_id: Optional[str] = None):
        super().__init__(message, code)
        self.video_id = video_id
        if video_id:
            self.details['video_id'] = video_id


class VideoExtractionException(YouTubeServiceException):
    """Exception for video information extraction errors."""
    
    def __init__(self, message: str, video_id: Optional[str] = None, 
                 url: Optional[str] = None):
        super().__init__(message, "video_extraction_error", video_id)
        self.url = url
        if url:
            self.details['url'] = url


class PlaylistExtractionException(YouTubeServiceException):
    """Exception for playlist information extraction errors."""
    
    def __init__(self, message: str, playlist_id: Optional[str] = None, 
                 url: Optional[str] = None):
        super().__init__(message, "playlist_extraction_error")
        self.playlist_id = playlist_id
        self.url = url
        if playlist_id:
            self.details['playlist_id'] = playlist_id
        if url:
            self.details['url'] = url


# Download Service Exceptions

class DownloadServiceException(ServiceException):
    """Base exception for download service errors."""
    
    def __init__(self, message: str, code: str = "download_error", 
                 task_id: Optional[str] = None):
        super().__init__(message, code)
        self.task_id = task_id
        if task_id:
            self.details['task_id'] = task_id


class TaskNotFoundException(DownloadServiceException):
    """Exception for when a download task is not found."""
    
    def __init__(self, task_id: str):
        super().__init__(f"Download task not found: {task_id}", 
                        "task_not_found", task_id)


class InvalidTaskStateException(DownloadServiceException):
    """Exception for invalid task state operations."""
    
    def __init__(self, message: str, task_id: Optional[str] = None, 
                 current_state: Optional[str] = None, 
                 required_state: Optional[str] = None):
        super().__init__(message, "invalid_task_state", task_id)
        self.current_state = current_state
        self.required_state = required_state
        if current_state:
            self.details['current_state'] = current_state
        if required_state:
            self.details['required_state'] = required_state


class DownloadFailedException(DownloadServiceException):
    """Exception for download failures."""
    
    def __init__(self, message: str, task_id: Optional[str] = None, 
                 reason: Optional[str] = None):
        super().__init__(message, "download_failed", task_id)
        self.reason = reason
        if reason:
            self.details['reason'] = reason


# File Service Exceptions

class FileServiceException(ServiceException):
    """Base exception for file service errors."""
    
    def __init__(self, message: str, code: str = "file_error", 
                 file_path: Optional[str] = None):
        super().__init__(message, code)
        self.file_path = file_path
        if file_path:
            self.details['file_path'] = file_path


class FileNotFoundException(FileServiceException):
    """Exception for file not found errors."""
    
    def __init__(self, file_path: str):
        super().__init__(f"File not found: {file_path}", 
                        "file_not_found", file_path)


class FilePermissionException(FileServiceException):
    """Exception for file permission errors."""
    
    def __init__(self, message: str, file_path: Optional[str] = None, 
                 operation: Optional[str] = None):
        super().__init__(message, "file_permission_error", file_path)
        self.operation = operation
        if operation:
            self.details['operation'] = operation


class DiskSpaceException(FileServiceException):
    """Exception for insufficient disk space errors."""
    
    def __init__(self, message: str, required_space: Optional[int] = None, 
                 available_space: Optional[int] = None):
        super().__init__(message, "disk_space_error")
        self.required_space = required_space
        self.available_space = available_space
        if required_space:
            self.details['required_space'] = required_space
        if available_space:
            self.details['available_space'] = available_space


# Config Service Exceptions

class ConfigServiceException(ServiceException):
    """Base exception for configuration service errors."""
    
    def __init__(self, message: str, code: str = "config_error", 
                 config_file: Optional[str] = None):
        super().__init__(message, code)
        self.config_file = config_file
        if config_file:
            self.details['config_file'] = config_file


class ConfigNotFoundException(ConfigServiceException):
    """Exception for configuration file not found errors."""
    
    def __init__(self, config_file: str):
        super().__init__(f"Configuration file not found: {config_file}", 
                        "config_not_found", config_file)


class ConfigValidationException(ConfigServiceException):
    """Exception for configuration validation errors."""
    
    def __init__(self, message: str, config_file: Optional[str] = None, 
                 validation_errors: Optional[list] = None):
        super().__init__(message, "config_validation_error", config_file)
        self.validation_errors = validation_errors or []
        if validation_errors:
            self.details['validation_errors'] = validation_errors


# Logging Service Exceptions

class LoggingServiceException(ServiceException):
    """Exception for logging service errors."""
    
    def __init__(self, message: str, logger_name: Optional[str] = None):
        super().__init__(message, "logging_error")
        self.logger_name = logger_name
        if logger_name:
            self.details['logger_name'] = logger_name
