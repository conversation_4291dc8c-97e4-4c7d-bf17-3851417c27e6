# Configuration System

## Overview

This package provides a robust configuration system for the YouTube Downloader application. It includes:

- Core enumerations for application settings
- Pydantic models for configuration validation
- A settings manager for loading, saving, and accessing configuration
- A path manager for cross-platform path operations

## Components

### Core Enumerations (`base.py`)

- `LogLevel`: Enum for log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `DownloadQuality`: Enum for video quality options (LOW, MEDIUM, HIGH, VERY_HIGH)
- `VideoFormat`: Enum for video format options (MP4, MKV, AVI, etc.)
- `AudioFormat`: Enum for audio format options (MP3, AAC, WAV, etc.)

### Configuration Models (`base.py`)

- `AppConfig`: Application-wide settings
- `PathConfig`: Path-related settings
- `DownloadConfig`: Download-related settings
- `BaseConfiguration`: Root configuration model

### Factory Functions (`base.py`)

- `create_development_config()`: Configuration for development
- `create_production_config()`: Configuration for production
- `create_testing_config()`: Configuration for testing

### SettingsManager (`settings.py`)

- Singleton class for managing application configuration
- Methods for loading/saving configuration from/to JSON files
- Support for getting/setting/updating configuration values using dot notation
- Change notification system with callback registration

### PathManager (`paths.py`)

- Singleton class for cross-platform path operations
- OS detection methods
- Filename sanitization
- Directory management
- Temporary file management

## Usage Examples

### Basic Configuration

```python
from src.config import settings_manager, LogLevel

# Initialize with config file
settings_manager.initialize("config.json")

# Get a configuration value
log_level = settings_manager.get_setting("app.log_level")
print(f"Current log level: {log_level}")

# Update a configuration value
settings_manager.update_setting("app.log_level", LogLevel.DEBUG)

# Save configuration
settings_manager.save_to_file("config.json")
```

### Path Operations

```python
from src.config import path_manager
from pathlib import Path

# Check operating system
if path_manager.is_windows():
    print("Running on Windows")
elif path_manager.is_macos():
    print("Running on macOS")
elif path_manager.is_linux():
    print("Running on Linux")

# Sanitize a filename
filename = path_manager.sanitize_filename("Invalid<>:*?|\\filename")
print(f"Sanitized filename: {filename}")

# Ensure a directory exists
download_dir = Path("downloads")
path_manager.ensure_directory(download_dir)

# Create a temporary file
temp_file = path_manager.create_temp_file(suffix=".txt", content="Hello, world!")
print(f"Created temporary file: {temp_file}")

# Clean up temporary files
path_manager.cleanup_temp_files()
```

### Change Notifications

```python
from src.config import settings_manager, ConfigurationChangedEvent

# Define a callback function
def config_changed(event: ConfigurationChangedEvent):
    print(f"Configuration changed: {event.key}")
    print(f"Old value: {event.old_value}")
    print(f"New value: {event.new_value}")

# Register the callback
settings_manager.register_change_callback(config_changed)

# Update a setting (will trigger the callback)
settings_manager.update_setting("app.name", "New App Name")

# Unregister the callback
settings_manager.unregister_change_callback(config_changed)
```

## Testing

The configuration system includes comprehensive unit and integration tests:

- `tests/test_config/test_base.py`: Tests for enumerations, models, and factory functions
- `tests/test_config/test_settings.py`: Tests for SettingsManager functionality
- `tests/test_config/test_paths.py`: Tests for PathManager functionality
- `tests/test_config/test_integration.py`: Integration tests for the configuration system