#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Layer Usage Example

This example demonstrates how to use the enhanced service layer with validation,
error handling, and progress tracking.
"""

import asyncio
import sys
from pathlib import Path
from typing import List

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.services.service_factory import AsyncServiceFactory
from src.services.service_interfaces import DownloadTask, VideoQuality, DownloadStatus
from src.services.exceptions import (
    YouTubeServiceException, 
    DownloadServiceException,
    ServiceException
)
from src.services.utils.progress_tracker import ProgressTracker, ProgressEvent
from src.services.validation import ServiceValidationResult
from src.config import settings_manager


class ServiceLayerDemo:
    """Demonstrates service layer functionality."""
    
    def __init__(self):
        """Initialize the demo."""
        self.factory = AsyncServiceFactory()
        self.progress_tracker = ProgressTracker()
        self.setup_progress_callbacks()
    
    def setup_progress_callbacks(self):
        """Set up progress tracking callbacks."""
        def progress_callback(event: ProgressEvent):
            """Handle progress updates."""
            if event.status == "downloading":
                percent = (event.bytes_downloaded / event.total_bytes * 100) if event.total_bytes else 0
                speed_mb = event.speed_bps / (1024 * 1024) if event.speed_bps else 0
                print(f"Progress: {percent:.1f}% - Speed: {speed_mb:.2f} MB/s - ETA: {event.eta_seconds}s")
            else:
                print(f"Status: {event.status}")
        
        self.progress_tracker.register_callback(progress_callback)
    
    async def demonstrate_youtube_service(self):
        """Demonstrate YouTube service functionality."""
        print("\n=== YouTube Service Demo ===")
        
        try:
            youtube_service = await self.factory.create_youtube_service_async()
            
            # Example URLs
            video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            playlist_url = "https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9H1_KxjYbJjf4e2"
            
            # Validate URLs
            print("Validating URLs...")
            video_validation = await youtube_service.validate_url(video_url)
            print(f"Video URL valid: {video_validation.is_valid}")
            
            playlist_validation = await youtube_service.validate_url(playlist_url)
            print(f"Playlist URL valid: {playlist_validation.is_valid}")
            
            # Extract video information
            print("\nExtracting video information...")
            try:
                video_info = await youtube_service.extract_video_info(video_url)
                print(f"Title: {video_info.title}")
                print(f"Author: {video_info.author}")
                print(f"Duration: {video_info.duration} seconds")
                print(f"View count: {video_info.view_count}")
                
                # Get available formats
                formats = await youtube_service.get_available_formats(video_info.video_id)
                print(f"Available formats: {[f.name for f in formats]}")
                
                return video_info
                
            except YouTubeServiceException as e:
                print(f"YouTube service error: {e.message}")
                print(f"Error code: {e.code}")
                return None
                
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None
    
    async def demonstrate_download_service(self, video_info):
        """Demonstrate download service functionality."""
        print("\n=== Download Service Demo ===")
        
        if not video_info:
            print("No video info available for download demo")
            return
        
        try:
            download_service = await self.factory.create_download_service_async()
            file_service = await self.factory.create_file_service_async()
            
            # Create download directory
            download_dir = Path("./demo_downloads")
            await file_service.create_directory(download_dir)
            
            # Create download task
            destination = download_dir / f"{video_info.title[:50]}.mp4"
            task = DownloadTask(
                url=video_info.url,
                destination=destination,
                quality=VideoQuality.MEDIUM,
                video_info=video_info
            )
            
            print(f"Starting download to: {destination}")
            
            # Start download
            task_id = await download_service.start_download(task)
            print(f"Download started with task ID: {task_id}")
            
            # Monitor progress (simulate - in real usage this would be actual download)
            print("Monitoring download progress...")
            for i in range(10):
                # Simulate progress updates
                bytes_downloaded = (i + 1) * 1024 * 1024  # 1MB per iteration
                total_bytes = 10 * 1024 * 1024  # 10MB total
                
                self.progress_tracker.update_progress(
                    task_id, 
                    bytes_downloaded, 
                    total_bytes, 
                    "downloading"
                )
                
                await asyncio.sleep(0.5)  # Simulate time passing
            
            # Complete the download
            self.progress_tracker.update_progress(
                task_id, 
                total_bytes, 
                total_bytes, 
                "completed"
            )
            
            print("Download completed!")
            
            # Get final task status
            final_task = await download_service.get_task(task_id)
            if final_task:
                print(f"Final status: {final_task.status}")
            
        except DownloadServiceException as e:
            print(f"Download service error: {e.message}")
            print(f"Error code: {e.code}")
        except Exception as e:
            print(f"Unexpected error: {e}")
    
    async def demonstrate_validation_framework(self):
        """Demonstrate validation framework functionality."""
        print("\n=== Validation Framework Demo ===")
        
        from src.services.validation import (
            YouTubeServiceValidator,
            DownloadServiceValidator,
            TaskValidator,
            ConfigurationValidator
        )
        
        # YouTube URL validation
        print("Testing YouTube URL validation...")
        youtube_validator = YouTubeServiceValidator()
        
        valid_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        invalid_url = "https://example.com/not-youtube"
        
        valid_result = youtube_validator.validate_video_extraction(valid_url)
        invalid_result = youtube_validator.validate_video_extraction(invalid_url)
        
        print(f"Valid URL result: {valid_result.is_valid}")
        print(f"Invalid URL result: {invalid_result.is_valid}")
        if not invalid_result.is_valid:
            print(f"Validation errors: {invalid_result.get_error_messages()}")
        
        # Task validation
        print("\nTesting task validation...")
        task_validator = TaskValidator()
        
        valid_task_data = {
            'url': valid_url,
            'destination': './downloads/test.mp4',
            'quality': '720p'
        }
        
        invalid_task_data = {
            'url': '',  # Empty URL
            'quality': 'invalid_quality'  # Invalid quality
            # Missing destination
        }
        
        valid_task_result = task_validator.validate_task_creation(valid_task_data)
        invalid_task_result = task_validator.validate_task_creation(invalid_task_data)
        
        print(f"Valid task result: {valid_task_result.is_valid}")
        print(f"Invalid task result: {invalid_task_result.is_valid}")
        if not invalid_task_result.is_valid:
            print(f"Task validation errors: {invalid_task_result.get_error_messages()}")
        
        # Configuration validation
        print("\nTesting configuration validation...")
        config_validator = ConfigurationValidator()
        
        valid_config = {
            'download_dir': './downloads',
            'max_concurrent_downloads': 3,
            'default_format': '720p',
            'log_level': 'INFO'
        }
        
        invalid_config = {
            'download_dir': '',  # Empty directory
            'max_concurrent_downloads': 0,  # Invalid number
            'default_format': 'invalid_format',  # Invalid format
            'log_level': 'INVALID_LEVEL'  # Invalid log level
        }
        
        valid_config_result = config_validator.validate_app_config(valid_config)
        invalid_config_result = config_validator.validate_app_config(invalid_config)
        
        print(f"Valid config result: {valid_config_result.is_valid}")
        print(f"Invalid config result: {invalid_config_result.is_valid}")
        if not invalid_config_result.is_valid:
            print(f"Config validation errors: {invalid_config_result.get_error_messages()}")
    
    async def demonstrate_error_handling(self):
        """Demonstrate error handling across services."""
        print("\n=== Error Handling Demo ===")
        
        youtube_service = await self.factory.create_youtube_service_async()
        download_service = await self.factory.create_download_service_async()
        
        # Test various error scenarios
        error_scenarios = [
            ("Empty URL", ""),
            ("Invalid URL", "not-a-url"),
            ("Non-YouTube URL", "https://example.com"),
            ("Malformed YouTube URL", "https://youtube.com/invalid")
        ]
        
        for scenario_name, test_url in error_scenarios:
            print(f"\nTesting: {scenario_name}")
            try:
                await youtube_service.extract_video_info(test_url)
                print("  Unexpectedly succeeded")
            except YouTubeServiceException as e:
                print(f"  Caught YouTube service error: {e.message}")
            except ServiceException as e:
                print(f"  Caught general service error: {e.message}")
            except Exception as e:
                print(f"  Caught unexpected error: {e}")
        
        # Test download service errors
        print("\nTesting download service errors...")
        try:
            await download_service.pause_download("nonexistent_task")
        except DownloadServiceException as e:
            print(f"Caught download service error: {e.message}")
    
    async def demonstrate_configuration_management(self):
        """Demonstrate configuration management."""
        print("\n=== Configuration Management Demo ===")
        
        config_service = await self.factory.create_config_service_async()
        
        # Load current configuration
        print("Loading current configuration...")
        config = await config_service.load_config()
        print(f"Download directory: {config.download_dir}")
        print(f"Max concurrent downloads: {config.max_concurrent_downloads}")
        print(f"Default format: {config.default_format}")
        
        # Update a setting
        print("\nUpdating configuration...")
        await config_service.update_setting("max_concurrent_downloads", 5)
        
        # Verify the change
        updated_value = await config_service.get_setting("max_concurrent_downloads")
        print(f"Updated max concurrent downloads: {updated_value}")
        
        # Save configuration
        print("Saving configuration...")
        save_result = await config_service.save_config(config)
        print(f"Configuration saved: {save_result}")
    
    async def run_demo(self):
        """Run the complete service layer demonstration."""
        print("YouTube Downloader V3 - Service Layer Demo")
        print("=" * 50)
        
        try:
            # Initialize settings
            settings_manager.initialize()
            
            # Run demonstrations
            video_info = await self.demonstrate_youtube_service()
            await self.demonstrate_download_service(video_info)
            await self.demonstrate_validation_framework()
            await self.demonstrate_error_handling()
            await self.demonstrate_configuration_management()
            
            print("\n=== Demo Complete ===")
            print("All service layer components demonstrated successfully!")
            
        except Exception as e:
            print(f"Demo failed with error: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """Main entry point for the demo."""
    demo = ServiceLayerDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
