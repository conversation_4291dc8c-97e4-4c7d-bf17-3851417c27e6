import unittest
from datetime import datetime
from pathlib import Path
from pydantic import ValidationError
import time

from src.core.domain.models.base import Priority, SizeInfo
from src.core.domain.models.video import (
    VideoQuality, VideoCodec, AudioCodec, VideoContainer,
    VideoFormat, VideoInfo
)
from src.core.domain.models.download import (
    DownloadStatus, DownloadSpeed, ProgressInfo, DownloadConfig, DownloadTask
)

class TestDownloadEnums(unittest.TestCase):
    def test_download_status_enum(self):
        self.assertEqual(DownloadStatus.QUEUED, "queued")
        self.assertEqual(DownloadStatus.PREPARING, "preparing")
        self.assertEqual(DownloadStatus.DOWNLOADING, "downloading")
        self.assertEqual(DownloadStatus.PAUSED, "paused")
        self.assertEqual(DownloadStatus.COMPLETED, "completed")
        self.assertEqual(DownloadStatus.FAILED, "failed")
        self.assertEqual(DownloadStatus.CANCELLED, "cancelled")

class TestDownloadSpeed(unittest.TestCase):
    def test_speed_conversions(self):
        # Test with 1 MB/s
        speed = DownloadSpeed(bytes_per_second=1024 * 1024)
        self.assertEqual(speed.bytes_per_second, 1024 * 1024)
        self.assertEqual(speed.kb_per_second, 1024)
        self.assertEqual(speed.mb_per_second, 1)
    
    def test_format_speed(self):
        # Test bytes/s
        speed = DownloadSpeed(bytes_per_second=500)
        self.assertEqual(speed.format_speed(), "500.00 B/s")
        
        # Test KB/s
        speed = DownloadSpeed(bytes_per_second=1500)
        self.assertEqual(speed.format_speed(), "1.46 KB/s")
        
        # Test MB/s
        speed = DownloadSpeed(bytes_per_second=1024 * 1024 * 2.5)
        self.assertEqual(speed.format_speed(), "2.50 MB/s")

class TestProgressInfo(unittest.TestCase):
    def test_progress_info_creation(self):
        progress = ProgressInfo(bytes_downloaded=1024 * 1024, total_bytes=1024 * 1024 * 10)
        self.assertEqual(progress.bytes_downloaded, 1024 * 1024)
        self.assertEqual(progress.total_bytes, 1024 * 1024 * 10)
        self.assertAlmostEqual(progress.percent_complete, 10.0)
        self.assertIsInstance(progress.started_at, datetime)
        self.assertIsInstance(progress.last_updated, datetime)
    
    def test_percent_calculation(self):
        # Test with provided percent
        progress = ProgressInfo(
            bytes_downloaded=1024 * 1024,
            total_bytes=1024 * 1024 * 10,
            percent_complete=15.0
        )
        self.assertEqual(progress.percent_complete, 15.0)
        
        # Test with calculated percent
        progress = ProgressInfo(
            bytes_downloaded=1024 * 1024 * 2,
            total_bytes=1024 * 1024 * 10
        )
        self.assertEqual(progress.percent_complete, 20.0)
        
        # Test with no total bytes
        progress = ProgressInfo(bytes_downloaded=1024 * 1024)
        self.assertEqual(progress.percent_complete, 0.0)
    
    def test_eta_formatted(self):
        # Test seconds
        progress = ProgressInfo(bytes_downloaded=0, eta_seconds=45)
        self.assertEqual(progress.eta_formatted, "45s")
        
        # Test minutes and seconds
        progress = ProgressInfo(bytes_downloaded=0, eta_seconds=125)
        self.assertEqual(progress.eta_formatted, "2m 5s")
        
        # Test hours, minutes, and seconds
        progress = ProgressInfo(bytes_downloaded=0, eta_seconds=3725)
        self.assertEqual(progress.eta_formatted, "1h 2m 5s")
        
        # Test unknown ETA
        progress = ProgressInfo(bytes_downloaded=0)
        self.assertEqual(progress.eta_formatted, "Unknown")

class TestDownloadConfig(unittest.TestCase):
    def test_download_config_creation(self):
        config = DownloadConfig(
            download_path=Path("D:/Downloads")
        )
        self.assertEqual(config.preferred_quality, VideoQuality.HIGH_1080P)
        self.assertEqual(config.preferred_container, VideoContainer.MP4)
        self.assertEqual(config.download_path, Path("D:/Downloads"))
        self.assertTrue(config.create_thumbnail)
        self.assertFalse(config.create_info_json)
        self.assertFalse(config.audio_only)
        self.assertEqual(config.max_retries, 3)
        self.assertEqual(config.timeout_seconds, 30)
        self.assertIsNone(config.rate_limit)
    
    def test_download_config_custom_values(self):
        config = DownloadConfig(
            preferred_quality=VideoQuality.HIGH_720P,
            preferred_container=VideoContainer.MKV,
            download_path=Path("D:/Videos"),
            create_thumbnail=False,
            create_info_json=True,
            audio_only=True,
            max_retries=5,
            timeout_seconds=60,
            rate_limit="1M"
        )
        self.assertEqual(config.preferred_quality, VideoQuality.HIGH_720P)
        self.assertEqual(config.preferred_container, VideoContainer.MKV)
        self.assertEqual(config.download_path, Path("D:/Videos"))
        self.assertFalse(config.create_thumbnail)
        self.assertTrue(config.create_info_json)
        self.assertTrue(config.audio_only)
        self.assertEqual(config.max_retries, 5)
        self.assertEqual(config.timeout_seconds, 60)
        self.assertEqual(config.rate_limit, "1M")
    
    def test_download_path_validation(self):
        # Valid absolute path
        config = DownloadConfig(download_path=Path("D:/Downloads"))
        self.assertEqual(config.download_path, Path("D:/Downloads"))
        
        # String path should be converted to Path
        config = DownloadConfig(download_path="D:/Downloads")
        self.assertIsInstance(config.download_path, Path)
        self.assertEqual(config.download_path, Path("D:/Downloads"))
        
        # Invalid relative path
        with self.assertRaises(ValidationError):
            DownloadConfig(download_path=Path("Downloads"))

class TestDownloadTask(unittest.TestCase):
    def setUp(self):
        # Create a sample video format
        self.format_720p = VideoFormat(
            format_id="22",
            quality=VideoQuality.HIGH_720P,
            container=VideoContainer.MP4,
            video_codec=VideoCodec.H264,
            audio_codec=AudioCodec.AAC,
            file_size=SizeInfo(bytes=1024 * 1024 * 10),  # 10 MB
            bitrate=1500,
            fps=30,
            width=1280,
            height=720
        )
        
        # Create a sample video info
        self.video_info = VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title="Rick Astley - Never Gonna Give You Up (Official Music Video)",
            available_formats=[self.format_720p]
        )
        
        # Create a sample download config
        self.download_config = DownloadConfig(
            download_path=Path("D:/Downloads")
        )
    
    def test_download_task_creation(self):
        task = DownloadTask(
            video_info=self.video_info,
            config=self.download_config
        )
        
        self.assertEqual(task.video_info, self.video_info)
        self.assertEqual(task.config, self.download_config)
        self.assertEqual(task.status, DownloadStatus.QUEUED)
        self.assertIsInstance(task.progress, ProgressInfo)
        self.assertEqual(task.priority, Priority.NORMAL)
        self.assertIsNone(task.error_message)
        self.assertEqual(task.retry_count, 0)
    
    def test_output_path_generation(self):
        task = DownloadTask(
            video_info=self.video_info,
            selected_format=self.format_720p,
            config=self.download_config
        )
        
        # Check that output filename and path are generated
        self.assertIsNotNone(task.output_filename)
        self.assertTrue(task.output_filename.endswith(".mp4"))
        self.assertIsNotNone(task.output_path)
        self.assertEqual(
            task.output_path,
            Path("D:/Downloads") / task.output_filename
        )
        
        # Test with custom output filename
        task = DownloadTask(
            video_info=self.video_info,
            config=self.download_config,
            output_filename="custom_name.mp4"
        )
        self.assertEqual(task.output_filename, "custom_name.mp4")
        self.assertEqual(
            task.output_path,
            Path("D:/Downloads/custom_name.mp4")
        )
    
    def test_update_progress(self):
        task = DownloadTask(
            video_info=self.video_info,
            config=self.download_config
        )
        
        # Initial state
        self.assertEqual(task.progress.bytes_downloaded, 0)
        self.assertEqual(task.status, DownloadStatus.QUEUED)
        
        # Update progress
        task.update_progress(bytes_downloaded=1024 * 1024, total_bytes=1024 * 1024 * 10)
        
        # Check updated state
        self.assertEqual(task.progress.bytes_downloaded, 1024 * 1024)
        self.assertEqual(task.progress.total_bytes, 1024 * 1024 * 10)
        self.assertEqual(task.status, DownloadStatus.DOWNLOADING)
        
        # Update with speed
        task.update_progress(
            bytes_downloaded=1024 * 1024 * 2,
            total_bytes=1024 * 1024 * 10,
            speed_bps=1024 * 1024  # 1 MB/s
        )
        
        # Check speed and ETA
        self.assertIsNotNone(task.progress.speed)
        self.assertEqual(task.progress.speed.bytes_per_second, 1024 * 1024)
        self.assertIsNotNone(task.progress.eta_seconds)
        self.assertEqual(task.progress.eta_seconds, 8)  # 8 MB remaining at 1 MB/s
    
    def test_state_transitions(self):
        task = DownloadTask(
            video_info=self.video_info,
            config=self.download_config
        )
        
        # Initial state
        self.assertEqual(task.status, DownloadStatus.QUEUED)
        
        # Complete
        task.complete()
        self.assertEqual(task.status, DownloadStatus.COMPLETED)
        self.assertEqual(task.progress.percent_complete, 100.0)
        
        # Reset for next test
        task = DownloadTask(
            video_info=self.video_info,
            config=self.download_config
        )
        
        # Fail
        task.fail("Download failed")
        self.assertEqual(task.status, DownloadStatus.FAILED)
        self.assertEqual(task.error_message, "Download failed")
        
        # Retry
        result = task.retry()
        self.assertTrue(result)
        self.assertEqual(task.status, DownloadStatus.QUEUED)
        self.assertEqual(task.retry_count, 1)
        self.assertIsNone(task.error_message)
        
        # Update progress to start downloading
        task.update_progress(bytes_downloaded=1024)
        self.assertEqual(task.status, DownloadStatus.DOWNLOADING)
        
        # Pause
        result = task.pause()
        self.assertTrue(result)
        self.assertEqual(task.status, DownloadStatus.PAUSED)
        
        # Resume
        result = task.resume()
        self.assertTrue(result)
        self.assertEqual(task.status, DownloadStatus.DOWNLOADING)
        
        # Cancel
        result = task.cancel()
        self.assertTrue(result)
        self.assertEqual(task.status, DownloadStatus.CANCELLED)
        
        # Try invalid transitions
        result = task.pause()  # Can't pause a cancelled download
        self.assertFalse(result)
        self.assertEqual(task.status, DownloadStatus.CANCELLED)
        
        # Test retry limit
        task = DownloadTask(
            video_info=self.video_info,
            config=self.download_config
        )
        task.fail("Download failed")
        task.retry_count = 3  # Max retries is 3
        result = task.retry()
        self.assertFalse(result)  # Should not retry
        self.assertEqual(task.status, DownloadStatus.FAILED)

if __name__ == "__main__":
    unittest.main()