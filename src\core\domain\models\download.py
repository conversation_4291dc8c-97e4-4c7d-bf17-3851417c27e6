from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable
from pydantic import BaseModel, Field, validator, root_validator
from datetime import datetime
from pathlib import Path
import uuid

from .base import BaseEntity, Status, Priority, SizeInfo
from .video import VideoInfo, VideoFormat, VideoQuality, VideoContainer

class DownloadStatus(str, Enum):
    """Download status options"""
    QUEUED = "queued"
    PREPARING = "preparing"
    DOWNLOADING = "downloading"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DownloadSpeed(BaseModel):
    """Represents download speed information"""
    bytes_per_second: float = Field(ge=0, description="Download speed in bytes per second")
    
    @property
    def kb_per_second(self) -> float:
        return self.bytes_per_second / 1024
    
    @property
    def mb_per_second(self) -> float:
        return self.bytes_per_second / (1024 * 1024)
    
    def format_speed(self) -> str:
        """Format speed in human-readable format"""
        if self.mb_per_second >= 1:
            return f"{self.mb_per_second:.2f} MB/s"
        elif self.kb_per_second >= 1:
            return f"{self.kb_per_second:.2f} KB/s"
        else:
            return f"{self.bytes_per_second:.2f} B/s"

class ProgressInfo(BaseModel):
    """Represents download progress information"""
    bytes_downloaded: int = Field(ge=0, description="Bytes downloaded")
    total_bytes: Optional[int] = Field(default=None, description="Total bytes to download")
    percent_complete: float = Field(default=0, ge=0, le=100, description="Percent complete")
    speed: Optional[DownloadSpeed] = Field(default=None, description="Download speed")
    eta_seconds: Optional[int] = Field(default=None, description="Estimated time remaining in seconds")
    started_at: datetime = Field(default_factory=datetime.now, description="When download started")
    last_updated: datetime = Field(default_factory=datetime.now, description="When progress was last updated")
    
    @root_validator(skip_on_failure=True)
    def calculate_percent(cls, values):
        """Calculate percent complete if not provided"""
        bytes_downloaded = values.get('bytes_downloaded', 0)
        total_bytes = values.get('total_bytes')
        percent_complete = values.get('percent_complete')
        
        if percent_complete is None and total_bytes and total_bytes > 0:
            values['percent_complete'] = min(100.0, (bytes_downloaded / total_bytes) * 100)
        
        return values
    
    @property
    def eta_formatted(self) -> str:
        """Get formatted ETA"""
        if not self.eta_seconds:
            return "Unknown"
        
        hours, remainder = divmod(self.eta_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"

class DownloadConfig(BaseModel):
    """Configuration for a download task"""
    preferred_quality: VideoQuality = Field(default=VideoQuality.HIGH_1080P, description="Preferred video quality")
    preferred_container: VideoContainer = Field(default=VideoContainer.MP4, description="Preferred container format")
    download_path: Path = Field(description="Download directory path")
    create_thumbnail: bool = Field(default=True, description="Whether to download thumbnail")
    create_info_json: bool = Field(default=False, description="Whether to create info JSON file")
    audio_only: bool = Field(default=False, description="Whether to download audio only")
    max_retries: int = Field(default=3, ge=0, le=10, description="Maximum retry attempts")
    timeout_seconds: int = Field(default=30, ge=5, le=300, description="Connection timeout in seconds")
    rate_limit: Optional[str] = Field(default=None, description="Download rate limit (e.g., '1M')")
    
    @validator('download_path')
    def validate_download_path(cls, v):
        """Validate download path"""
        if not isinstance(v, Path):
            v = Path(v)
        
        # Ensure path is absolute
        if not v.is_absolute():
            raise ValueError("Download path must be absolute")
        
        return v

class DownloadTask(BaseEntity):
    """Represents a download task"""
    video_info: VideoInfo = Field(description="Video information")
    selected_format: Optional[VideoFormat] = Field(default=None, description="Selected format")
    config: DownloadConfig = Field(description="Download configuration")
    status: DownloadStatus = Field(default=DownloadStatus.QUEUED, description="Current status")
    progress: ProgressInfo = Field(default_factory=ProgressInfo, description="Download progress")
    priority: Priority = Field(default=Priority.NORMAL, description="Task priority")
    output_filename: Optional[str] = Field(default=None, description="Output filename")
    output_path: Optional[Path] = Field(default=None, description="Complete output path")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    retry_count: int = Field(default=0, ge=0, description="Current retry count")
    
    @root_validator(skip_on_failure=True)
    def set_output_path(cls, values):
        """Set output path based on filename and download path"""
        video_info = values.get('video_info')
        config = values.get('config')
        output_filename = values.get('output_filename')
        
        if video_info and config and not output_filename:
            # Generate safe filename from video title
            safe_filename = video_info.get_safe_filename()
            
            # Add appropriate extension
            selected_format = values.get('selected_format')
            if selected_format and selected_format.container:
                extension = selected_format.container.value
            elif config.audio_only:
                extension = "mp3"
            else:
                extension = "mp4"
            
            values['output_filename'] = f"{safe_filename}.{extension}"
        
        if config and values.get('output_filename'):
            values['output_path'] = config.download_path / values['output_filename']
        
        return values
    
    def update_progress(self, bytes_downloaded: int, total_bytes: Optional[int] = None, 
                       speed_bps: Optional[float] = None):
        """Update download progress"""
        now = datetime.now()
        
        # Calculate speed if not provided
        if speed_bps is None and self.progress.last_updated:
            time_diff = (now - self.progress.last_updated).total_seconds()
            if time_diff > 0:
                bytes_diff = bytes_downloaded - self.progress.bytes_downloaded
                speed_bps = bytes_diff / time_diff
        
        # Create speed object if we have speed data
        speed = DownloadSpeed(bytes_per_second=speed_bps) if speed_bps is not None else self.progress.speed
        
        # Calculate ETA
        eta_seconds = None
        if total_bytes and speed and speed.bytes_per_second > 0:
            remaining_bytes = total_bytes - bytes_downloaded
            eta_seconds = int(remaining_bytes / speed.bytes_per_second)
        
        # Update progress
        self.progress = ProgressInfo(
            bytes_downloaded=bytes_downloaded,
            total_bytes=total_bytes or self.progress.total_bytes,
            speed=speed,
            eta_seconds=eta_seconds,
            started_at=self.progress.started_at,
            last_updated=now
        )
        
        # Update status if needed
        if self.status == DownloadStatus.QUEUED and bytes_downloaded > 0:
            self.status = DownloadStatus.DOWNLOADING
        
        # Update entity timestamp
        self.update_timestamp()
    
    def complete(self):
        """Mark download as complete"""
        self.status = DownloadStatus.COMPLETED
        if self.progress.total_bytes:
            self.progress.bytes_downloaded = self.progress.total_bytes
            self.progress.percent_complete = 100.0
        self.progress.eta_seconds = 0
        self.progress.last_updated = datetime.now()
        self.update_timestamp()
    
    def fail(self, error_message: str):
        """Mark download as failed"""
        self.status = DownloadStatus.FAILED
        self.error_message = error_message
        self.progress.last_updated = datetime.now()
        self.update_timestamp()
    
    def pause(self):
        """Pause the download"""
        if self.status == DownloadStatus.DOWNLOADING:
            self.status = DownloadStatus.PAUSED
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False
    
    def resume(self):
        """Resume the download"""
        if self.status == DownloadStatus.PAUSED:
            self.status = DownloadStatus.DOWNLOADING
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False
    
    def cancel(self):
        """Cancel the download"""
        if self.status in [DownloadStatus.QUEUED, DownloadStatus.PREPARING, 
                          DownloadStatus.DOWNLOADING, DownloadStatus.PAUSED]:
            self.status = DownloadStatus.CANCELLED
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False
    
    def retry(self):
        """Retry the download"""
        if self.status == DownloadStatus.FAILED and self.retry_count < self.config.max_retries:
            self.status = DownloadStatus.QUEUED
            self.retry_count += 1
            self.error_message = None
            self.progress.last_updated = datetime.now()
            self.update_timestamp()
            return True
        return False