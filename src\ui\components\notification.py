#!/usr/bin/env python3
"""
Notification Component Module

This module defines the Notification class, which provides a reusable component
for displaying notifications and alerts to the user.
"""

import sys
from enum import Enum
from pathlib import Path

# Try to import PyQt6, fall back to PySide6 if not available
try:
    from PyQt6.QtCore import Qt, QTimer, pyqtSignal as Signal, QPropertyAnimation, QEasingCurve, QRect
    from PyQt6.QtWidgets import QWidget, QLabel, QHBoxLayout, QPushButton, QApplication, QFrame
    from PyQt6.QtGui import QColor, QPainter, QPen, QBrush
    PYQT6 = True
except ImportError:
    try:
        from PySide6.QtCore import Qt, QTimer, Signal, QPropertyAnimation, QEasingCurve, QRect
        from PySide6.QtWidgets import QWidget, QLabel, QHBoxLayout, QPushButton, QApplication, QFrame
        from PySide6.QtGui import Q<PERSON>olor, Q<PERSON><PERSON>ter, Q<PERSON>en, QBrush
        PYQT6 = False
    except ImportError:
        print("Error: Neither PyQt6 nor PySide6 is installed.")
        print("Please install one of them using pip:")
        print("pip install PyQt6")
        print("or")
        print("pip install PySide6")
        sys.exit(1)

# Try to import qtawesome for icons
try:
    import qtawesome as qta
    HAS_QTA = True
except ImportError:
    HAS_QTA = False


class NotificationType(Enum):
    """
    Enumeration of notification types with associated colors and icons.
    """
    INFO = ("info", "#2196F3", "fa5s.info-circle")
    SUCCESS = ("success", "#4CAF50", "fa5s.check-circle")
    WARNING = ("warning", "#FF9800", "fa5s.exclamation-triangle")
    ERROR = ("error", "#F44336", "fa5s.times-circle")
    
    def __init__(self, name, color, icon):
        self.name = name
        self.color = color
        self.icon = icon


class Notification(QFrame):
    """
    A reusable notification component for displaying alerts and messages.
    
    This component can display different types of notifications (info, success, warning, error)
    with appropriate styling and icons. It can be configured to automatically close after a
    specified duration or require manual dismissal.
    """
    
    # Signal emitted when the notification is closed
    closed = Signal()
    
    def __init__(self, parent=None, message="", notification_type=NotificationType.INFO, 
                 duration=5000, closable=True):
        """
        Initialize the notification component.
        
        Args:
            parent: Parent widget
            message (str): The message to display
            notification_type (NotificationType): The type of notification
            duration (int): Duration in milliseconds before auto-closing (0 for no auto-close)
            closable (bool): Whether the notification can be manually closed
        """
        super().__init__(parent)
        
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        self.closable = closable
        
        # Set up the UI
        self._init_ui()
        
        # Set up auto-close timer if duration is specified
        if duration > 0:
            self.timer = QTimer(self)
            self.timer.setSingleShot(True)
            self.timer.timeout.connect(self.close_notification)
            self.timer.start(duration)
    
    def _init_ui(self):
        """
        Initialize the user interface components.
        """
        # Set frame style
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        
        # Set minimum size
        self.setMinimumWidth(300)
        self.setMinimumHeight(50)
        
        # Set maximum size
        self.setMaximumWidth(500)
        
        # Set style based on notification type
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.notification_type.color};
                border-radius: 4px;
                color: white;
            }}
            QPushButton {{
                background-color: transparent;
                border: none;
                color: white;
                font-weight: bold;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
            }}
        """)
        
        # Create layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Add icon if qtawesome is available
        if HAS_QTA:
            icon_label = QLabel()
            icon = qta.icon(self.notification_type.icon, color="white")
            icon_label.setPixmap(icon.pixmap(24, 24))
            layout.addWidget(icon_label)
        
        # Add message label
        self.message_label = QLabel(self.message)
        self.message_label.setWordWrap(True)
        self.message_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(self.message_label, 1)  # 1 = stretch factor
        
        # Add close button if closable
        if self.closable:
            close_button = QPushButton("×")
            close_button.setFixedSize(24, 24)
            close_button.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                }
            """)
            close_button.clicked.connect(self.close_notification)
            layout.addWidget(close_button)
    
    def set_message(self, message):
        """
        Update the notification message.
        
        Args:
            message (str): The new message to display
        """
        self.message = message
        self.message_label.setText(message)
    
    def set_type(self, notification_type):
        """
        Update the notification type.
        
        Args:
            notification_type (NotificationType): The new notification type
        """
        self.notification_type = notification_type
        
        # Update style
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.notification_type.color};
                border-radius: 4px;
                color: white;
            }}
            QPushButton {{
                background-color: transparent;
                border: none;
                color: white;
                font-weight: bold;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
            }}
        """)
        
        # Update icon if qtawesome is available
        if HAS_QTA:
            icon_label = self.layout().itemAt(0).widget()
            if icon_label:
                icon = qta.icon(self.notification_type.icon, color="white")
                icon_label.setPixmap(icon.pixmap(24, 24))
    
    def close_notification(self):
        """
        Close the notification with a fade-out animation.
        """
        # Create fade-out animation
        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.animation.setDuration(500)
        self.animation.setStartValue(1.0)
        self.animation.setEndValue(0.0)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.finished.connect(self._on_animation_finished)
        self.animation.start()
    
    def _on_animation_finished(self):
        """
        Handle animation finished event.
        """
        # Emit closed signal
        self.closed.emit()
        
        # Hide and schedule for deletion
        self.hide()
        self.deleteLater()
    
    def enterEvent(self, event):
        """
        Handle mouse enter event.
        
        Args:
            event: The event object
        """
        # Stop the timer if it's running
        if hasattr(self, 'timer') and self.timer.isActive():
            self.timer.stop()
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """
        Handle mouse leave event.
        
        Args:
            event: The event object
        """
        # Restart the timer if duration is specified
        if hasattr(self, 'timer') and self.duration > 0:
            self.timer.start(self.duration)
        
        super().leaveEvent(event)
    
    def paintEvent(self, event):
        """
        Custom paint event to draw a border and shadow.
        
        Args:
            event: The paint event
        """
        super().paintEvent(event)
        
        # Draw a subtle border
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw border
        pen = QPen(QColor(0, 0, 0, 30))
        pen.setWidth(1)
        painter.setPen(pen)
        painter.drawRoundedRect(0, 0, self.width() - 1, self.height() - 1, 4, 4)


class NotificationManager:
    """
    Manager for displaying and controlling multiple notifications.
    
    This class manages the display of multiple notifications, ensuring they are
    properly positioned and don't overlap.
    """
    
    def __init__(self, parent=None, position=Qt.Corner.TopRight, spacing=10, margin=10):
        """
        Initialize the notification manager.
        
        Args:
            parent: Parent widget (usually the main window)
            position (Qt.Corner): The corner position for notifications
            spacing (int): Spacing between notifications
            margin (int): Margin from the edge of the parent widget
        """
        self.parent = parent
        self.position = position
        self.spacing = spacing
        self.margin = margin
        self.notifications = []
    
    def show_notification(self, message, notification_type=NotificationType.INFO, 
                         duration=5000, closable=True):
        """
        Show a new notification.
        
        Args:
            message (str): The message to display
            notification_type (NotificationType): The type of notification
            duration (int): Duration in milliseconds before auto-closing (0 for no auto-close)
            closable (bool): Whether the notification can be manually closed
            
        Returns:
            Notification: The created notification object
        """
        # Create notification
        notification = Notification(
            parent=self.parent,
            message=message,
            notification_type=notification_type,
            duration=duration,
            closable=closable
        )
        
        # Connect closed signal
        notification.closed.connect(lambda: self._on_notification_closed(notification))
        
        # Add to list and show
        self.notifications.append(notification)
        notification.show()
        
        # Position the notification
        self._position_notifications()
        
        # Fade in animation
        self._animate_notification_in(notification)
        
        return notification
    
    def _position_notifications(self):
        """
        Position all notifications based on the specified corner.
        """
        if not self.parent or not self.notifications:
            return
        
        # Get parent geometry
        parent_rect = self.parent.geometry()
        
        # Calculate positions based on corner
        x, y = 0, 0
        
        if self.position in (Qt.Corner.TopRight, Qt.Corner.BottomRight):
            x = parent_rect.width() - self.margin
        else:
            x = self.margin
        
        if self.position in (Qt.Corner.TopLeft, Qt.Corner.TopRight):
            y = self.margin
            
            # Position each notification from top to bottom
            for notification in self.notifications:
                notification_width = notification.width()
                notification_height = notification.height()
                
                if self.position == Qt.Corner.TopRight:
                    notification.move(x - notification_width, y)
                else:  # TopLeft
                    notification.move(x, y)
                
                y += notification_height + self.spacing
        else:  # BottomLeft or BottomRight
            # Position each notification from bottom to top
            y = parent_rect.height() - self.margin
            
            for notification in reversed(self.notifications):
                notification_width = notification.width()
                notification_height = notification.height()
                
                if self.position == Qt.Corner.BottomRight:
                    notification.move(x - notification_width, y - notification_height)
                else:  # BottomLeft
                    notification.move(x, y - notification_height)
                
                y -= notification_height + self.spacing
    
    def _animate_notification_in(self, notification):
        """
        Animate the notification appearing.
        
        Args:
            notification (Notification): The notification to animate
        """
        # Set initial opacity
        notification.setWindowOpacity(0.0)
        
        # Create fade-in animation
        animation = QPropertyAnimation(notification, b"windowOpacity")
        animation.setDuration(250)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        animation.start()
    
    def _on_notification_closed(self, notification):
        """
        Handle notification closed event.
        
        Args:
            notification (Notification): The closed notification
        """
        # Remove from list
        if notification in self.notifications:
            self.notifications.remove(notification)
        
        # Reposition remaining notifications
        self._position_notifications()
    
    def close_all(self):
        """
        Close all active notifications.
        """
        # Make a copy of the list since it will be modified during iteration
        notifications_copy = self.notifications.copy()
        
        for notification in notifications_copy:
            notification.close_notification()


if __name__ == "__main__":
    # Test the notification component
    app = QApplication(sys.argv)
    
    # Create a main window for testing
    from PyQt6.QtWidgets import QMainWindow, QVBoxLayout, QWidget
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Notification Test")
            self.resize(800, 600)
            
            # Central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # Create buttons for different notification types
            info_button = QPushButton("Show Info Notification")
            success_button = QPushButton("Show Success Notification")
            warning_button = QPushButton("Show Warning Notification")
            error_button = QPushButton("Show Error Notification")
            close_all_button = QPushButton("Close All Notifications")
            
            # Add buttons to layout
            layout.addWidget(info_button)
            layout.addWidget(success_button)
            layout.addWidget(warning_button)
            layout.addWidget(error_button)
            layout.addWidget(close_all_button)
            layout.addStretch()
            
            # Create notification manager
            self.notification_manager = NotificationManager(self)
            
            # Connect buttons
            info_button.clicked.connect(self.show_info)
            success_button.clicked.connect(self.show_success)
            warning_button.clicked.connect(self.show_warning)
            error_button.clicked.connect(self.show_error)
            close_all_button.clicked.connect(self.notification_manager.close_all)
        
        def show_info(self):
            self.notification_manager.show_notification(
                "This is an information message.",
                NotificationType.INFO
            )
        
        def show_success(self):
            self.notification_manager.show_notification(
                "Operation completed successfully!",
                NotificationType.SUCCESS
            )
        
        def show_warning(self):
            self.notification_manager.show_notification(
                "Warning: This action might have consequences.",
                NotificationType.WARNING
            )
        
        def show_error(self):
            self.notification_manager.show_notification(
                "Error: Something went wrong. Please try again.",
                NotificationType.ERROR,
                duration=0  # No auto-close
            )
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())