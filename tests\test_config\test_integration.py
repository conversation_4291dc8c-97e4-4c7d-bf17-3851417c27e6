#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Integration tests for the configuration system.
"""

import unittest
import tempfile
import json
import os
from pathlib import Path

from src.config.base import LogLevel, DownloadQuality, BaseConfiguration
from src.config.settings import SettingsManager
from src.config.paths import PathManager
from src.utils.logging_utils import setup_logging, get_logger, LogCapture


class TestConfigurationIntegration(unittest.TestCase):
    """Integration tests for the configuration system."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for testing
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config_file = self.temp_dir / "config.json"
        self.log_file = self.temp_dir / "app.log"
        
        # Reset singleton instances
        SettingsManager._instance = None
        PathManager._instance = None
        
        # Create new instances
        self.settings_manager = SettingsManager()
        self.path_manager = PathManager()
        
        # Reset root logger
        import logging
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:  # Make a copy of the list
            root_logger.removeHandler(handler)
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove temporary files
        for file_path in [self.config_file, self.log_file]:
            if file_path.exists():
                try:
                    os.remove(file_path)
                except (PermissionError, OSError):
                    pass  # Ignore if file is locked
        
        # Remove temporary directory
        try:
            os.rmdir(self.temp_dir)
        except (PermissionError, OSError):
            pass  # Ignore if directory is not empty or locked
    
    def test_config_persistence(self):
        """Test configuration persistence across instances."""
        # Initialize with config file
        self.settings_manager.initialize(self.config_file)
        
        # Modify configuration
        self.settings_manager.update_setting('app.name', "Integration Test")
        self.settings_manager.update_setting('app.log_level', LogLevel.DEBUG)
        self.settings_manager.update_setting('downloads.default_quality', DownloadQuality.MEDIUM)
        
        # Save configuration
        self.settings_manager.save_to_file(self.config_file)
        
        # Create a new settings manager
        SettingsManager._instance = None
        new_manager = SettingsManager()
        
        # Load configuration
        new_manager.load_from_file(self.config_file)
        
        # Check that settings were loaded correctly
        self.assertEqual(
            new_manager.get_setting('app.name'),
            "Integration Test"
        )
        
        self.assertEqual(
            new_manager.get_setting('app.log_level'),
            LogLevel.DEBUG
        )
        
        self.assertEqual(
            new_manager.get_setting('downloads.default_quality'),
            DownloadQuality.MEDIUM
        )
    
    def test_path_manager_with_settings(self):
        """Test PathManager with SettingsManager integration."""
        # Initialize with config file
        self.settings_manager.initialize(self.config_file)
        
        # Update download directory setting
        test_download_dir = self.temp_dir / "downloads"
        self.settings_manager.update_setting('paths.download_dir', str(test_download_dir))
        
        # Ensure the directory exists using PathManager
        download_dir_path = Path(self.settings_manager.get_setting('paths.download_dir'))
        result = self.path_manager.ensure_directory(download_dir_path)
        
        # Check that directory was created
        self.assertTrue(result)
        self.assertTrue(test_download_dir.exists())
        self.assertTrue(test_download_dir.is_dir())
        
        # Check that the path is writable
        self.assertTrue(self.path_manager.is_path_writable(test_download_dir))
        
        # Get free space
        free_space = self.path_manager.get_free_space(test_download_dir)
        self.assertIsInstance(free_space, int)
        self.assertGreater(free_space, 0)
    
    def test_logging_with_config(self):
        """Test logging system with configuration integration."""
        # Initialize with config file
        self.settings_manager.initialize(self.config_file)
        
        # Update log level and log file path
        self.settings_manager.update_setting('app.log_level', LogLevel.DEBUG)
        self.settings_manager.update_setting('paths.log_dir', str(self.temp_dir))
        
        # Set up logging using configuration
        log_level = self.settings_manager.get_setting('app.log_level')
        log_dir = Path(self.settings_manager.get_setting('paths.log_dir'))
        
        # Ensure log directory exists
        self.path_manager.ensure_directory(log_dir)
        
        # Set up logging
        setup_logging(
            log_level=log_level,
            log_file=self.log_file,
            console_output=True
        )
        
        # Get a logger and log some messages
        logger = get_logger("integration_test")
        
        with LogCapture() as log_capture:
            debug_msg = "DEBUG message"
            info_msg = "INFO message"
            logger.debug(debug_msg)
            logger.info(info_msg)
            
            # Check captured messages
            captured_records = log_capture.records
            self.assertEqual(len(captured_records), 2)
            
            self.assertEqual(captured_records[0].getMessage(), debug_msg)
            self.assertEqual(captured_records[0].levelno, 10)  # DEBUG level
            
            self.assertEqual(captured_records[1].getMessage(), info_msg)
            self.assertEqual(captured_records[1].levelno, 20)  # INFO level
        
        # Check that messages were written to log file
        self.assertTrue(self.log_file.exists())
        with open(self.log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        self.assertIn(debug_msg, log_content)
        self.assertIn(info_msg, log_content)
        self.assertIn("integration_test", log_content)
    
    def test_full_configuration_workflow(self):
        """Test the full configuration workflow."""
        # Initialize with config file
        self.settings_manager.initialize(self.config_file)
        
        # Update various settings
        self.settings_manager.update_setting('app.name', "Full Workflow Test")
        self.settings_manager.update_setting('app.log_level', LogLevel.INFO)
        
        test_download_dir = self.temp_dir / "downloads"
        self.settings_manager.update_setting('paths.download_dir', str(test_download_dir))
        
        self.settings_manager.update_setting('downloads.default_quality', DownloadQuality.HIGH)
        self.settings_manager.update_setting('downloads.preferred_format', "mp4")
        
        # Save configuration
        self.settings_manager.save_to_file(self.config_file)
        
        # Ensure download directory exists
        download_dir_path = Path(self.settings_manager.get_setting('paths.download_dir'))
        self.path_manager.ensure_directory(download_dir_path)
        
        # Set up logging
        setup_logging(
            log_level=self.settings_manager.get_setting('app.log_level'),
            log_file=self.log_file,
            console_output=True
        )
        
        # Get a logger
        logger = get_logger("workflow_test")
        
        # Log some information
        logger.info(f"Application: {self.settings_manager.get_setting('app.name')}")
        logger.info(f"Download Quality: {self.settings_manager.get_setting('downloads.default_quality')}")
        logger.info(f"Download Directory: {self.settings_manager.get_setting('paths.download_dir')}")
        
        # Check that log file was created
        self.assertTrue(self.log_file.exists())
        
        # Check that download directory was created
        self.assertTrue(test_download_dir.exists())
        self.assertTrue(test_download_dir.is_dir())
        
        # Check that configuration file was created with correct content
        self.assertTrue(self.config_file.exists())
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        self.assertEqual(config_dict['app']['name'], "Full Workflow Test")
        self.assertEqual(config_dict['app']['log_level'], "INFO")
        self.assertEqual(config_dict['paths']['download_dir'], str(test_download_dir))
        self.assertEqual(config_dict['downloads']['default_quality'], "1080p")
        self.assertEqual(config_dict['downloads']['preferred_format'], "mp4")


if __name__ == "__main__":
    unittest.main()