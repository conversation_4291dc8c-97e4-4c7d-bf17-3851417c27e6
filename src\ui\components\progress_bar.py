#!/usr/bin/env python3
"""
Progress Bar Component

This module defines a custom progress bar component with additional features.
"""

import sys

try:
    from PyQt6.QtWidgets import QProgressBar, QWidget, QHBoxLayout, QLabel
    from PyQt6.QtCore import Qt, pyqtSignal, QSize
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import Q<PERSON>rogressBar, QWidget, QHBoxLayout, QLabel
        from PySide6.QtCore import Qt, Signal as pyqtSignal, QSize
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required.")
        sys.exit(1)


class ProgressBar(QWidget):
    """Enhanced progress bar with additional information display."""
    
    def __init__(self, parent=None):
        """Initialize the progress bar.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Initialize UI components
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.layout.addWidget(self.progress_bar, 3)  # 3 = stretch factor
        
        # Speed label
        self.speed_label = QLabel("0 KB/s")
        self.speed_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.layout.addWidget(self.speed_label, 1)  # 1 = stretch factor
    
    def set_progress(self, value: float):
        """Set the progress value.
        
        Args:
            value: Progress value (0-100)
        """
        self.progress_bar.setValue(int(value))
    
    def set_speed(self, speed: str):
        """Set the download speed.
        
        Args:
            speed: Speed string (e.g., "1.2 MB/s")
        """
        self.speed_label.setText(speed)
    
    def reset(self):
        """Reset the progress bar."""
        self.progress_bar.setValue(0)
        self.speed_label.setText("0 KB/s")