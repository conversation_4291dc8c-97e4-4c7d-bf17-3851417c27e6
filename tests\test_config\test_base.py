#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for configuration base models.
"""

import unittest
from pathlib import Path

from src.config.base import (
    LogLevel, DownloadQuality, VideoFormat, AudioFormat,
    AppConfig, PathConfig, DownloadConfig, BaseConfiguration,
    create_development_config, create_production_config, create_testing_config
)


class TestEnums(unittest.TestCase):
    """Test configuration enumerations."""
    
    def test_log_level_values(self):
        """Test LogLevel enum values."""
        self.assertEqual(LogLevel.DEBUG, "DEBUG")
        self.assertEqual(LogLevel.INFO, "INFO")
        self.assertEqual(LogLevel.WARNING, "WARNING")
        self.assertEqual(LogLevel.ERROR, "ERROR")
        self.assertEqual(LogLevel.CRITICAL, "CRITICAL")
    
    def test_download_quality_values(self):
        """Test DownloadQuality enum values."""
        self.assertEqual(DownloadQuality.AUDIO_ONLY, "audio_only")
        self.assertEqual(DownloadQuality.LOW, "360p")
        self.assertEqual(DownloadQuality.MEDIUM, "720p")
        self.assertEqual(DownloadQuality.HIGH, "1080p")
        self.assertEqual(DownloadQuality.ULTRA, "2160p")
        self.assertEqual(DownloadQuality.BEST, "best")
    
    def test_video_format_values(self):
        """Test VideoFormat enum values."""
        self.assertEqual(VideoFormat.MP4, "mp4")
        self.assertEqual(VideoFormat.MKV, "mkv")
        self.assertEqual(VideoFormat.WEBM, "webm")
    
    def test_audio_format_values(self):
        """Test AudioFormat enum values."""
        self.assertEqual(AudioFormat.MP3, "mp3")
        self.assertEqual(AudioFormat.AAC, "aac")
        self.assertEqual(AudioFormat.OGG, "ogg")
        self.assertEqual(AudioFormat.M4A, "m4a")


class TestAppConfig(unittest.TestCase):
    """Test AppConfig model."""
    
    def test_default_values(self):
        """Test default values for AppConfig."""
        config = AppConfig()
        self.assertEqual(config.name, "YouTube Downloader V3")
        self.assertEqual(config.version, "3.0.0")
        self.assertEqual(config.log_level, LogLevel.INFO)
        self.assertEqual(config.max_concurrent_downloads, 3)
        self.assertTrue(config.auto_update_check)
        self.assertIsNone(config.user_agent)
    
    def test_custom_values(self):
        """Test custom values for AppConfig."""
        config = AppConfig(
            name="Custom App",
            version="1.0.0",
            log_level=LogLevel.DEBUG,
            max_concurrent_downloads=5,
            auto_update_check=False,
            user_agent="Custom User Agent"
        )
        self.assertEqual(config.name, "Custom App")
        self.assertEqual(config.version, "1.0.0")
        self.assertEqual(config.log_level, LogLevel.DEBUG)
        self.assertEqual(config.max_concurrent_downloads, 5)
        self.assertFalse(config.auto_update_check)
        self.assertEqual(config.user_agent, "Custom User Agent")
    
    def test_validation(self):
        """Test validation for AppConfig."""
        # Test max_concurrent_downloads validation
        with self.assertRaises(ValueError):
            AppConfig(max_concurrent_downloads=0)  # Below minimum
        
        with self.assertRaises(ValueError):
            AppConfig(max_concurrent_downloads=11)  # Above maximum


class TestPathConfig(unittest.TestCase):
    """Test PathConfig model."""
    
    def test_default_values(self):
        """Test default values for PathConfig."""
        config = PathConfig()
        self.assertEqual(config.downloads_dir, Path.home() / "Downloads" / "YouTubeDownloader")
        self.assertEqual(config.config_dir, Path.home() / ".youtube_downloader")
        self.assertEqual(config.temp_dir, Path.home() / ".youtube_downloader" / "temp")
        self.assertEqual(config.log_dir, Path.home() / ".youtube_downloader" / "logs")
    
    def test_custom_values(self):
        """Test custom values for PathConfig."""
        config = PathConfig(
            downloads_dir="/custom/downloads",
            config_dir="/custom/config",
            temp_dir="/custom/temp",
            log_dir="/custom/logs"
        )
        self.assertEqual(config.downloads_dir, Path("/custom/downloads").absolute())
        self.assertEqual(config.config_dir, Path("/custom/config").absolute())
        self.assertEqual(config.temp_dir, Path("/custom/temp").absolute())
        self.assertEqual(config.log_dir, Path("/custom/logs").absolute())
    
    def test_path_validation(self):
        """Test path validation for PathConfig."""
        config = PathConfig(downloads_dir="./downloads")
        self.assertTrue(config.downloads_dir.is_absolute())


class TestDownloadConfig(unittest.TestCase):
    """Test DownloadConfig model."""
    
    def test_default_values(self):
        """Test default values for DownloadConfig."""
        config = DownloadConfig()
        self.assertEqual(config.default_quality, DownloadQuality.HIGH)
        self.assertEqual(config.default_video_format, VideoFormat.MP4)
        self.assertEqual(config.default_audio_format, AudioFormat.MP3)
        self.assertTrue(config.create_thumbnail)
        self.assertFalse(config.create_info_json)
        self.assertEqual(config.max_retries, 3)
        self.assertEqual(config.timeout, 30)
        self.assertIsNone(config.rate_limit)
    
    def test_custom_values(self):
        """Test custom values for DownloadConfig."""
        config = DownloadConfig(
            default_quality=DownloadQuality.MEDIUM,
            default_video_format=VideoFormat.MKV,
            default_audio_format=AudioFormat.AAC,
            create_thumbnail=False,
            create_info_json=True,
            max_retries=5,
            timeout=60,
            rate_limit="1M"
        )
        self.assertEqual(config.default_quality, DownloadQuality.MEDIUM)
        self.assertEqual(config.default_video_format, VideoFormat.MKV)
        self.assertEqual(config.default_audio_format, AudioFormat.AAC)
        self.assertFalse(config.create_thumbnail)
        self.assertTrue(config.create_info_json)
        self.assertEqual(config.max_retries, 5)
        self.assertEqual(config.timeout, 60)
        self.assertEqual(config.rate_limit, "1M")
    
    def test_validation(self):
        """Test validation for DownloadConfig."""
        # Test max_retries validation
        with self.assertRaises(ValueError):
            DownloadConfig(max_retries=-1)  # Below minimum
        
        with self.assertRaises(ValueError):
            DownloadConfig(max_retries=11)  # Above maximum
        
        # Test timeout validation
        with self.assertRaises(ValueError):
            DownloadConfig(timeout=4)  # Below minimum
        
        with self.assertRaises(ValueError):
            DownloadConfig(timeout=301)  # Above maximum


class TestBaseConfiguration(unittest.TestCase):
    """Test BaseConfiguration model."""
    
    def test_default_values(self):
        """Test default values for BaseConfiguration."""
        config = BaseConfiguration()
        self.assertIsInstance(config.app, AppConfig)
        self.assertIsInstance(config.paths, PathConfig)
        self.assertIsInstance(config.downloads, DownloadConfig)
    
    def test_custom_values(self):
        """Test custom values for BaseConfiguration."""
        app_config = AppConfig(name="Custom App")
        path_config = PathConfig(downloads_dir="/custom/downloads")
        download_config = DownloadConfig(default_quality=DownloadQuality.MEDIUM)
        
        config = BaseConfiguration(
            app=app_config,
            paths=path_config,
            downloads=download_config
        )
        
        self.assertEqual(config.app.name, "Custom App")
        self.assertEqual(config.paths.downloads_dir, Path("/custom/downloads").absolute())
        self.assertEqual(config.downloads.default_quality, DownloadQuality.MEDIUM)
    
    def test_validate_assignment(self):
        """Test validate_assignment for BaseConfiguration."""
        config = BaseConfiguration()
        
        # Test valid assignment
        config.app.name = "New Name"
        self.assertEqual(config.app.name, "New Name")
        
        # Test invalid assignment
        with self.assertRaises(ValueError):
            config.app.max_concurrent_downloads = 0  # Below minimum


class TestFactoryFunctions(unittest.TestCase):
    """Test configuration factory functions."""
    
    def test_development_config(self):
        """Test development configuration factory."""
        config = create_development_config()
        self.assertEqual(config.app.log_level, LogLevel.DEBUG)
        self.assertEqual(config.app.max_concurrent_downloads, 1)
        self.assertTrue(config.downloads.create_info_json)
    
    def test_production_config(self):
        """Test production configuration factory."""
        config = create_production_config()
        self.assertEqual(config.app.log_level, LogLevel.INFO)
        self.assertEqual(config.app.max_concurrent_downloads, 3)
        self.assertFalse(config.downloads.create_info_json)
    
    def test_testing_config(self):
        """Test testing configuration factory."""
        config = create_testing_config()
        self.assertEqual(config.app.log_level, LogLevel.DEBUG)
        self.assertEqual(config.paths.downloads_dir, Path("./test_downloads").absolute())
        self.assertEqual(config.paths.temp_dir, Path("./test_temp").absolute())
        self.assertEqual(config.paths.log_dir, Path("./test_logs").absolute())


if __name__ == "__main__":
    unittest.main()