#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Custom Format Example of YouTube Downloader

This script demonstrates how to download YouTube videos with custom format options
using the YouTube Downloader services.
"""

import asyncio
import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from src.services.service_interfaces import VideoQuality, DownloadStatus, DownloadTask
from src.services.service_factory import AsyncServiceFactory


# Custom format options for yt-dlp
# These are examples of common format strings that can be used with yt-dlp
CUSTOM_FORMATS = {
    'audio_only_mp3': 'bestaudio[ext=mp3]/bestaudio/best',
    'audio_only_m4a': 'bestaudio[ext=m4a]/bestaudio/best',
    'video_only_mp4': 'bestvideo[ext=mp4]/bestvideo/best',
    'video_only_webm': 'bestvideo[ext=webm]/bestvideo/best',
    'video_720p': 'bestvideo[height<=720][ext=mp4]+bestaudio[ext=m4a]/best[height<=720][ext=mp4]/best[height<=720]',
    'video_480p': 'bestvideo[height<=480][ext=mp4]+bestaudio[ext=m4a]/best[height<=480][ext=mp4]/best[height<=480]',
    'video_360p': 'bestvideo[height<=360][ext=mp4]+bestaudio[ext=m4a]/best[height<=360][ext=mp4]/best[height<=360]',
    'video_240p': 'bestvideo[height<=240][ext=mp4]+bestaudio[ext=m4a]/best[height<=240][ext=mp4]/best[height<=240]',
    'video_with_subtitles': 'bestvideo+bestaudio --write-sub --sub-lang en',
    'video_with_thumbnail': 'bestvideo+bestaudio --write-thumbnail',
}


async def main():
    """Example of downloading YouTube videos with custom format options."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    youtube_service = await factory.create_youtube_service_async()
    download_service = await factory.create_download_service_async()
    file_service = await factory.create_file_service_async()
    config_service = await factory.create_config_service_async()
    
    # Load configuration
    config = await config_service.load_config()
    print(f"Download directory: {config.download_dir}")
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    
    # Create a directory for custom format downloads
    custom_format_dir = Path(config.download_dir) / "custom_formats"
    await file_service.create_directory(custom_format_dir)
    
    # Example video URL (a short video for demonstration)
    video_url = "https://www.youtube.com/watch?v=jNQXAC9IVRw"  # Me at the zoo (first YouTube video)
    
    # Validate URL
    validation_result = await youtube_service.validate_url(video_url)
    if not validation_result.is_valid:
        print(f"Invalid URL: {video_url} - {validation_result.message}")
        return
    
    # Extract video info
    print(f"Extracting info for {video_url}...")
    video_info = await youtube_service.extract_video_info(video_url)
    
    if not video_info:
        print(f"Failed to extract info for {video_url}")
        return
    
    print(f"Video title: {video_info.title}")
    print(f"Video duration: {format_duration(video_info.duration)}")
    print(f"Video channel: {video_info.channel}")
    
    # Display available custom formats
    print("\nAvailable custom formats:")
    for i, (format_name, format_string) in enumerate(CUSTOM_FORMATS.items(), 1):
        print(f"{i}. {format_name}: {format_string}")
    
    # Ask user to select a format
    selected_format = await ask_user_format_selection(list(CUSTOM_FORMATS.keys()))
    if not selected_format:
        print("No format selected. Exiting.")
        return
    
    format_string = CUSTOM_FORMATS[selected_format]
    print(f"\nSelected format: {selected_format} ({format_string})")
    
    # Create safe filename with format indication
    base_filename = create_safe_filename(video_info.title)
    filename = f"{base_filename}_{selected_format}.mp4"
    
    # For audio-only formats, use appropriate extension
    if selected_format.startswith('audio_only_'):
        ext = selected_format.split('_')[-1]
        filename = f"{base_filename}.{ext}"
    
    destination = custom_format_dir / filename
    
    # Create download task with custom format
    task = DownloadTask(
        url=video_url,
        destination=str(destination),
        quality=VideoQuality.CUSTOM,  # Use CUSTOM quality for custom format string
        video_info=video_info,
        format_string=format_string  # Set the custom format string
    )
    
    # Start download
    task_id = await download_service.start_download(task)
    print(f"Started download with task ID: {task_id}")
    
    # Monitor progress
    await monitor_download_progress(download_service, task_id)


def create_safe_filename(title: str) -> str:
    """Create a safe filename from a title.
    
    Args:
        title: The title to convert to a safe filename
        
    Returns:
        A safe filename
    """
    # Remove invalid characters
    safe_title = re.sub(r'[\\/*?:"<>|]', '', title)
    
    # Replace spaces with underscores
    safe_title = safe_title.replace(' ', '_')
    
    # Limit length
    if len(safe_title) > 100:
        safe_title = safe_title[:100]
        
    return safe_title


def format_duration(seconds: int) -> str:
    """Format duration in seconds to a human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds}s"
    
    minutes = seconds // 60
    remaining_seconds = seconds % 60
    
    if minutes < 60:
        return f"{minutes}m {remaining_seconds}s"
    
    hours = minutes // 60
    remaining_minutes = minutes % 60
    
    return f"{hours}h {remaining_minutes}m {remaining_seconds}s"


async def ask_user_format_selection(format_names: List[str]) -> Optional[str]:
    """Ask user to select a format from the available formats.
    
    Args:
        format_names: List of format names
        
    Returns:
        Selected format name or None if invalid
    """
    while True:
        try:
            selection = input(f"\nEnter number to select format (1-{len(format_names)}) or 'q' to quit: ")
            
            if selection.lower() == 'q':
                return None
            
            index = int(selection) - 1
            if 0 <= index < len(format_names):
                return format_names[index]
            else:
                print(f"Please enter a number between 1 and {len(format_names)}.")
        except ValueError:
            print("Please enter a valid number.")


async def monitor_download_progress(download_service, task_id: str):
    """Monitor download progress for a task.
    
    Args:
        download_service: Download service instance
        task_id: Download task ID
    """
    try:
        # Monitor progress until task is complete, failed, or canceled
        while True:
            # Get task
            task = await download_service.get_task(task_id)
            if not task:
                print(f"Task {task_id} not found.")
                return
            
            progress = task.progress
            if not progress:
                print("No progress information.")
                await asyncio.sleep(1)
                continue
            
            # Clear line
            print('\r' + ' ' * 80, end='\r')
            
            # Print progress
            if progress.status == DownloadStatus.DOWNLOADING:
                # Calculate speed in MB/s
                speed_mb = progress.speed / (1024 * 1024) if progress.speed else 0
                
                # Format ETA
                eta_text = "Unknown"
                if progress.eta is not None:
                    eta_min = progress.eta // 60
                    eta_sec = progress.eta % 60
                    eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                
                # Print progress bar
                bar_length = 30
                filled_length = int(bar_length * progress.progress_percentage / 100)
                bar = '█' * filled_length + '░' * (bar_length - filled_length)
                
                print(f"\r[{bar}] {progress.progress_percentage:.1f}% - {speed_mb:.2f} MB/s - ETA: {eta_text}", end='')
            else:
                # Print status for non-downloading states
                print(f"\rStatus: {progress.status.name}", end='')
            
            # Check if download is complete, failed, or canceled
            if progress.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELED]:
                print()  # New line
                print(f"Download {progress.status.name.lower()}.")
                
                if progress.status == DownloadStatus.COMPLETED:
                    print(f"File saved to: {task.destination}")
                elif progress.status == DownloadStatus.FAILED and progress.error_message:
                    print(f"Error: {progress.error_message}")
                
                return
            
            # Wait before checking again
            await asyncio.sleep(0.5)
            
    except KeyboardInterrupt:
        # Cancel download if user presses Ctrl+C
        print("\nCanceling download...")
        try:
            await download_service.cancel_download(task_id)
            print("Download canceled.")
        except Exception as e:
            print(f"Error canceling download: {e}")
        
    except Exception as e:
        print(f"\nError monitoring download: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())