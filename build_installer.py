#!/usr/bin/env python3
"""
Build Installer Script

This script builds a Windows installer for the YouTube Downloader application.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# Application information
APP_NAME = "YouTube Downloader V2"
APP_VERSION = "2.0.0"
APP_AUTHOR = "YouTube Downloader V2 Team"

# Paths
ROOT_DIR = Path(__file__).parent
SRC_DIR = ROOT_DIR / "src"
DIST_DIR = ROOT_DIR / "dist"
BUILD_DIR = ROOT_DIR / "build"
INSTALLER_DIR = ROOT_DIR / "installer"

# NSIS script template
NSIS_SCRIPT_TEMPLATE = """
; YouTube Downloader V2 Installer Script
; Generated by build_installer.py

; Define application information
!define APPNAME "YouTube Downloader V2"
!define APPVERSION "{version}"
!define APPAUTHOR "YouTube Downloader V2 Team"
!define APPEXE "YouTubeDownloaderV2.exe"

; Include Modern UI
!include "MUI2.nsh"

; General settings
Name "${APPNAME} ${APPVERSION}"
OutFile "YouTubeDownloaderV2_Setup_${APPVERSION}.exe"
InstallDir "$PROGRAMFILES\\${APPNAME}"
InstallDirRegKey HKLM "Software\\${APPNAME}" "Install_Dir"
RequestExecutionLevel admin

; Interface settings
!define MUI_ABORTWARNING
!define MUI_ICON "{icon_path}"
!define MUI_UNICON "{icon_path}"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "{license_path}"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; Languages
!insertmacro MUI_LANGUAGE "English"

; Installer sections
Section "Install"
    SetOutPath "$INSTDIR"
    
    ; Copy application files
    File /r "{dist_dir}\\*.*"
    
    ; Create uninstaller
    WriteUninstaller "$INSTDIR\\uninstall.exe"
    
    ; Create shortcuts
    CreateDirectory "$SMPROGRAMS\\${APPNAME}"
    CreateShortcut "$SMPROGRAMS\\${APPNAME}\\${APPNAME}.lnk" "$INSTDIR\\${APPEXE}"
    CreateShortcut "$SMPROGRAMS\\${APPNAME}\\Uninstall.lnk" "$INSTDIR\\uninstall.exe"
    CreateShortcut "$DESKTOP\\${APPNAME}.lnk" "$INSTDIR\\${APPEXE}"
    
    ; Write registry keys
    WriteRegStr HKLM "Software\\${APPNAME}" "Install_Dir" "$INSTDIR"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "UninstallString" '"$INSTDIR\\uninstall.exe"'
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayVersion" "${APPVERSION}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "Publisher" "${APPAUTHOR}"
    
    ; Write estimated size
    ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
    IntFmt $0 "0x%08X" $0
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "EstimatedSize" "$0"
    
    ; File associations
    WriteRegStr HKCR ".ytdl" "" "YouTubeDownloaderV2.Project"
    WriteRegStr HKCR "YouTubeDownloaderV2.Project" "" "YouTube Downloader V2 Project"
    WriteRegStr HKCR "YouTubeDownloaderV2.Project\\DefaultIcon" "" "$INSTDIR\\${APPEXE},0"
    WriteRegStr HKCR "YouTubeDownloaderV2.Project\\shell\\open\\command" "" '"$INSTDIR\\${APPEXE}" "%1"'
SectionEnd

; Uninstaller section
Section "Uninstall"
    ; Remove application files
    RMDir /r "$INSTDIR"
    
    ; Remove shortcuts
    Delete "$SMPROGRAMS\\${APPNAME}\\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\\${APPNAME}\\Uninstall.lnk"
    RMDir "$SMPROGRAMS\\${APPNAME}"
    Delete "$DESKTOP\\${APPNAME}.lnk"
    
    ; Remove registry keys
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}"
    DeleteRegKey HKLM "Software\\${APPNAME}"
    
    ; Remove file associations
    DeleteRegKey HKCR ".ytdl"
    DeleteRegKey HKCR "YouTubeDownloaderV2.Project"
SectionEnd
"""


def check_requirements():
    """Check if all required tools are installed."""
    print("Checking requirements...")
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("PyInstaller is installed.")
    except ImportError:
        print("Error: PyInstaller is not installed. Please install it with 'pip install pyinstaller'.")
        return False
    
    # Check if NSIS is installed (Windows only)
    if sys.platform == 'win32':
        try:
            # Try to find makensis.exe
            nsis_paths = [
                os.path.join(os.environ.get('PROGRAMFILES', ''), 'NSIS', 'makensis.exe'),
                os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), 'NSIS', 'makensis.exe')
            ]
            
            nsis_found = False
            for path in nsis_paths:
                if os.path.exists(path):
                    nsis_found = True
                    break
            
            if not nsis_found:
                print("Warning: NSIS is not found. You won't be able to create an installer.")
                print("Please install NSIS from https://nsis.sourceforge.io/Download")
                return False
            
            print("NSIS is installed.")
        except Exception as e:
            print(f"Error checking for NSIS: {str(e)}")
            return False
    
    return True


def clean_build_directories():
    """Clean build directories."""
    print("Cleaning build directories...")
    
    # Remove dist directory
    if DIST_DIR.exists():
        shutil.rmtree(DIST_DIR)
    
    # Remove build directory
    if BUILD_DIR.exists():
        shutil.rmtree(BUILD_DIR)
    
    # Create installer directory if it doesn't exist
    if not INSTALLER_DIR.exists():
        INSTALLER_DIR.mkdir(parents=True)


def build_executable():
    """Build executable using PyInstaller."""
    print("Building executable...")
    
    # PyInstaller command
    pyinstaller_args = [
        'pyinstaller',
        '--name=YouTubeDownloaderV2',
        '--windowed',
        '--onedir',
        f'--icon={ROOT_DIR / "src" / "ui" / "resources" / "icons" / "app_icon.ico"}',
        '--add-data=src/ui/resources;resources',
        '--hidden-import=PyQt6.QtSvg',
        '--hidden-import=PyQt6.QtXml',
        f'{SRC_DIR / "ui" / "main_app.py"}'
    ]
    
    # Run PyInstaller
    try:
        subprocess.run(pyinstaller_args, check=True)
        print("Executable built successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error building executable: {str(e)}")
        return False


def create_installer():
    """Create installer using NSIS."""
    if sys.platform != 'win32':
        print("Installer creation is only supported on Windows.")
        return False
    
    print("Creating installer...")
    
    # Create NSIS script
    nsis_script_path = INSTALLER_DIR / "installer.nsi"
    icon_path = ROOT_DIR / "src" / "ui" / "resources" / "icons" / "app_icon.ico"
    license_path = ROOT_DIR / "LICENSE"
    
    # Create a default license file if it doesn't exist
    if not license_path.exists():
        with open(license_path, 'w') as f:
            f.write(f"{APP_NAME} License\n\nCopyright (c) 2025 {APP_AUTHOR}\n\nAll rights reserved.")
    
    # Create NSIS script from template
    nsis_script = NSIS_SCRIPT_TEMPLATE.format(
        version=APP_VERSION,
        icon_path=icon_path.as_posix(),
        license_path=license_path.as_posix(),
        dist_dir=DIST_DIR.as_posix()
    )
    
    with open(nsis_script_path, 'w') as f:
        f.write(nsis_script)
    
    # Find NSIS executable
    nsis_paths = [
        os.path.join(os.environ.get('PROGRAMFILES', ''), 'NSIS', 'makensis.exe'),
        os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), 'NSIS', 'makensis.exe')
    ]
    
    nsis_exe = None
    for path in nsis_paths:
        if os.path.exists(path):
            nsis_exe = path
            break
    
    if not nsis_exe:
        print("Error: NSIS executable not found.")
        return False
    
    # Run NSIS
    try:
        subprocess.run([nsis_exe, str(nsis_script_path)], check=True)
        print("Installer created successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error creating installer: {str(e)}")
        return False


def main():
    """Main function."""
    print(f"Building {APP_NAME} {APP_VERSION}...")
    
    # Check requirements
    if not check_requirements():
        print("Error: Some requirements are not met.")
        return 1
    
    # Clean build directories
    clean_build_directories()
    
    # Build executable
    if not build_executable():
        print("Error: Failed to build executable.")
        return 1
    
    # Create installer
    if sys.platform == 'win32':
        if not create_installer():
            print("Error: Failed to create installer.")
            return 1
    
    print(f"{APP_NAME} {APP_VERSION} built successfully.")
    return 0


if __name__ == "__main__":
    sys.exit(main())