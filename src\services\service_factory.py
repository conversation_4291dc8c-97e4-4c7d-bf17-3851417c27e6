#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Factory Implementation

This module implements the IServiceFactory interface to create and manage
service instances, including YouTubeService, DownloadService, FileService,
and ConfigService.
"""

import asyncio
from typing import Dict, Optional, Type, TypeVar

from src.services.service_interfaces import (
    IServiceFactory, IYouTubeService, IDownloadService, 
    IFileService, IConfigService, ILoggingService
)
from src.services.youtube_service import YouTubeService
from src.services.download_service import DownloadService
from src.services.file_service import FileService
from src.services.config_service import ConfigService
from src.services.logging_service import LoggingService

# Type variable for service interfaces
T = TypeVar('T')


class ServiceFactory(IServiceFactory):
    """Implementation of the service factory interface."""
    
    def __init__(self):
        """Initialize service factory."""
        self._services: Dict[Type, object] = {}
        
    def create_youtube_service(self) -> IYouTubeService:
        """Create or get YouTube service instance.
        
        Returns:
            IYouTubeService implementation
        """
        return self._get_or_create_service(IYouTubeService, YouTubeService)
        
    def create_download_service(self) -> IDownloadService:
        """Create or get download service instance.
        
        Returns:
            IDownloadService implementation
        """
        config_service = self.create_config_service()
        max_concurrent_downloads = 3  # Default value
        
        try:
            # Try to get max_concurrent_downloads from config
            # This is synchronous, so we can't await the async method
            if hasattr(config_service, 'config') and config_service.config:
                max_concurrent_downloads = config_service.config.max_concurrent_downloads
        except Exception:
            # If we can't get it, use the default
            pass
            
        return self._get_or_create_service(
            IDownloadService,
            DownloadService,
            max_concurrent_downloads=max_concurrent_downloads
        )
        
    def create_file_service(self) -> IFileService:
        """Create or get file service instance.
        
        Returns:
            IFileService implementation
        """
        return self._get_or_create_service(IFileService, FileService)
        
    def create_config_service(self) -> IConfigService:
        """Create or get config service instance.
        
        Returns:
            IConfigService implementation
        """
        return self._get_or_create_service(IConfigService, ConfigService)
        
    def create_logging_service(self) -> ILoggingService:
        """Create or get logging service instance.
        
        Returns:
            ILoggingService implementation
        """
        return self._get_or_create_service(ILoggingService, LoggingService)
        
    def _get_or_create_service(self, interface_type: Type[T], implementation_type, **kwargs) -> T:
        """Get existing service instance or create a new one.
        
        Args:
            interface_type: Service interface type
            implementation_type: Service implementation type
            **kwargs: Additional arguments for service constructor
            
        Returns:
            Service instance
        """
        if interface_type not in self._services:
            self._services[interface_type] = implementation_type(**kwargs)
            
        return self._services[interface_type]


class AsyncServiceFactory:
    """Asynchronous wrapper for ServiceFactory."""
    
    def __init__(self, service_factory: Optional[IServiceFactory] = None):
        """Initialize async service factory.
        
        Args:
            service_factory: Optional service factory instance
        """
        self._factory = service_factory or ServiceFactory()
        
    async def create_youtube_service_async(self) -> IYouTubeService:
        """Create or get YouTube service instance asynchronously.
        
        Returns:
            IYouTubeService implementation
        """
        return self._factory.create_youtube_service()
        
    async def create_download_service_async(self) -> IDownloadService:
        """Create or get download service instance asynchronously.
        
        Returns:
            IDownloadService implementation
        """
        # Get config service first to ensure it's initialized
        config_service = await self.create_config_service_async()
        
        # Try to load config to get max_concurrent_downloads
        try:
            await config_service.load_config()
        except Exception:
            # If we can't load config, we'll use the default value
            pass
            
        return self._factory.create_download_service()
        
    async def create_file_service_async(self) -> IFileService:
        """Create or get file service instance asynchronously.
        
        Returns:
            IFileService implementation
        """
        return self._factory.create_file_service()
        
    async def create_config_service_async(self) -> IConfigService:
        """Create or get config service instance asynchronously.
        
        Returns:
            IConfigService implementation
        """
        return self._factory.create_config_service()
        
    async def create_logging_service_async(self) -> ILoggingService:
        """Create or get logging service instance asynchronously.
        
        Returns:
            ILoggingService implementation
        """
        return self._factory.create_logging_service()
        
    async def create_all_services_async(self) -> Dict[Type, object]:
        """Create all services asynchronously.
        
        Returns:
            Dictionary of service instances
        """
        # Create services in order of dependencies
        logging_service = await self.create_logging_service_async()
        config_service = await self.create_config_service_async()
        file_service = await self.create_file_service_async()
        youtube_service = await self.create_youtube_service_async()
        download_service = await self.create_download_service_async()
        
        return {
            ILoggingService: logging_service,
            IConfigService: config_service,
            IFileService: file_service,
            IYouTubeService: youtube_service,
            IDownloadService: download_service
        }


# Example usage
async def example():
    """Example usage of AsyncServiceFactory."""
    # Create factory
    factory = AsyncServiceFactory()
    
    # Create all services
    services = await factory.create_all_services_async()
    
    # Get individual services
    logging_service: ILoggingService = services[ILoggingService]
    config_service: IConfigService = services[IConfigService]
    file_service: IFileService = services[IFileService]
    youtube_service: IYouTubeService = services[IYouTubeService]
    download_service: IDownloadService = services[IDownloadService]
    
    # Set up logging first
    from src.utils.logging_service import LogLevel, LogFormat, LogDestination
    await logging_service.setup(
        log_level=LogLevel.DEBUG,
        log_format=LogFormat.TEXT,
        log_destination=LogDestination.CONSOLE,
        log_dir=None
    )
    
    # Get a logger
    logger = logging_service.get_logger("example")
    logger.info("Services initialized successfully")
    
    # Use services
    config = await config_service.load_config()
    logger.info(f"Download directory: {config.download_dir}")
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    logger.info(f"Created download directory: {config.download_dir}")
    
    # Add context to logs
    logging_service.add_context(session_id="example-session")
    logger.info("Added session context to logs")
    
    # More service usage examples...
    
    # Shutdown logging when done
    await logging_service.shutdown()
    

if __name__ == "__main__":
    asyncio.run(example())