#!/usr/bin/env python3
"""
Settings Window Module

This module defines the SettingsWindow class, which provides a dialog for configuring
application settings such as download directory, theme, and other preferences.
"""

import os
import sys
from pathlib import Path

# Try to import PyQt6, fall back to PySide6 if not available
try:
    from PyQt6.QtCore import Qt, pyqtSignal as Signal
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
        QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
        QCheckBox, QFileDialog, QGroupBox, QFormLayout, QDialogButtonBox
    )
    from PyQt6.QtGui import QIcon
    PYQT6 = True
except ImportError:
    try:
        from PySide6.QtCore import Qt, Signal
        from PySide6.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, <PERSON>Widget,
            QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
            QCheckBox, QFileDialog, QGroupBox, QFormLayout, QDialogButtonBox
        )
        from PySide6.QtGui import QIcon
        PYQT6 = False
    except ImportError:
        print("Error: Neither PyQt6 nor PySide6 is installed.")
        print("Please install one of them using pip:")
        print("pip install PyQt6")
        print("or")
        print("pip install PySide6")
        sys.exit(1)

# Try to import qtawesome for icons
try:
    import qtawesome as qta
    HAS_QTA = True
except ImportError:
    HAS_QTA = False

# Import application configuration
try:
    from src.config.app_config import AppConfig
except ImportError:
    # Fallback for direct module execution
    sys.path.insert(0, str(Path(__file__).resolve().parents[3]))
    from src.config.app_config import AppConfig

# Import validation utilities
from src.ui.utils.ui_validation import validate_settings

# Import style manager and theme switcher
from src.ui.utils.style_manager import StyleManager
from src.ui.components.theme_switcher import ThemeSwitcher, ThemePreview


class SettingsWindow(QDialog):
    """
    Settings Window for configuring application preferences.
    
    This dialog allows users to configure various application settings including:
    - General settings (theme, language, startup behavior)
    - Download settings (output directory, format preferences, concurrent downloads)
    - Network settings (proxy, connection limits)
    - Advanced settings (cache size, debug mode)
    """
    
    # Signal emitted when settings are changed and saved
    settings_changed = Signal(dict)
    
    def __init__(self, parent=None, config=None):
        """
        Initialize the settings window.
        
        Args:
            parent: Parent widget
            config: AppConfig instance or None to use the default
        """
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.resize(600, 450)
        
        # Set window icon if qtawesome is available
        if HAS_QTA:
            self.setWindowIcon(qta.icon('fa5s.cog'))
        
        # Get application configuration
        self.config = config or AppConfig()
        
        # Initialize UI
        self._init_ui()
        
        # Load current settings
        self._load_settings()
    
    def _init_ui(self):
        """
        Initialize the user interface components.
        """
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create tabs
        self.general_tab = self._create_general_tab()
        self.download_tab = self._create_download_tab()
        self.network_tab = self._create_network_tab()
        self.advanced_tab = self._create_advanced_tab()
        
        # Add tabs to tab widget
        self.tab_widget.addTab(self.general_tab, "General")
        self.tab_widget.addTab(self.download_tab, "Download")
        self.tab_widget.addTab(self.network_tab, "Network")
        self.tab_widget.addTab(self.advanced_tab, "Advanced")
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)
        
        # Add dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | 
                                     QDialogButtonBox.StandardButton.Cancel | 
                                     QDialogButtonBox.StandardButton.Apply)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self._apply_settings)
        
        main_layout.addWidget(button_box)
    
    def _create_general_tab(self):
        """
        Create the general settings tab.
        
        Returns:
            QWidget: The general settings tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Appearance group
        appearance_group = QGroupBox("Appearance")
        appearance_layout = QVBoxLayout(appearance_group)
        
        # Theme selection with preview
        theme_form = QFormLayout()
        
        # Get current theme from config
        current_theme = self.config.get_setting("app/theme", StyleManager.THEME_SYSTEM)
        
        # Create theme switcher
        self.theme_switcher = ThemeSwitcher(self, current_theme)
        self.theme_switcher.themeChanged.connect(self._on_theme_changed)
        theme_form.addRow("Theme:", self.theme_switcher)
        
        # Add theme form to appearance layout
        appearance_layout.addLayout(theme_form)
        
        # Add theme preview
        preview_label = QLabel("Preview:")
        appearance_layout.addWidget(preview_label)
        
        # Create a frame to show theme preview
        preview_frame = QWidget()
        preview_frame.setMinimumHeight(100)
        preview_frame.setStyleSheet("background-color: palette(window); border: 1px solid palette(mid);")
        preview_layout = QVBoxLayout(preview_frame)
        
        # Add some sample widgets to the preview
        preview_title = QLabel("Theme Preview")
        preview_title.setProperty("header", True)
        preview_layout.addWidget(preview_title)
        
        preview_button = QPushButton("Sample Button")
        preview_button.setProperty("primary", True)
        preview_layout.addWidget(preview_button)
        
        appearance_layout.addWidget(preview_frame)
        
        # Language selection
        self.language_combo = QComboBox()
        self.language_combo.addItems(["English", "Spanish", "French", "German"])
        appearance_layout.addRow("Language:", self.language_combo)
        
        layout.addWidget(appearance_group)
        
        # Behavior group
        behavior_group = QGroupBox("Behavior")
        behavior_layout = QFormLayout(behavior_group)
        
        # Start with system checkbox
        self.start_with_system = QCheckBox()
        behavior_layout.addRow("Start with system:", self.start_with_system)
        
        # Minimize to tray checkbox
        self.minimize_to_tray = QCheckBox()
        behavior_layout.addRow("Minimize to tray:", self.minimize_to_tray)
        
        # Check for updates checkbox
        self.check_for_updates = QCheckBox()
        behavior_layout.addRow("Check for updates at startup:", self.check_for_updates)
        
        layout.addWidget(behavior_group)
        
        # Add stretch to push everything to the top
        layout.addStretch()
        
        return tab
    
    def _create_download_tab(self):
        """
        Create the download settings tab.
        
        Returns:
            QWidget: The download settings tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Output directory group
        output_group = QGroupBox("Output")
        output_layout = QHBoxLayout(output_group)
        
        # Download directory
        self.download_dir = QLineEdit()
        self.download_dir.setReadOnly(True)
        
        # Browse button
        browse_button = QPushButton("Browse...")
        browse_button.clicked.connect(self._browse_download_dir)
        
        output_layout.addWidget(self.download_dir)
        output_layout.addWidget(browse_button)
        
        layout.addWidget(output_group)
        
        # Format preferences group
        format_group = QGroupBox("Format Preferences")
        format_layout = QFormLayout(format_group)
        
        # Default video format
        self.video_format = QComboBox()
        self.video_format.addItems(["mp4", "webm", "mkv", "avi"])
        format_layout.addRow("Default video format:", self.video_format)
        
        # Default audio format
        self.audio_format = QComboBox()
        self.audio_format.addItems(["mp3", "m4a", "wav", "ogg"])
        format_layout.addRow("Default audio format:", self.audio_format)
        
        # Default video quality
        self.video_quality = QComboBox()
        self.video_quality.addItems(["Best", "1080p", "720p", "480p", "360p"])
        format_layout.addRow("Default video quality:", self.video_quality)
        
        layout.addWidget(format_group)
        
        # Download behavior group
        behavior_group = QGroupBox("Download Behavior")
        behavior_layout = QFormLayout(behavior_group)
        
        # Max concurrent downloads
        self.max_concurrent = QSpinBox()
        self.max_concurrent.setRange(1, 10)
        behavior_layout.addRow("Max concurrent downloads:", self.max_concurrent)
        
        # Auto-start downloads
        self.auto_start = QCheckBox()
        behavior_layout.addRow("Auto-start downloads:", self.auto_start)
        
        # Create playlist folder
        self.create_playlist_folder = QCheckBox()
        behavior_layout.addRow("Create playlist folder:", self.create_playlist_folder)
        
        layout.addWidget(behavior_group)
        
        # Add stretch to push everything to the top
        layout.addStretch()
        
        return tab
    
    def _create_network_tab(self):
        """
        Create the network settings tab.
        
        Returns:
            QWidget: The network settings tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Proxy group
        proxy_group = QGroupBox("Proxy Settings")
        proxy_layout = QFormLayout(proxy_group)
        
        # Use proxy checkbox
        self.use_proxy = QCheckBox()
        self.use_proxy.toggled.connect(self._toggle_proxy_fields)
        proxy_layout.addRow("Use proxy:", self.use_proxy)
        
        # Proxy host
        self.proxy_host = QLineEdit()
        proxy_layout.addRow("Proxy host:", self.proxy_host)
        
        # Proxy port
        self.proxy_port = QSpinBox()
        self.proxy_port.setRange(1, 65535)
        self.proxy_port.setValue(8080)
        proxy_layout.addRow("Proxy port:", self.proxy_port)
        
        # Proxy username
        self.proxy_username = QLineEdit()
        proxy_layout.addRow("Username:", self.proxy_username)
        
        # Proxy password
        self.proxy_password = QLineEdit()
        self.proxy_password.setEchoMode(QLineEdit.EchoMode.Password)
        proxy_layout.addRow("Password:", self.proxy_password)
        
        layout.addWidget(proxy_group)
        
        # Connection group
        connection_group = QGroupBox("Connection")
        connection_layout = QFormLayout(connection_group)
        
        # Connection timeout
        self.connection_timeout = QSpinBox()
        self.connection_timeout.setRange(5, 300)
        self.connection_timeout.setValue(30)
        self.connection_timeout.setSuffix(" seconds")
        connection_layout.addRow("Connection timeout:", self.connection_timeout)
        
        # Retry attempts
        self.retry_attempts = QSpinBox()
        self.retry_attempts.setRange(0, 10)
        self.retry_attempts.setValue(3)
        connection_layout.addRow("Retry attempts:", self.retry_attempts)
        
        layout.addWidget(connection_group)
        
        # Add stretch to push everything to the top
        layout.addStretch()
        
        # Initialize proxy fields state
        self._toggle_proxy_fields(False)
        
        return tab
    
    def _create_advanced_tab(self):
        """
        Create the advanced settings tab.
        
        Returns:
            QWidget: The advanced settings tab widget
        """
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Cache group
        cache_group = QGroupBox("Cache")
        cache_layout = QFormLayout(cache_group)
        
        # Cache size
        self.cache_size = QSpinBox()
        self.cache_size.setRange(100, 10000)
        self.cache_size.setValue(1000)
        self.cache_size.setSuffix(" MB")
        cache_layout.addRow("Cache size:", self.cache_size)
        
        # Clear cache button
        self.clear_cache_button = QPushButton("Clear Cache")
        self.clear_cache_button.clicked.connect(self._clear_cache)
        cache_layout.addRow("", self.clear_cache_button)
        
        layout.addWidget(cache_group)
        
        # Debug group
        debug_group = QGroupBox("Debug")
        debug_layout = QFormLayout(debug_group)
        
        # Debug mode
        self.debug_mode = QCheckBox()
        debug_layout.addRow("Debug mode:", self.debug_mode)
        
        # Log level
        self.log_level = QComboBox()
        self.log_level.addItems(["ERROR", "WARNING", "INFO", "DEBUG"])
        debug_layout.addRow("Log level:", self.log_level)
        
        layout.addWidget(debug_group)
        
        # Reset button
        self.reset_button = QPushButton("Reset to Defaults")
        self.reset_button.clicked.connect(self._reset_to_defaults)
        layout.addWidget(self.reset_button)
        
        # Add stretch to push everything to the top
        layout.addStretch()
        
        return tab
    
    def _toggle_proxy_fields(self, enabled):
        """
        Enable or disable proxy fields based on the 'Use proxy' checkbox.
        
        Args:
            enabled (bool): Whether to enable the proxy fields
        """
        self.proxy_host.setEnabled(enabled)
        self.proxy_port.setEnabled(enabled)
        self.proxy_username.setEnabled(enabled)
        self.proxy_password.setEnabled(enabled)
    
    def _browse_download_dir(self):
        """
        Open a file dialog to select the download directory.
        """
        current_dir = self.download_dir.text() or os.path.expanduser("~/Downloads")
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Download Directory",
            current_dir,
            QFileDialog.Option.ShowDirsOnly
        )
        
        if directory:
            self.download_dir.setText(directory)
    
    def _clear_cache(self):
        """
        Clear the application cache.
        """
        # TODO: Implement cache clearing functionality
        pass
    
    def _reset_to_defaults(self):
        """
        Reset all settings to their default values.
        """
        # Reset general settings
        self.theme_combo.setCurrentText("System")
        self.language_combo.setCurrentText("English")
        self.start_with_system.setChecked(False)
        self.minimize_to_tray.setChecked(True)
        self.check_for_updates.setChecked(True)
        
        # Reset download settings
        self.download_dir.setText(os.path.expanduser("~/Downloads"))
        self.video_format.setCurrentText("mp4")
        self.audio_format.setCurrentText("mp3")
        self.video_quality.setCurrentText("Best")
        self.max_concurrent.setValue(3)
        self.auto_start.setChecked(True)
        self.create_playlist_folder.setChecked(True)
        
        # Reset network settings
        self.use_proxy.setChecked(False)
        self.proxy_host.clear()
        self.proxy_port.setValue(8080)
        self.proxy_username.clear()
        self.proxy_password.clear()
        self.connection_timeout.setValue(30)
        self.retry_attempts.setValue(3)
        
        # Reset advanced settings
        self.cache_size.setValue(1000)
        self.debug_mode.setChecked(False)
        self.log_level.setCurrentText("INFO")
    
    def _load_settings(self):
        """
        Load current settings from the configuration.
        """
        # General settings
        theme_value = self.config.get("ui", "theme", "system")
        # Convert old theme values to new StyleManager constants
        theme_mapping = {
            "Light": StyleManager.THEME_LIGHT,
            "Dark": StyleManager.THEME_DARK,
            "System": StyleManager.THEME_SYSTEM
        }
        theme = theme_mapping.get(theme_value, theme_value)
        self.theme_switcher.set_current_theme(theme)
        self.language_combo.setCurrentText(self.config.get("ui", "language", "English"))
        self.start_with_system.setChecked(self.config.get("general", "start_with_system", False))
        self.minimize_to_tray.setChecked(self.config.get("general", "minimize_to_tray", True))
        self.check_for_updates.setChecked(self.config.get("general", "check_for_updates", True))
        
        # Download settings
        self.download_dir.setText(self.config.get("download", "output_directory", 
                                               os.path.expanduser("~/Downloads")))
        self.video_format.setCurrentText(self.config.get("download", "video_format", "mp4"))
        self.audio_format.setCurrentText(self.config.get("download", "audio_format", "mp3"))
        self.video_quality.setCurrentText(self.config.get("download", "video_quality", "Best"))
        self.max_concurrent.setValue(self.config.get("download", "max_concurrent", 3))
        self.auto_start.setChecked(self.config.get("download", "auto_start", True))
        self.create_playlist_folder.setChecked(self.config.get("download", "create_playlist_folder", True))
        
        # Network settings
        use_proxy = self.config.get("network", "use_proxy", False)
        self.use_proxy.setChecked(use_proxy)
        self.proxy_host.setText(self.config.get("network", "proxy_host", ""))
        self.proxy_port.setValue(self.config.get("network", "proxy_port", 8080))
        self.proxy_username.setText(self.config.get("network", "proxy_username", ""))
        self.proxy_password.setText(self.config.get("network", "proxy_password", ""))
        self.connection_timeout.setValue(self.config.get("network", "connection_timeout", 30))
        self.retry_attempts.setValue(self.config.get("network", "retry_attempts", 3))
        
        # Advanced settings
        self.cache_size.setValue(self.config.get("advanced", "cache_size", 1000))
        self.debug_mode.setChecked(self.config.get("advanced", "debug_mode", False))
        self.log_level.setCurrentText(self.config.get("advanced", "log_level", "INFO"))
        
        # Update proxy fields state
        self._toggle_proxy_fields(use_proxy)
    
    def _get_current_settings(self):
        """
        Get the current settings from the UI.
        
        Returns:
            dict: Dictionary containing the current settings
        """
        settings = {
            "ui": {
                "theme": self.theme_switcher.get_current_theme(),
                "language": self.language_combo.currentText()
            },
            "general": {
                "start_with_system": self.start_with_system.isChecked(),
                "minimize_to_tray": self.minimize_to_tray.isChecked(),
                "check_for_updates": self.check_for_updates.isChecked()
            },
            "download": {
                "output_directory": self.download_dir.text(),
                "video_format": self.video_format.currentText(),
                "audio_format": self.audio_format.currentText(),
                "video_quality": self.video_quality.currentText(),
                "max_concurrent": self.max_concurrent.value(),
                "auto_start": self.auto_start.isChecked(),
                "create_playlist_folder": self.create_playlist_folder.isChecked()
            },
            "network": {
                "use_proxy": self.use_proxy.isChecked(),
                "proxy_host": self.proxy_host.text(),
                "proxy_port": self.proxy_port.value(),
                "proxy_username": self.proxy_username.text(),
                "proxy_password": self.proxy_password.text(),
                "connection_timeout": self.connection_timeout.value(),
                "retry_attempts": self.retry_attempts.value()
            },
            "advanced": {
                "cache_size": self.cache_size.value(),
                "debug_mode": self.debug_mode.isChecked(),
                "log_level": self.log_level.currentText()
            }
        }
        
        return settings
    
    def _on_theme_changed(self, theme):
        """
        Handle theme change event.
        
        Args:
            theme (str): The selected theme identifier.
        """
        # Apply the theme immediately for preview
        if self.parent():
            app = self.parent().window().app
            StyleManager.apply_theme(app, theme)
    
    def _apply_settings(self):
        """
        Apply the current settings.
        """
        settings = self._get_current_settings()
        
        # Validate settings
        validation_result = validate_settings(settings)
        if not validation_result["valid"]:
            # Show error message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Invalid Settings", validation_result["message"])
            return
        
        # Save settings to configuration
        for section, options in settings.items():
            for option, value in options.items():
                self.config.set(section, option, value)
        
        # Save theme setting to app/theme for StyleManager compatibility
        theme = settings["ui"]["theme"]
        self.config.set_setting("app/theme", theme)
        
        # Save configuration to file
        self.config.save()
        
        # Emit settings changed signal
        self.settings_changed.emit(settings)
    
    def accept(self):
        """
        Handle dialog acceptance (OK button).
        """
        self._apply_settings()
        super().accept()


if __name__ == "__main__":
    # Test the settings window
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    window = SettingsWindow()
    window.show()
    sys.exit(app.exec())