/* YouTube Downloader V2 Custom Styles */

/* General Styles */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow, QDialog {
    background-color: #f5f5f5;
}

/* Header Styles */
QLabel[header="true"] {
    font-size: 16pt;
    font-weight: bold;
    color: #cc0000;
}

QLabel[subheader="true"] {
    font-size: 12pt;
    font-weight: bold;
    color: #333333;
}

/* Button Styles */
QPushButton {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 6px 12px;
    color: #333333;
}

QPushButton:hover {
    background-color: #e0e0e0;
    border-color: #bbbbbb;
}

QPushButton:pressed {
    background-color: #d0d0d0;
    border-color: #aaaaaa;
}

QPushButton:disabled {
    background-color: #f8f8f8;
    border-color: #dddddd;
    color: #999999;
}

/* Primary Button */
QPushButton[primary="true"] {
    background-color: #cc0000;
    border-color: #aa0000;
    color: white;
}

QPushButton[primary="true"]:hover {
    background-color: #bb0000;
    border-color: #990000;
}

QPushButton[primary="true"]:pressed {
    background-color: #aa0000;
    border-color: #880000;
}

QPushButton[primary="true"]:disabled {
    background-color: #ffcccc;
    border-color: #ffaaaa;
    color: #ffeeee;
}

/* Secondary Button */
QPushButton[secondary="true"] {
    background-color: #4285f4;
    border-color: #3275e4;
    color: white;
}

QPushButton[secondary="true"]:hover {
    background-color: #3275e4;
    border-color: #2265d4;
}

QPushButton[secondary="true"]:pressed {
    background-color: #2265d4;
    border-color: #1255c4;
}

QPushButton[secondary="true"]:disabled {
    background-color: #c2d5f4;
    border-color: #b2c5e4;
    color: #e2e5f4;
}

/* Input Styles */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 4px;
    color: #333333;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #4285f4;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background-color: #f8f8f8;
    border-color: #dddddd;
    color: #999999;
}

/* Combo Box Styles */
QComboBox {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 4px;
    color: #333333;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: right center;
    width: 20px;
    border-left: 1px solid #cccccc;
}

QComboBox::down-arrow {
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #cccccc;
    selection-background-color: #4285f4;
    selection-color: white;
}

/* Tab Widget Styles */
QTabWidget::pane {
    border: 1px solid #cccccc;
    border-top: 0px;
    background-color: white;
}

QTabBar::tab {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-bottom: 0px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 6px 12px;
    color: #333333;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 0px;
}

QTabBar::tab:hover:!selected {
    background-color: #e0e0e0;
}

/* Progress Bar Styles */
QProgressBar {
    border: 1px solid #cccccc;
    border-radius: 4px;
    background-color: #f0f0f0;
    text-align: center;
    color: #333333;
}

QProgressBar::chunk {
    background-color: #4285f4;
    width: 10px;
    margin: 0.5px;
}

/* Table View Styles */
QTableView {
    border: 1px solid #cccccc;
    background-color: white;
    gridline-color: #eeeeee;
    selection-background-color: #4285f4;
    selection-color: white;
}

QTableView QHeaderView::section {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-left: 0px;
    border-top: 0px;
    padding: 4px;
    color: #333333;
    font-weight: bold;
}

QTableView QHeaderView::section:first {
    border-left: 1px solid #cccccc;
}

/* List View Styles */
QListView {
    border: 1px solid #cccccc;
    background-color: white;
    alternate-background-color: #f9f9f9;
    selection-background-color: #4285f4;
    selection-color: white;
}

QListView::item {
    padding: 4px;
}

QListView::item:hover {
    background-color: #f0f0f0;
}

QListView::item:selected {
    background-color: #4285f4;
    color: white;
}

/* Scroll Bar Styles */
QScrollBar:vertical {
    border: none;
    background-color: #f0f0f0;
    width: 12px;
    margin: 12px 0px 12px 0px;
}

QScrollBar::handle:vertical {
    background-color: #cccccc;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #bbbbbb;
}

QScrollBar::add-line:vertical {
    border: none;
    background-color: #f0f0f0;
    height: 12px;
    subcontrol-position: bottom;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:vertical {
    border: none;
    background-color: #f0f0f0;
    height: 12px;
    subcontrol-position: top;
    subcontrol-origin: margin;
}

QScrollBar:horizontal {
    border: none;
    background-color: #f0f0f0;
    height: 12px;
    margin: 0px 12px 0px 12px;
}

QScrollBar::handle:horizontal {
    background-color: #cccccc;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #bbbbbb;
}

QScrollBar::add-line:horizontal {
    border: none;
    background-color: #f0f0f0;
    width: 12px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:horizontal {
    border: none;
    background-color: #f0f0f0;
    width: 12px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}

/* Menu Styles */
QMenuBar {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

QMenuBar::item {
    spacing: 6px;
    padding: 4px 8px;
    background: transparent;
}

QMenuBar::item:selected {
    background-color: #e0e0e0;
}

QMenu {
    background-color: white;
    border: 1px solid #cccccc;
}

QMenu::item {
    padding: 6px 24px 6px 24px;
}

QMenu::item:selected {
    background-color: #4285f4;
    color: white;
}

QMenu::separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 4px 0px 4px 0px;
}

/* Toolbar Styles */
QToolBar {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    spacing: 4px;
}

QToolBar::separator {
    width: 1px;
    background-color: #e0e0e0;
    margin: 4px 4px 4px 4px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 4px;
}

QToolButton:hover {
    background-color: #e0e0e0;
    border-color: #cccccc;
}

QToolButton:pressed {
    background-color: #d0d0d0;
    border-color: #bbbbbb;
}

/* Status Bar Styles */
QStatusBar {
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
}

QStatusBar::item {
    border: none;
}

/* Group Box Styles */
QGroupBox {
    border: 1px solid #cccccc;
    border-radius: 4px;
    margin-top: 12px;
    padding-top: 12px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0px 4px;
    color: #333333;
    font-weight: bold;
}

/* Spin Box Styles */
QSpinBox, QDoubleSpinBox {
    background-color: white;
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 4px;
    color: #333333;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    width: 16px;
    border-left: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    width: 16px;
    border-left: 1px solid #cccccc;
    border-top: 1px solid #cccccc;
}

/* Check Box Styles */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #cccccc;
    background-color: white;
    border-radius: 3px;
}

QCheckBox::indicator:checked {
    border: 1px solid #4285f4;
    background-color: #4285f4;
    border-radius: 3px;
}

/* Radio Button Styles */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
}

QRadioButton::indicator:unchecked {
    border: 1px solid #cccccc;
    background-color: white;
    border-radius: 9px;
}

QRadioButton::indicator:checked {
    border: 1px solid #4285f4;
    background-color: #4285f4;
    border-radius: 9px;
}

/* Slider Styles */
QSlider::groove:horizontal {
    border: 1px solid #cccccc;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background-color: #4285f4;
    border: 1px solid #3275e4;
    width: 18px;
    height: 18px;
    margin: -6px 0px;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background-color: #3275e4;
    border-color: #2265d4;
}

/* Custom Styles for YouTube Downloader */

/* Video Card */
QFrame[videoCard="true"] {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

QFrame[videoCard="true"]:hover {
    border-color: #4285f4;
    background-color: #f9f9f9;
}

/* Video Title */
QLabel[videoTitle="true"] {
    font-weight: bold;
    font-size: 11pt;
    color: #333333;
}

/* Video Channel */
QLabel[videoChannel="true"] {
    color: #cc0000;
    font-size: 10pt;
}

/* Video Stats */
QLabel[videoStats="true"] {
    color: #666666;
    font-size: 9pt;
}

/* Download Button */
QPushButton[downloadButton="true"] {
    background-color: #cc0000;
    border-color: #aa0000;
    color: white;
    font-weight: bold;
}

QPushButton[downloadButton="true"]:hover {
    background-color: #bb0000;
    border-color: #990000;
}

/* Watch Button */
QPushButton[watchButton="true"] {
    background-color: #4285f4;
    border-color: #3275e4;
    color: white;
}

QPushButton[watchButton="true"]:hover {
    background-color: #3275e4;
    border-color: #2265d4;
}

/* Search Box */
QLineEdit[searchBox="true"] {
    border: 1px solid #cccccc;
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 11pt;
}

QLineEdit[searchBox="true"]:focus {
    border-color: #4285f4;
}

/* Search Button */
QPushButton[searchButton="true"] {
    background-color: #f0f0f0;
    border: 1px solid #cccccc;
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: bold;
}

QPushButton[searchButton="true"]:hover {
    background-color: #e0e0e0;
    border-color: #bbbbbb;
}

/* Download Progress */
QProgressBar[downloadProgress="true"] {
    border: 1px solid #cccccc;
    border-radius: 4px;
    background-color: #f0f0f0;
    text-align: center;
    color: #333333;
    font-weight: bold;
}

QProgressBar[downloadProgress="true"]::chunk {
    background-color: #4285f4;
    width: 10px;
    margin: 0.5px;
}

/* Library Item */
QFrame[libraryItem="true"] {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

QFrame[libraryItem="true"]:hover {
    border-color: #4285f4;
    background-color: #f9f9f9;
}

/* Playlist Item */
QFrame[playlistItem="true"] {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px;
}

QFrame[playlistItem="true"]:hover {
    border-color: #4285f4;
    background-color: #f0f0f0;
}

/* Status Message */
QLabel[statusMessage="true"] {
    color: #666666;
    font-style: italic;
}

/* Error Message */
QLabel[errorMessage="true"] {
    color: #cc0000;
    font-weight: bold;
}

/* Success Message */
QLabel[successMessage="true"] {
    color: #4CAF50;
    font-weight: bold;
}