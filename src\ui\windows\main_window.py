#!/usr/bin/env python3
"""
Main Window Module

This module defines the main application window for the YouTube Downloader.
"""

import sys

try:
    from PyQt6.QtWidgets import (
        QMainWindow, QTabWidget, QToolBar, QStatusBar, 
        QMenuBar, QMenu, QMessageBox, QWidget, QVBoxLayout
    )
    from PyQt6.QtGui import QAction, QIcon
    from PyQt6.QtCore import Qt, QSize
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QMainWindow, QTabWidget, QToolBar, QStatusBar, 
            QMenuBar, QMenu, QMessageBox, QWidget, QVBoxLayout
        )
        from PySide6.QtGui import QAction, QIcon
        from PySide6.QtCore import Qt, QSize
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required.")
        sys.exit(1)

try:
    import qtawesome as qta
    HAS_QTAWESOME = True
except ImportError:
    HAS_QTAWESOME = False

from src.config.app_config import AppConfig
from src.ui.widgets.search_widget import SearchWidget
from src.ui.widgets.download_widget import DownloadWidget
from src.ui.widgets.library_widget import LibraryWidget
from src.ui.windows.settings_window import SettingsWindow
from src.ui.windows.about_window import AboutWindow
from src.ui.utils.style_manager import StyleManager


class MainWindow(QMainWindow):
    """Main window for the YouTube Downloader application."""
    
    def __init__(self, config: AppConfig, app=None):
        """Initialize the main window.
        
        Args:
            config: Application configuration
            app: QApplication instance for theme management
        """
        super().__init__()
        self.config = config
        self.app = app
        
        # Set window properties
        self.setWindowTitle("YouTube Downloader V2")
        self.setMinimumSize(900, 600)
        
        # Store reference to settings window to prevent garbage collection
        self.settings_window = None
        self.about_window = None
        
        # Initialize UI components
        self._init_ui()
        
        # Connect signals and slots
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Create central widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_tabs()
        
        # Create menu bar
        self._create_menu_bar()
        
        # Create toolbar
        self._create_toolbar()
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def _create_tabs(self):
        """Create the main tabs for the application."""
        # Search tab
        self.search_widget = SearchWidget(self.config)
        self.tab_widget.addTab(self.search_widget, self._get_icon("fa5s.search"), "Search")
        
        # Download tab
        self.download_widget = DownloadWidget(self.config)
        self.tab_widget.addTab(self.download_widget, self._get_icon("fa5s.download"), "Downloads")
        
        # Library tab
        self.library_widget = LibraryWidget(self.config)
        self.tab_widget.addTab(self.library_widget, self._get_icon("fa5s.film"), "Library")
    
    def _create_menu_bar(self):
        """Create the application menu bar."""
        # File menu
        file_menu = self.menuBar().addMenu("&File")
        
        # Settings action
        settings_action = QAction(self._get_icon("fa5s.cog"), "&Settings", self)
        settings_action.setStatusTip("Open settings dialog")
        settings_action.triggered.connect(self._open_settings)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction(self._get_icon("fa5s.door-open"), "E&xit", self)
        exit_action.setStatusTip("Exit the application")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = self.menuBar().addMenu("&Help")
        
        # About action
        about_action = QAction(self._get_icon("fa5s.info-circle"), "&About", self)
        about_action.setStatusTip("Show the application's About box")
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_toolbar(self):
        """Create the application toolbar."""
        self.toolbar = QToolBar("Main Toolbar")
        self.toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(self.toolbar)
        
        # Add search action
        search_action = QAction(self._get_icon("fa5s.search"), "Search", self)
        search_action.setStatusTip("Search for videos")
        search_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        self.toolbar.addAction(search_action)
        
        # Add download action
        download_action = QAction(self._get_icon("fa5s.download"), "Downloads", self)
        download_action.setStatusTip("View downloads")
        download_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        self.toolbar.addAction(download_action)
        
        # Add library action
        library_action = QAction(self._get_icon("fa5s.film"), "Library", self)
        library_action.setStatusTip("View library")
        library_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2))
        self.toolbar.addAction(library_action)
        
        self.toolbar.addSeparator()
        
        # Add settings action
        settings_action = QAction(self._get_icon("fa5s.cog"), "Settings", self)
        settings_action.setStatusTip("Open settings dialog")
        settings_action.triggered.connect(self._open_settings)
        self.toolbar.addAction(settings_action)
    
    def _connect_signals(self):
        """Connect signals and slots."""
        # Connect tab widget signals
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
    
    def _on_settings_changed(self, settings):
        """Handle settings changed event.
        
        Args:
            settings: Dictionary containing the new settings
        """
        # Apply theme if it has changed
        if self.app:
            theme = self.config.get_setting("app/theme", StyleManager.THEME_SYSTEM)
            StyleManager.apply_theme(self.app, theme)
        
        # Update status bar
        self.status_bar.showMessage("Settings updated", 3000)
    
    def _on_tab_changed(self, index):
        """Handle tab change event.
        
        Args:
            index: Index of the selected tab
        """
        tab_name = self.tab_widget.tabText(index)
        self.status_bar.showMessage(f"Switched to {tab_name} tab")
    
    def _open_settings(self):
        """Open the settings dialog."""
        # Create settings window if it doesn't exist
        if not self.settings_window:
            self.settings_window = SettingsWindow(self, self.config)
            self.settings_window.settings_changed.connect(self._on_settings_changed)
        
        # Show settings window
        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()
        
        self.status_bar.showMessage("Settings dialog opened")
    
    def _show_about(self):
        """Show the about dialog."""
        # Create about window if it doesn't exist
        if not self.about_window:
            self.about_window = AboutWindow(self, self.config)
        
        # Show about window
        self.about_window.show()
        self.about_window.raise_()
        self.about_window.activateWindow()
        
        self.status_bar.showMessage("About dialog opened")
    
    def _get_icon(self, icon_name):
        """Get an icon from qtawesome or return a blank icon if not available.
        
        Args:
            icon_name: Name of the icon to retrieve
            
        Returns:
            QIcon: The requested icon or a blank icon if qtawesome is not available
        """
        if HAS_QTAWESOME:
            return qta.icon(icon_name)
        return QIcon()
    
    def closeEvent(self, event):
        """Handle window close event.
        
        Args:
            event: Close event
        """
        # Implement any cleanup or confirmation dialogs here if needed
        event.accept()