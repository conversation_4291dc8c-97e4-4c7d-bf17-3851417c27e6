#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
File Operations Example of YouTube Downloader

This script demonstrates how to use the FileService for various file operations
such as creating directories, validating paths, getting file sizes, moving files,
and deleting files.
"""

import asyncio
import os
from pathlib import Path
import tempfile
import time

from src.services.service_factory import AsyncServiceFactory


async def main():
    """Example of using FileService for various file operations."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create file service
    file_service = await factory.create_file_service_async()
    
    # Create a temporary directory for our examples
    temp_dir = Path(tempfile.gettempdir()) / f"youtube_downloader_example_{int(time.time())}"
    print(f"Creating temporary directory: {temp_dir}")
    
    try:
        # Example 1: Create directory
        print("\nExample 1: Creating directories")
        await file_service.create_directory(temp_dir)
        print(f"Created directory: {temp_dir}")
        
        # Create nested directories
        nested_dir = temp_dir / "nested" / "subdirectory" / "structure"
        await file_service.create_directory(nested_dir)
        print(f"Created nested directories: {nested_dir}")
        
        # Example 2: Validate paths
        print("\nExample 2: Validating paths")
        valid_paths = [
            temp_dir,
            nested_dir,
            temp_dir / "example.txt"
        ]
        
        for path in valid_paths:
            try:
                await file_service.validate_path(str(path))
                print(f"Path is valid: {path}")
            except Exception as e:
                print(f"Path is invalid: {path} - {e}")
        
        # Try an invalid path
        invalid_path = "invalid:path*with?characters"
        try:
            await file_service.validate_path(invalid_path)
            print(f"Path is valid: {invalid_path}")
        except Exception as e:
            print(f"Path is invalid: {invalid_path} - {e}")
        
        # Example 3: Create and get file size
        print("\nExample 3: Creating files and getting file sizes")
        
        # Create a test file
        test_file = temp_dir / "test_file.txt"
        with open(test_file, "w") as f:
            f.write("This is a test file.\n" * 100)  # Write some content
        
        # Get file size
        size = await file_service.get_file_size(str(test_file))
        print(f"File size: {size} bytes")
        
        # Get formatted file size
        formatted_size = await file_service.get_formatted_file_size(str(test_file))
        print(f"Formatted file size: {formatted_size}")
        
        # Example 4: Move file
        print("\nExample 4: Moving files")
        
        # Create a source file
        source_file = temp_dir / "source_file.txt"
        with open(source_file, "w") as f:
            f.write("This is a source file that will be moved.")
        
        # Define destination file
        destination_file = nested_dir / "moved_file.txt"
        
        # Move the file
        await file_service.move_file(str(source_file), str(destination_file))
        print(f"Moved file from {source_file} to {destination_file}")
        
        # Verify the file was moved
        if not source_file.exists() and destination_file.exists():
            print("File was successfully moved.")
        else:
            print("File move operation failed.")
        
        # Example 5: List files
        print("\nExample 5: Listing files")
        
        # Create some additional files
        for i in range(5):
            file_path = temp_dir / f"file_{i}.txt"
            with open(file_path, "w") as f:
                f.write(f"This is file {i}.")
        
        # List files in the directory
        files = await file_service.list_files(str(temp_dir))
        print(f"Files in {temp_dir}:")
        for file in files:
            print(f"  {file}")
        
        # List files recursively
        all_files = await file_service.list_files(str(temp_dir), recursive=True)
        print(f"\nAll files in {temp_dir} (recursive):")
        for file in all_files:
            print(f"  {file}")
        
        # Example 6: Delete files
        print("\nExample 6: Deleting files")
        
        # Delete a single file
        file_to_delete = temp_dir / "file_0.txt"
        await file_service.delete_file(str(file_to_delete))
        print(f"Deleted file: {file_to_delete}")
        
        # Verify the file was deleted
        if not file_to_delete.exists():
            print("File was successfully deleted.")
        else:
            print("File delete operation failed.")
        
        # Example 7: Delete directory
        print("\nExample 7: Deleting directories")
        
        # Delete a directory
        dir_to_delete = temp_dir / "nested"
        await file_service.delete_file(str(dir_to_delete), recursive=True)
        print(f"Deleted directory: {dir_to_delete}")
        
        # Verify the directory was deleted
        if not dir_to_delete.exists():
            print("Directory was successfully deleted.")
        else:
            print("Directory delete operation failed.")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up: Delete the temporary directory
        print("\nCleaning up...")
        try:
            await file_service.delete_file(str(temp_dir), recursive=True)
            print(f"Deleted temporary directory: {temp_dir}")
        except Exception as e:
            print(f"Error deleting temporary directory: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())