#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Validators

This module provides validators for different service operations.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from .validation_result import ServiceValidationResult, ServiceValidationSeverity
from ..utils.validation_utils import URLValidator, PathValidator, ConfigValidator


class ServiceValidator(ABC):
    """Base class for service validators."""
    
    def __init__(self, service_name: str):
        """Initialize service validator.
        
        Args:
            service_name: Name of the service being validated
        """
        self.service_name = service_name
    
    @abstractmethod
    def validate(self, data: Any, operation: str = "unknown") -> ServiceValidationResult:
        """Validate service data.
        
        Args:
            data: Data to validate
            operation: Name of the operation being validated
            
        Returns:
            Validation result
        """
        pass
    
    def create_result(self, operation: str = "unknown") -> ServiceValidationResult:
        """Create a new validation result for this service.
        
        Args:
            operation: Name of the operation
            
        Returns:
            New validation result
        """
        return ServiceValidationResult(service_name=self.service_name, operation=operation)


class YouTubeServiceValidator(ServiceValidator):
    """Validator for YouTube service operations."""
    
    def __init__(self):
        super().__init__("YouTubeService")
    
    def validate(self, data: Any, operation: str = "unknown") -> ServiceValidationResult:
        """Validate YouTube service data."""
        result = self.create_result(operation)
        
        if operation == "extract_video_info":
            return self.validate_video_extraction(data)
        elif operation == "extract_playlist_info":
            return self.validate_playlist_extraction(data)
        elif operation == "validate_url":
            return self.validate_url_validation(data)
        else:
            result.add_warning(f"Unknown operation: {operation}")
        
        return result
    
    def validate_video_extraction(self, url: str) -> ServiceValidationResult:
        """Validate video information extraction request.
        
        Args:
            url: YouTube video URL
            
        Returns:
            Validation result
        """
        result = self.create_result("extract_video_info")
        
        if not url:
            result.add_error("URL is required for video extraction")
            return result
        
        # Validate URL format
        url_result = URLValidator.validate_youtube_url(url)
        if not url_result.is_valid:
            for issue in url_result.issues:
                result.add_error(f"URL validation failed: {issue.message}")
        
        # Check if it's a video URL (not playlist)
        if 'playlist?list=' in url:
            result.add_error("Playlist URL provided for video extraction")
        
        # Extract and validate video ID
        video_id = URLValidator.extract_video_id(url)
        if not video_id:
            result.add_error("Could not extract video ID from URL")
        elif not URLValidator.is_valid_video_id(video_id):
            result.add_error(f"Invalid video ID format: {video_id}")
        
        return result
    
    def validate_playlist_extraction(self, url: str) -> ServiceValidationResult:
        """Validate playlist information extraction request.
        
        Args:
            url: YouTube playlist URL
            
        Returns:
            Validation result
        """
        result = self.create_result("extract_playlist_info")
        
        if not url:
            result.add_error("URL is required for playlist extraction")
            return result
        
        # Validate URL format
        url_result = URLValidator.validate_youtube_url(url)
        if not url_result.is_valid:
            for issue in url_result.issues:
                result.add_error(f"URL validation failed: {issue.message}")
        
        # Check if it's a playlist URL
        if 'playlist?list=' not in url:
            result.add_error("Video URL provided for playlist extraction")
        
        # Extract and validate playlist ID
        playlist_id = URLValidator.extract_playlist_id(url)
        if not playlist_id:
            result.add_error("Could not extract playlist ID from URL")
        elif not URLValidator.is_valid_playlist_id(playlist_id):
            result.add_error(f"Invalid playlist ID format: {playlist_id}")
        
        return result
    
    def validate_url_validation(self, url: str) -> ServiceValidationResult:
        """Validate URL validation request.
        
        Args:
            url: URL to validate
            
        Returns:
            Validation result
        """
        result = self.create_result("validate_url")
        
        if not url:
            result.add_error("URL is required for validation")
            return result
        
        # Validate URL format
        url_result = URLValidator.validate_youtube_url(url)
        if not url_result.is_valid:
            for issue in url_result.issues:
                result.add_error(f"URL validation failed: {issue.message}")
        
        return result


class DownloadServiceValidator(ServiceValidator):
    """Validator for download service operations."""
    
    def __init__(self):
        super().__init__("DownloadService")
    
    def validate(self, data: Any, operation: str = "unknown") -> ServiceValidationResult:
        """Validate download service data."""
        result = self.create_result(operation)
        
        if operation == "start_download":
            return self.validate_download_start(data)
        elif operation == "pause_download":
            return self.validate_task_operation(data, "pause")
        elif operation == "resume_download":
            return self.validate_task_operation(data, "resume")
        elif operation == "cancel_download":
            return self.validate_task_operation(data, "cancel")
        else:
            result.add_warning(f"Unknown operation: {operation}")
        
        return result
    
    def validate_download_start(self, task_data: Dict[str, Any]) -> ServiceValidationResult:
        """Validate download start request.
        
        Args:
            task_data: Download task data
            
        Returns:
            Validation result
        """
        result = self.create_result("start_download")
        
        # Check required fields
        required_fields = ['url', 'destination', 'quality']
        for field in required_fields:
            if field not in task_data:
                result.add_error(f"Missing required field: {field}")
        
        # Validate URL if present
        if 'url' in task_data:
            url_result = URLValidator.validate_youtube_url(task_data['url'])
            if not url_result.is_valid:
                for issue in url_result.issues:
                    result.add_error(f"URL validation failed: {issue.message}")
        
        # Validate destination path if present
        if 'destination' in task_data:
            path_result = PathValidator.validate_path(
                task_data['destination'], 
                must_be_writable=True
            )
            if not path_result.is_valid:
                for issue in path_result.issues:
                    result.add_error(f"Destination path validation failed: {issue.message}")
        
        # Validate quality if present
        if 'quality' in task_data:
            valid_qualities = ['best', 'worst', '720p', '1080p', '480p', '360p', '240p']
            if task_data['quality'] not in valid_qualities:
                result.add_error(f"Invalid quality: {task_data['quality']}. "
                               f"Must be one of {valid_qualities}")
        
        return result
    
    def validate_task_operation(self, task_id: str, operation: str) -> ServiceValidationResult:
        """Validate task operation request.
        
        Args:
            task_id: Task identifier
            operation: Operation name
            
        Returns:
            Validation result
        """
        result = self.create_result(f"{operation}_download")
        
        if not task_id:
            result.add_error("Task ID is required")
        elif not isinstance(task_id, str):
            result.add_error("Task ID must be a string")
        
        return result


class FileServiceValidator(ServiceValidator):
    """Validator for file service operations."""
    
    def __init__(self):
        super().__init__("FileService")
    
    def validate(self, data: Any, operation: str = "unknown") -> ServiceValidationResult:
        """Validate file service data."""
        result = self.create_result(operation)
        
        if operation in ["create_directory", "validate_path", "delete_file", "move_file"]:
            return self.validate_path_operation(data, operation)
        elif operation == "get_file_size":
            return self.validate_file_size_operation(data)
        else:
            result.add_warning(f"Unknown operation: {operation}")
        
        return result
    
    def validate_path_operation(self, path: Union[str, Path], operation: str) -> ServiceValidationResult:
        """Validate path-based operation.
        
        Args:
            path: File or directory path
            operation: Operation name
            
        Returns:
            Validation result
        """
        result = self.create_result(operation)
        
        if not path:
            result.add_error("Path is required")
            return result
        
        # Validate path format
        path_result = PathValidator.validate_path(path)
        if not path_result.is_valid:
            for issue in path_result.issues:
                result.add_error(f"Path validation failed: {issue.message}")
        
        return result
    
    def validate_file_size_operation(self, path: Union[str, Path]) -> ServiceValidationResult:
        """Validate file size operation.
        
        Args:
            path: File path
            
        Returns:
            Validation result
        """
        result = self.create_result("get_file_size")
        
        if not path:
            result.add_error("Path is required")
            return result
        
        # Validate path format and existence
        path_result = PathValidator.validate_path(path, must_exist=True)
        if not path_result.is_valid:
            for issue in path_result.issues:
                result.add_error(f"Path validation failed: {issue.message}")
        
        return result


class ConfigServiceValidator(ServiceValidator):
    """Validator for configuration service operations."""
    
    def __init__(self):
        super().__init__("ConfigService")
    
    def validate(self, data: Any, operation: str = "unknown") -> ServiceValidationResult:
        """Validate configuration service data."""
        result = self.create_result(operation)
        
        if operation == "load_config":
            return self.validate_config_load(data)
        elif operation == "save_config":
            return self.validate_config_save(data)
        elif operation == "update_setting":
            return self.validate_setting_update(data)
        else:
            result.add_warning(f"Unknown operation: {operation}")
        
        return result
    
    def validate_config_load(self, config_path: Union[str, Path]) -> ServiceValidationResult:
        """Validate configuration loading.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Validation result
        """
        result = self.create_result("load_config")
        
        if config_path:
            path_result = PathValidator.validate_path(config_path, must_exist=True)
            if not path_result.is_valid:
                for issue in path_result.issues:
                    result.add_error(f"Config path validation failed: {issue.message}")
        
        return result
    
    def validate_config_save(self, config_data: Dict[str, Any]) -> ServiceValidationResult:
        """Validate configuration saving.
        
        Args:
            config_data: Configuration data to save
            
        Returns:
            Validation result
        """
        result = self.create_result("save_config")
        
        if not config_data:
            result.add_error("Configuration data is required")
            return result
        
        # Validate configuration structure
        config_result = ConfigValidator.validate_download_config(config_data)
        if not config_result.is_valid:
            for issue in config_result.issues:
                result.add_error(f"Config validation failed: {issue.message}")
        
        return result
    
    def validate_setting_update(self, update_data: Dict[str, Any]) -> ServiceValidationResult:
        """Validate setting update.
        
        Args:
            update_data: Setting update data
            
        Returns:
            Validation result
        """
        result = self.create_result("update_setting")
        
        required_fields = ['key', 'value']
        for field in required_fields:
            if field not in update_data:
                result.add_error(f"Missing required field: {field}")
        
        return result
