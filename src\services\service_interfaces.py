#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Interfaces for YouTube Downloader V2

This module defines the core service interfaces for the YouTube Downloader V2 application.
These interfaces establish the contract that service implementations must follow.
"""

from abc import ABC, abstractmethod
from enum import Enum, auto
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass
import datetime


# Domain Models

class VideoQuality(Enum):
    """Video quality options."""
    LOW = auto()
    MEDIUM = auto()
    HIGH = auto()
    VERY_HIGH = auto()


class DownloadStatus(Enum):
    """Download task status."""
    QUEUED = auto()
    DOWNLOADING = auto()
    PAUSED = auto()
    COMPLETED = auto()
    FAILED = auto()
    CANCELED = auto()


@dataclass
class VideoInfo:
    """Video information."""
    video_id: str
    title: str
    description: str
    duration: int  # in seconds
    thumbnail_url: str
    author: str
    view_count: Optional[int] = None
    upload_date: Optional[datetime.date] = None
    available_formats: Optional[List[VideoQuality]] = None


@dataclass
class ProgressInfo:
    """Download progress information."""
    bytes_downloaded: int
    total_bytes: int
    speed: float  # bytes per second
    eta: int  # seconds
    status: DownloadStatus
    progress_percentage: float
    time_elapsed: int  # seconds

    @property
    def is_complete(self) -> bool:
        """Check if download is complete."""
        return self.status == DownloadStatus.COMPLETED


@dataclass
class DownloadConfig:
    """Download configuration."""
    download_dir: Path
    default_format: VideoQuality
    max_concurrent_downloads: int
    use_proxy: bool = False
    proxy_url: Optional[str] = None
    throttle_speed: Optional[int] = None  # bytes per second
    auto_resume: bool = True


@dataclass
class DownloadTask:
    """Download task information."""
    url: str
    destination: Path
    quality: VideoQuality
    video_info: Optional[VideoInfo] = None
    task_id: Optional[str] = None
    status: DownloadStatus = DownloadStatus.QUEUED
    progress: Optional[ProgressInfo] = None
    created_at: datetime.datetime = datetime.datetime.now()


@dataclass
class SizeInfo:
    """File size information."""
    bytes: int
    formatted: str  # human-readable format


# Service Interfaces

class IYouTubeService(ABC):
    """YouTube service interface."""
    
    @abstractmethod
    async def extract_video_info(self, url: str) -> VideoInfo:
        """Extract video information from YouTube URL."""
        pass
    
    @abstractmethod
    async def get_available_formats(self, video_id: str) -> List[VideoQuality]:
        """Get available download formats for a video."""
        pass
    
    @abstractmethod
    async def validate_url(self, url: str) -> bool:
        """Validate if URL is a valid YouTube URL."""
        pass
    
    @abstractmethod
    async def extract_playlist_info(self, url: str) -> List[VideoInfo]:
        """Extract playlist information."""
        pass


class IDownloadService(ABC):
    """Download service interface."""
    
    @abstractmethod
    async def start_download(self, task: DownloadTask) -> str:
        """Start a download task and return task ID."""
        pass
    
    @abstractmethod
    async def pause_download(self, task_id: str) -> bool:
        """Pause a running download."""
        pass
    
    @abstractmethod
    async def resume_download(self, task_id: str) -> bool:
        """Resume a paused download."""
        pass
    
    @abstractmethod
    async def cancel_download(self, task_id: str) -> bool:
        """Cancel a download."""
        pass
    
    @abstractmethod
    async def get_progress(self, task_id: str) -> Optional[ProgressInfo]:
        """Get download progress."""
        pass
    
    @abstractmethod
    async def get_all_tasks(self) -> List[DownloadTask]:
        """Get all download tasks."""
        pass
    
    @abstractmethod
    async def get_task(self, task_id: str) -> Optional[DownloadTask]:
        """Get a specific download task."""
        pass


class IFileService(ABC):
    """File service interface."""
    
    @abstractmethod
    async def create_directory(self, path: Path) -> bool:
        """Create directory if it doesn't exist."""
        pass
    
    @abstractmethod
    async def validate_path(self, path: Path) -> bool:
        """Validate if path is valid and writable."""
        pass
    
    @abstractmethod
    async def get_file_size(self, path: Path) -> Optional[SizeInfo]:
        """Get file size information."""
        pass
    
    @abstractmethod
    async def move_file(self, source: Path, destination: Path) -> bool:
        """Move file from source to destination."""
        pass
    
    @abstractmethod
    async def delete_file(self, path: Path) -> bool:
        """Delete file."""
        pass
    
    @abstractmethod
    async def list_files(self, directory: Path, pattern: Optional[str] = None) -> List[Path]:
        """List files in directory with optional pattern matching."""
        pass
    
    @abstractmethod
    async def ensure_long_path_support(self) -> bool:
        """Ensure support for long paths on Windows."""
        pass


class IConfigService(ABC):
    """Config service interface."""
    
    @abstractmethod
    async def load_config(self) -> DownloadConfig:
        """Load configuration from file."""
        pass
    
    @abstractmethod
    async def save_config(self, config: DownloadConfig) -> bool:
        """Save configuration to file."""
        pass
    
    @abstractmethod
    async def get_setting(self, key: str) -> Optional[Any]:
        """Get specific setting value."""
        pass
    
    @abstractmethod
    async def update_setting(self, key: str, value: Any) -> bool:
        """Update specific setting."""
        pass
    
    @abstractmethod
    async def reset_to_defaults(self) -> bool:
        """Reset configuration to default values."""
        pass
    
    @abstractmethod
    async def get_config_file_path(self) -> Path:
        """Get the path to the configuration file."""
        pass


# Service Factory Interface

class ILoggingService(ABC):
    """Logging service interface."""
    
    @abstractmethod
    async def setup(self, 
                   log_level: str,
                   log_format: str,
                   log_destination: str,
                   log_dir: Optional[Union[str, Path]] = None) -> None:
        """Set up logging configuration.
        
        Args:
            log_level: Logging level
            log_format: Logging format (text or JSON)
            log_destination: Where to send logs (console, file, or both)
            log_dir: Directory for log files (required if destination includes file)
        """
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown logging system and remove handlers."""
        pass
    
    @abstractmethod
    def get_logger(self, name: str) -> Any:
        """Get a named logger.
        
        Args:
            name: Logger name, typically the module name
            
        Returns:
            Logger instance
        """
        pass
    
    @abstractmethod
    def add_context(self, **context) -> None:
        """Add context to all log records.
        
        Args:
            **context: Key-value pairs to add to log context
        """
        pass


class IServiceFactory(ABC):
    """Service factory interface."""
    
    @abstractmethod
    def create_youtube_service(self) -> IYouTubeService:
        """Create YouTube service instance."""
        pass
    
    @abstractmethod
    def create_download_service(self) -> IDownloadService:
        """Create download service instance."""
        pass
    
    @abstractmethod
    def create_file_service(self) -> IFileService:
        """Create file service instance."""
        pass
    
    @abstractmethod
    def create_config_service(self) -> IConfigService:
        """Create config service instance."""
        pass
    
    @abstractmethod
    def create_logging_service(self) -> ILoggingService:
        """Create logging service instance."""
        pass