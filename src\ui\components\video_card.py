#!/usr/bin/env python3
"""
Video Card Component

This module defines a reusable video card component for displaying video information.
"""

import sys
from typing import Dict, Any

try:
    from PyQt6.QtWidgets import (
        QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton,
        QFrame, QSizePolicy, QMenu
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QSize, QUrl
    from PyQt6.QtGui import QPixmap, QImage, QDesktopServices, QCursor
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton,
            QFrame, QSizePolicy, QMenu
        )
        from PySide6.QtCore import Qt, Signal as pyqtSignal, QSize, QUrl
        from PySide6.QtGui import QPixmap, QImage, QDesktopServices, QCursor
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required.")
        sys.exit(1)

try:
    import qtawesome as qta
    HAS_QTAWESOME = True
except ImportError:
    HAS_QTAWESOME = False


class VideoCard(QFrame):
    """A card component for displaying video information."""
    
    # Signals
    video_selected = pyqtSignal(dict)
    download_requested = pyqtSignal(dict)
    
    def __init__(self, video_data: Dict[str, Any]):
        """Initialize the video card.
        
        Args:
            video_data: Dictionary containing video information
        """
        super().__init__()
        self.video_data = video_data
        
        # Set frame style
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        
        # Initialize UI components
        self._init_ui()
        
        # Connect signals and slots
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.layout = QHBoxLayout(self)
        
        # Thumbnail
        self._create_thumbnail()
        
        # Video info
        self._create_video_info()
        
        # Action buttons
        self._create_action_buttons()
    
    def _create_thumbnail(self):
        """Create the video thumbnail."""
        # Thumbnail container
        thumbnail_container = QWidget()
        thumbnail_container.setFixedSize(120, 90)
        thumbnail_layout = QVBoxLayout(thumbnail_container)
        thumbnail_layout.setContentsMargins(0, 0, 0, 0)
        
        # Thumbnail label
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(120, 90)
        self.thumbnail_label.setScaledContents(True)
        
        # Set placeholder image
        placeholder = QPixmap(120, 90)
        placeholder.fill(Qt.GlobalColor.lightGray)
        self.thumbnail_label.setPixmap(placeholder)
        
        # TODO: Load actual thumbnail from URL
        # This would typically be done in a worker thread
        # For now, we'll just use a placeholder
        
        thumbnail_layout.addWidget(self.thumbnail_label)
        self.layout.addWidget(thumbnail_container)
    
    def _create_video_info(self):
        """Create the video information section."""
        # Info container
        info_container = QWidget()
        info_layout = QVBoxLayout(info_container)
        
        # Title
        self.title_label = QLabel(self.video_data.get('title', 'Unknown Title'))
        self.title_label.setWordWrap(True)
        self.title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        info_layout.addWidget(self.title_label)
        
        # Channel
        self.channel_label = QLabel(f"Channel: {self.video_data.get('channel_title', 'Unknown')}")
        info_layout.addWidget(self.channel_label)
        
        # Duration and views
        stats_layout = QHBoxLayout()
        
        # Duration
        duration = self.video_data.get('duration', 'Unknown')
        self.duration_label = QLabel(f"Duration: {duration}")
        stats_layout.addWidget(self.duration_label)
        
        # Views
        views = self.video_data.get('view_count', 'Unknown')
        self.views_label = QLabel(f"Views: {views}")
        stats_layout.addWidget(self.views_label)
        
        # Add stats layout to info layout
        info_layout.addLayout(stats_layout)
        
        # Description
        description = self.video_data.get('description', '')
        if description:
            # Truncate description if too long
            if len(description) > 100:
                description = description[:97] + '...'
            
            self.description_label = QLabel(description)
            self.description_label.setWordWrap(True)
            self.description_label.setStyleSheet("color: #666;")
            info_layout.addWidget(self.description_label)
        
        # Add stretch to push everything to the top
        info_layout.addStretch()
        
        self.layout.addWidget(info_container, 1)  # 1 = stretch factor
    
    def _create_action_buttons(self):
        """Create the action buttons."""
        # Buttons container
        buttons_container = QWidget()
        buttons_layout = QVBoxLayout(buttons_container)
        
        # Download button
        self.download_button = QPushButton("Download")
        if HAS_QTAWESOME:
            self.download_button.setIcon(qta.icon("fa5s.download"))
        buttons_layout.addWidget(self.download_button)
        
        # Watch button
        self.watch_button = QPushButton("Watch")
        if HAS_QTAWESOME:
            self.watch_button.setIcon(qta.icon("fa5s.play"))
        buttons_layout.addWidget(self.watch_button)
        
        # Options button
        self.options_button = QPushButton("Options")
        if HAS_QTAWESOME:
            self.options_button.setIcon(qta.icon("fa5s.ellipsis-h"))
        buttons_layout.addWidget(self.options_button)
        
        # Add stretch to push buttons to the top
        buttons_layout.addStretch()
        
        self.layout.addWidget(buttons_container)
    
    def _connect_signals(self):
        """Connect signals and slots."""
        # Connect download button
        self.download_button.clicked.connect(self._on_download_clicked)
        
        # Connect watch button
        self.watch_button.clicked.connect(self._on_watch_clicked)
        
        # Connect options button
        self.options_button.clicked.connect(self._on_options_clicked)
        
        # Make the entire card clickable
        self.mousePressEvent = self._on_card_clicked
    
    def _on_download_clicked(self):
        """Handle download button click."""
        self.download_requested.emit(self.video_data)
    
    def _on_watch_clicked(self):
        """Handle watch button click."""
        video_id = self.video_data.get('video_id')
        if video_id:
            url = f"https://www.youtube.com/watch?v={video_id}"
            QDesktopServices.openUrl(QUrl(url))
    
    def _on_options_clicked(self):
        """Handle options button click."""
        # Create context menu
        menu = QMenu(self)
        
        # Add actions
        copy_link_action = menu.addAction("Copy Link")
        copy_title_action = menu.addAction("Copy Title")
        menu.addSeparator()
        add_to_playlist_action = menu.addAction("Add to Playlist")
        
        # Show menu at button position
        action = menu.exec(QCursor.pos())
        
        # Handle action selection
        if action == copy_link_action:
            # TODO: Implement copy link functionality
            pass
        elif action == copy_title_action:
            # TODO: Implement copy title functionality
            pass
        elif action == add_to_playlist_action:
            # TODO: Implement add to playlist functionality
            pass
    
    def _on_card_clicked(self, event):
        """Handle card click event.
        
        Args:
            event: Mouse press event
        """
        # Emit video selected signal
        self.video_selected.emit(self.video_data)
        
        # Call parent class implementation
        super().mousePressEvent(event)