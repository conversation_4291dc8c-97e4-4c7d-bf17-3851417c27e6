#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Concurrent Downloads Example of YouTube Downloader

This script demonstrates how to download multiple YouTube videos concurrently
and track their progress using the YouTube Downloader services.
"""

import asyncio
import os
import re
from pathlib import Path
from typing import Dict, List, Set
import time

from src.services.service_interfaces import VideoQuality, DownloadStatus, DownloadTask
from src.services.service_factory import AsyncServiceFactory


async def main():
    """Example of downloading multiple YouTube videos concurrently."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    youtube_service = await factory.create_youtube_service_async()
    download_service = await factory.create_download_service_async()
    file_service = await factory.create_file_service_async()
    config_service = await factory.create_config_service_async()
    
    # Load configuration
    config = await config_service.load_config()
    print(f"Download directory: {config.download_dir}")
    print(f"Maximum concurrent downloads: {config.max_concurrent_downloads}")
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    
    # Create a directory for concurrent downloads
    concurrent_dir = Path(config.download_dir) / "concurrent_downloads"
    await file_service.create_directory(concurrent_dir)
    
    # Example video URLs (short educational videos)
    video_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Astley - Never Gonna Give You Up
        "https://www.youtube.com/watch?v=jNQXAC9IVRw",  # Me at the zoo (first YouTube video)
        "https://www.youtube.com/watch?v=TcMBFSGVi1c",  # Avengers: Endgame Trailer
        "https://www.youtube.com/watch?v=fJ9rUzIMcZQ",  # Queen - Bohemian Rhapsody
        "https://www.youtube.com/watch?v=kJQP7kiw5Fk"   # Luis Fonsi - Despacito
    ]
    
    # Validate URLs and prepare download tasks
    task_ids = []
    
    print("Preparing download tasks...")
    for url in video_urls:
        # Validate URL
        validation_result = await youtube_service.validate_url(url)
        if not validation_result.is_valid:
            print(f"Invalid URL: {url} - {validation_result.message}")
            continue
        
        try:
            # Extract video info
            print(f"Extracting info for {url}...")
            video_info = await youtube_service.extract_video_info(url)
            
            if not video_info:
                print(f"Failed to extract info for {url}")
                continue
            
            # Create safe filename
            filename = f"{create_safe_filename(video_info.title)}.mp4"
            destination = concurrent_dir / filename
            
            # Create download task
            task = DownloadTask(
                url=url,
                destination=str(destination),
                quality=VideoQuality.MEDIUM,  # You can change this to any quality
                video_info=video_info
            )
            
            # Start download
            task_id = await download_service.start_download(task)
            task_ids.append(task_id)
            print(f"Started download for '{video_info.title}' with task ID: {task_id}")
            
        except Exception as e:
            print(f"Error processing {url}: {e}")
    
    if not task_ids:
        print("No downloads were started. Exiting.")
        return
    
    # Monitor progress for all tasks
    await monitor_concurrent_downloads(download_service, task_ids)


def create_safe_filename(title: str) -> str:
    """Create a safe filename from a title.
    
    Args:
        title: The title to convert to a safe filename
        
    Returns:
        A safe filename
    """
    # Remove invalid characters
    safe_title = re.sub(r'[\\/*?:"<>|]', '', title)
    
    # Replace spaces with underscores
    safe_title = safe_title.replace(' ', '_')
    
    # Limit length
    if len(safe_title) > 100:
        safe_title = safe_title[:100]
        
    return safe_title


async def monitor_concurrent_downloads(download_service, task_ids: List[str]):
    """Monitor download progress for multiple concurrent tasks.
    
    Args:
        download_service: Download service instance
        task_ids: List of download task IDs
    """
    try:
        # Set to track completed tasks
        completed_tasks: Set[str] = set()
        start_time = time.time()
        
        # Monitor progress until all tasks are complete, failed, or canceled
        while len(completed_tasks) < len(task_ids):
            # Clear screen
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Print header
            elapsed_time = time.time() - start_time
            minutes = int(elapsed_time // 60)
            seconds = int(elapsed_time % 60)
            
            print(f"Monitoring {len(task_ids)} concurrent downloads:")
            print(f"Elapsed time: {minutes}m {seconds}s")
            print(f"Completed: {len(completed_tasks)}/{len(task_ids)}")
            print("-" * 80)
            
            # Get all tasks
            all_tasks = await download_service.get_all_tasks()
            
            # Create a dictionary of tasks by ID for easy lookup
            tasks_dict: Dict[str, DownloadTask] = {task.task_id: task for task in all_tasks if task.task_id in task_ids}
            
            # Calculate overall progress
            total_percentage = 0
            active_downloads = 0
            
            # Print progress for each task
            for task_id in task_ids:
                if task_id not in tasks_dict:
                    print(f"Task {task_id}: Not found")
                    completed_tasks.add(task_id)
                    continue
                    
                task = tasks_dict[task_id]
                progress = task.progress
                
                if not progress:
                    print(f"Task {task_id}: No progress information")
                    continue
                    
                # Get video title
                title = task.video_info.title if task.video_info else "Unknown"
                
                # Print progress
                status_text = progress.status.name
                if progress.status == DownloadStatus.DOWNLOADING:
                    active_downloads += 1
                    total_percentage += progress.progress_percentage
                    
                    # Calculate speed in MB/s
                    speed_mb = progress.speed / (1024 * 1024) if progress.speed else 0
                    
                    # Format ETA
                    eta_text = "Unknown"
                    if progress.eta is not None:
                        eta_min = progress.eta // 60
                        eta_sec = progress.eta % 60
                        eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                    
                    # Print progress bar
                    bar_length = 30
                    filled_length = int(bar_length * progress.progress_percentage / 100)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    
                    print(f"[{bar}] {progress.progress_percentage:.1f}% - {speed_mb:.2f} MB/s - ETA: {eta_text}")
                    print(f"Title: {title}")
                else:
                    # Print status for non-downloading states
                    print(f"Status: {status_text}")
                    print(f"Title: {title}")
                    
                print("-" * 80)
                
                # Check if download is complete, failed, or canceled
                if progress.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELED]:
                    completed_tasks.add(task_id)
            
            # Print overall progress
            if active_downloads > 0:
                overall_percentage = total_percentage / active_downloads
                bar_length = 50
                filled_length = int(bar_length * overall_percentage / 100)
                bar = '█' * filled_length + '░' * (bar_length - filled_length)
                
                print(f"Overall progress: [{bar}] {overall_percentage:.1f}%")
                print(f"Active downloads: {active_downloads}")
            
            # Wait before checking again
            await asyncio.sleep(1)
            
        # All downloads finished
        elapsed_time = time.time() - start_time
        minutes = int(elapsed_time // 60)
        seconds = int(elapsed_time % 60)
        
        print("All downloads finished!")
        print(f"Total time: {minutes}m {seconds}s")
        
        # Print final status for each task
        print("\nFinal status:")
        for task_id in task_ids:
            task = await download_service.get_task(task_id)
            if task and task.progress:
                status = task.progress.status.name
                title = task.video_info.title if task.video_info else "Unknown"
                print(f"{title}: {status}")
                
                if task.progress.status == DownloadStatus.FAILED and task.progress.error_message:
                    print(f"  Error: {task.progress.error_message}")
            else:
                print(f"Task {task_id}: Not found")
            
    except KeyboardInterrupt:
        # Cancel all downloads if user presses Ctrl+C
        print("\nCanceling all downloads...")
        for task_id in task_ids:
            try:
                await download_service.cancel_download(task_id)
            except Exception:
                pass
        print("All downloads canceled.")
        
    except Exception as e:
        print(f"Error monitoring downloads: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())