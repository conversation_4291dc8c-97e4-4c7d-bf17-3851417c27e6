#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Logging Utilities

This module provides utilities for setting up and managing application logging.
It integrates with the configuration system and supports file and console logging.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Union, Dict, Any
from datetime import datetime

# Import from config once it's available
try:
    from ..config import LogLevel, settings_manager
    CONFIG_AVAILABLE = True
except ImportError:
    # Define a fallback LogLevel enum if config is not available
    from enum import Enum, auto
    class LogLevel(str, Enum):
        DEBUG = "DEBUG"
        INFO = "INFO"
        WARNING = "WARNING"
        ERROR = "ERROR"
        CRITICAL = "CRITICAL"
    CONFIG_AVAILABLE = False


class LoggingUtilsException(Exception):
    """Base exception for logging utilities errors."""
    pass


class LoggingConfigError(LoggingUtilsException):
    """Raised when logging configuration is invalid."""
    pass


def _get_log_level(level: Union[str, LogLevel]) -> int:
    """Convert string or LogLevel enum to logging module level."""
    if isinstance(level, LogLevel):
        level_str = level.value
    else:
        level_str = str(level).upper()
    
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    
    if level_str not in level_map:
        raise LoggingConfigError(f"Invalid log level: {level}")
    
    return level_map[level_str]


def setup_logging(log_dir: Optional[Union[str, Path]] = None, 
                 log_level: Optional[Union[str, LogLevel]] = None,
                 log_to_console: bool = True,
                 log_file_prefix: str = "youtube_downloader",
                 max_log_files: int = 5,
                 max_log_size_mb: int = 10) -> logging.Logger:
    """Set up logging configuration.
    
    Args:
        log_dir: Directory for log files. If None, uses config setting.
        log_level: Logging level. If None, uses config setting.
        log_to_console: Whether to log to console.
        log_file_prefix: Prefix for log files.
        max_log_files: Maximum number of log files to keep.
        max_log_size_mb: Maximum size of each log file in MB.
        
    Returns:
        Configured root logger.
        
    Raises:
        LoggingConfigError: If configuration is invalid.
    """
    # Get settings from config if not provided and config is available
    if CONFIG_AVAILABLE:
        if log_dir is None:
            log_dir = settings_manager.get_setting("paths.log_dir")
        
        if log_level is None:
            log_level = settings_manager.get_setting("app.log_level")
    else:
        # Default values if config is not available
        if log_dir is None:
            log_dir = Path.home() / ".youtube_downloader" / "logs"
        
        if log_level is None:
            log_level = LogLevel.INFO
    
    # Ensure log directory exists
    log_dir = Path(log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Convert log level to logging constant
    log_level_int = _get_log_level(log_level)
    
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(log_level_int)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:  
        logger.removeHandler(handler)
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(levelname)s: %(message)s'
    )
    
    # Create file handler with rotation
    log_file = log_dir / f"{log_file_prefix}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        log_file, 
        maxBytes=max_log_size_mb * 1024 * 1024,  # Convert MB to bytes
        backupCount=max_log_files,
        encoding='utf-8'
    )
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # Create console handler if requested
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """Get a named logger.
    
    Args:
        name: Logger name, typically the module name.
        
    Returns:
        Named logger instance.
    """
    return logging.getLogger(name)


class LogCapture:
    """Context manager for capturing log messages during testing."""
    
    def __init__(self, logger_name: Optional[str] = None, level: Union[str, LogLevel] = LogLevel.DEBUG):
        """Initialize log capture.
        
        Args:
            logger_name: Name of logger to capture. If None, captures root logger.
            level: Minimum log level to capture.
        """
        self.logger_name = logger_name
        self.level = _get_log_level(level)
        self.handler = None
        self.messages = []
    
    def __enter__(self):
        """Start capturing logs."""
        logger = logging.getLogger(self.logger_name)
        
        class ListHandler(logging.Handler):
            def __init__(self, messages_list):
                super().__init__()
                self.messages_list = messages_list
            
            def emit(self, record):
                self.messages_list.append(self.format(record))
        
        self.handler = ListHandler(self.messages)
        self.handler.setLevel(self.level)
        self.handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
        logger.addHandler(self.handler)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Stop capturing logs."""
        if self.handler:
            logging.getLogger(self.logger_name).removeHandler(self.handler)
    
    def get_messages(self) -> list:
        """Get captured log messages."""
        return self.messages
    
    def contains(self, text: str, level: Optional[Union[str, LogLevel]] = None) -> bool:
        """Check if captured logs contain the specified text.
        
        Args:
            text: Text to search for in log messages.
            level: If provided, only search in messages of this level.
            
        Returns:
            True if text is found in logs, False otherwise.
        """
        if level is None:
            return any(text in message for message in self.messages)
        
        level_str = level.value if isinstance(level, LogLevel) else str(level).upper()
        return any(message.startswith(f"{level_str}:") and text in message 
                  for message in self.messages)