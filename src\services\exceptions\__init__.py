#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Exceptions Package

This package contains all service-specific exceptions for the YouTube Downloader application.
"""

from .service_exceptions import (
    ServiceException,
    ServiceValidationException,
    ServiceConfigurationException,
    ServiceConnectionException,
    ServiceTimeoutException,
    YouTubeServiceException,
    VideoExtractionException,
    PlaylistExtractionException,
    DownloadServiceException,
    TaskNotFoundException,
    InvalidTaskStateException,
    DownloadFailedException,
    FileServiceException,
    FileNotFoundException,
    FilePermissionException,
    DiskSpaceException,
    ConfigServiceException,
    ConfigNotFoundException,
    ConfigValidationException,
    LoggingServiceException
)

__all__ = [
    'ServiceException',
    'ServiceValidationException',
    'ServiceConfigurationException',
    'ServiceConnectionException',
    'ServiceTimeoutException',
    'YouTubeServiceException',
    'VideoExtractionException',
    'PlaylistExtractionException',
    'DownloadServiceException',
    'TaskNotFoundException',
    'InvalidTaskStateException',
    'DownloadFailedException',
    'FileServiceException',
    'FileNotFoundException',
    'FilePermissionException',
    'DiskSpaceException',
    'ConfigServiceException',
    'ConfigNotFoundException',
    'ConfigValidationException',
    'LoggingServiceException'
]
