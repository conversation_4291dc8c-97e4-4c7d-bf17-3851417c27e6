#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Progress Tracking Utilities

This module provides utilities for tracking and managing download progress,
including progress calculation, event handling, and persistence.
"""

import asyncio
import json
import time
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any, Union
from collections import deque


@dataclass
class ProgressEvent:
    """Represents a progress event."""
    task_id: str
    timestamp: datetime
    bytes_downloaded: int
    total_bytes: Optional[int]
    speed_bps: float
    eta_seconds: Optional[int]
    status: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'task_id': self.task_id,
            'timestamp': self.timestamp.isoformat(),
            'bytes_downloaded': self.bytes_downloaded,
            'total_bytes': self.total_bytes,
            'speed_bps': self.speed_bps,
            'eta_seconds': self.eta_seconds,
            'status': self.status
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProgressEvent':
        """Create from dictionary."""
        return cls(
            task_id=data['task_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            bytes_downloaded=data['bytes_downloaded'],
            total_bytes=data.get('total_bytes'),
            speed_bps=data['speed_bps'],
            eta_seconds=data.get('eta_seconds'),
            status=data['status']
        )


# Type alias for progress callback functions
ProgressCallback = Callable[[ProgressEvent], None]


class ProgressEstimator:
    """Estimates download progress and ETA based on historical data."""
    
    def __init__(self, window_size: int = 10):
        """Initialize progress estimator.
        
        Args:
            window_size: Number of recent samples to use for calculations
        """
        self.window_size = window_size
        self.speed_samples: deque = deque(maxlen=window_size)
        self.time_samples: deque = deque(maxlen=window_size)
        self.bytes_samples: deque = deque(maxlen=window_size)
    
    def add_sample(self, bytes_downloaded: int, timestamp: Optional[datetime] = None):
        """Add a progress sample for calculation.
        
        Args:
            bytes_downloaded: Number of bytes downloaded
            timestamp: When the sample was taken (defaults to now)
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        self.bytes_samples.append(bytes_downloaded)
        self.time_samples.append(timestamp)
        
        # Calculate speed if we have at least 2 samples
        if len(self.bytes_samples) >= 2:
            time_diff = (self.time_samples[-1] - self.time_samples[-2]).total_seconds()
            bytes_diff = self.bytes_samples[-1] - self.bytes_samples[-2]
            
            if time_diff > 0:
                speed = bytes_diff / time_diff
                self.speed_samples.append(speed)
    
    def get_average_speed(self) -> float:
        """Get average download speed in bytes per second."""
        if not self.speed_samples:
            return 0.0
        return sum(self.speed_samples) / len(self.speed_samples)
    
    def get_current_speed(self) -> float:
        """Get current download speed in bytes per second."""
        if not self.speed_samples:
            return 0.0
        return self.speed_samples[-1]
    
    def estimate_eta(self, bytes_downloaded: int, total_bytes: Optional[int]) -> Optional[int]:
        """Estimate time remaining in seconds.
        
        Args:
            bytes_downloaded: Current bytes downloaded
            total_bytes: Total bytes to download
            
        Returns:
            Estimated seconds remaining, or None if cannot estimate
        """
        if not total_bytes or total_bytes <= bytes_downloaded:
            return None
        
        avg_speed = self.get_average_speed()
        if avg_speed <= 0:
            return None
        
        remaining_bytes = total_bytes - bytes_downloaded
        return int(remaining_bytes / avg_speed)


class ProgressPersistence:
    """Handles persistence of progress data to disk."""
    
    def __init__(self, storage_path: Path):
        """Initialize progress persistence.
        
        Args:
            storage_path: Path to store progress data
        """
        self.storage_path = storage_path
        self.storage_path.mkdir(parents=True, exist_ok=True)
    
    def save_progress(self, task_id: str, event: ProgressEvent) -> bool:
        """Save progress event to disk.
        
        Args:
            task_id: Task identifier
            event: Progress event to save
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            file_path = self.storage_path / f"{task_id}_progress.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(event.to_dict(), f, indent=2)
            return True
        except Exception:
            return False
    
    def load_progress(self, task_id: str) -> Optional[ProgressEvent]:
        """Load progress event from disk.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Progress event if found, None otherwise
        """
        try:
            file_path = self.storage_path / f"{task_id}_progress.json"
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return ProgressEvent.from_dict(data)
        except Exception:
            return None
    
    def delete_progress(self, task_id: str) -> bool:
        """Delete progress data for a task.
        
        Args:
            task_id: Task identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            file_path = self.storage_path / f"{task_id}_progress.json"
            if file_path.exists():
                file_path.unlink()
            return True
        except Exception:
            return False


class ProgressTracker:
    """Main progress tracking class that coordinates all progress-related functionality."""
    
    def __init__(self, storage_path: Optional[Path] = None):
        """Initialize progress tracker.
        
        Args:
            storage_path: Path for progress persistence (optional)
        """
        self.estimators: Dict[str, ProgressEstimator] = {}
        self.callbacks: List[ProgressCallback] = []
        self.persistence = ProgressPersistence(storage_path) if storage_path else None
        self.last_events: Dict[str, ProgressEvent] = {}
    
    def register_callback(self, callback: ProgressCallback):
        """Register a progress callback function.
        
        Args:
            callback: Function to call when progress updates
        """
        if callback not in self.callbacks:
            self.callbacks.append(callback)
    
    def unregister_callback(self, callback: ProgressCallback):
        """Unregister a progress callback function.
        
        Args:
            callback: Function to remove from callbacks
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def update_progress(self, task_id: str, bytes_downloaded: int, 
                       total_bytes: Optional[int] = None, 
                       status: str = "downloading") -> ProgressEvent:
        """Update progress for a task and notify callbacks.
        
        Args:
            task_id: Task identifier
            bytes_downloaded: Current bytes downloaded
            total_bytes: Total bytes to download (optional)
            status: Current download status
            
        Returns:
            Progress event that was created
        """
        # Get or create estimator for this task
        if task_id not in self.estimators:
            self.estimators[task_id] = ProgressEstimator()
        
        estimator = self.estimators[task_id]
        timestamp = datetime.now()
        
        # Add sample to estimator
        estimator.add_sample(bytes_downloaded, timestamp)
        
        # Calculate speed and ETA
        speed_bps = estimator.get_current_speed()
        eta_seconds = estimator.estimate_eta(bytes_downloaded, total_bytes)
        
        # Create progress event
        event = ProgressEvent(
            task_id=task_id,
            timestamp=timestamp,
            bytes_downloaded=bytes_downloaded,
            total_bytes=total_bytes,
            speed_bps=speed_bps,
            eta_seconds=eta_seconds,
            status=status
        )
        
        # Store last event
        self.last_events[task_id] = event
        
        # Persist if enabled
        if self.persistence:
            self.persistence.save_progress(task_id, event)
        
        # Notify callbacks
        for callback in self.callbacks:
            try:
                callback(event)
            except Exception:
                # Don't let callback errors break progress tracking
                pass
        
        return event
    
    def get_last_progress(self, task_id: str) -> Optional[ProgressEvent]:
        """Get the last progress event for a task.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Last progress event, or None if not found
        """
        # Try memory first
        if task_id in self.last_events:
            return self.last_events[task_id]
        
        # Try persistence if available
        if self.persistence:
            return self.persistence.load_progress(task_id)
        
        return None
    
    def cleanup_task(self, task_id: str):
        """Clean up tracking data for a completed/cancelled task.
        
        Args:
            task_id: Task identifier
        """
        # Remove from memory
        self.estimators.pop(task_id, None)
        self.last_events.pop(task_id, None)
        
        # Remove from persistence
        if self.persistence:
            self.persistence.delete_progress(task_id)
    
    def get_all_active_tasks(self) -> List[str]:
        """Get list of all tasks being tracked.
        
        Returns:
            List of task IDs
        """
        return list(self.estimators.keys())
