import unittest
from pathlib import Path

from src.core.domain.exceptions.domain_exceptions import (
    DomainException,
    ValidationException,
    VideoInfoException,
    DownloadException,
    FileSystemException
)

class TestDomainExceptions(unittest.TestCase):
    def test_domain_exception(self):
        # Test basic exception
        ex = DomainException("General domain error")
        self.assertEqual(str(ex), "General domain error")
        self.assertEqual(ex.code, "domain_error")
        
        # Test with custom code
        ex = DomainException("Custom error", code="custom_code")
        self.assertEqual(str(ex), "Custom error")
        self.assertEqual(ex.code, "custom_code")
    
    def test_validation_exception(self):
        # Test basic validation exception
        ex = ValidationException("Invalid value")
        self.assertEqual(str(ex), "Invalid value")
        self.assertEqual(ex.code, "validation_error")
        self.assertIsNone(ex.field)
        
        # Test with field
        ex = ValidationException("Invalid URL", field="url")
        self.assertEqual(str(ex), "Invalid URL")
        self.assertEqual(ex.field, "url")
        
        # Test with custom code
        ex = ValidationException("Invalid format", field="format", code="invalid_format")
        self.assertEqual(str(ex), "Invalid format")
        self.assertEqual(ex.field, "format")
        self.assertEqual(ex.code, "invalid_format")
    
    def test_video_info_exception(self):
        # Test basic video info exception
        ex = VideoInfoException("Video not found")
        self.assertEqual(str(ex), "Video not found")
        self.assertEqual(ex.code, "video_info_error")
        self.assertIsNone(ex.video_id)
        
        # Test with video_id
        ex = VideoInfoException("Video not available", video_id="dQw4w9WgXcQ")
        self.assertEqual(str(ex), "Video not available")
        self.assertEqual(ex.video_id, "dQw4w9WgXcQ")
        
        # Test with custom code
        ex = VideoInfoException(
            "Video is private", 
            video_id="dQw4w9WgXcQ", 
            code="video_private"
        )
        self.assertEqual(str(ex), "Video is private")
        self.assertEqual(ex.video_id, "dQw4w9WgXcQ")
        self.assertEqual(ex.code, "video_private")
    
    def test_download_exception(self):
        # Test basic download exception
        ex = DownloadException("Download failed")
        self.assertEqual(str(ex), "Download failed")
        self.assertEqual(ex.code, "download_error")
        self.assertIsNone(ex.task_id)
        
        # Test with task_id
        ex = DownloadException("Connection timeout", task_id="task-123")
        self.assertEqual(str(ex), "Connection timeout")
        self.assertEqual(ex.task_id, "task-123")
        
        # Test with custom code
        ex = DownloadException(
            "Network error", 
            task_id="task-123", 
            code="network_error"
        )
        self.assertEqual(str(ex), "Network error")
        self.assertEqual(ex.task_id, "task-123")
        self.assertEqual(ex.code, "network_error")
    
    def test_file_system_exception(self):
        # Test basic file system exception
        ex = FileSystemException("File not found")
        self.assertEqual(str(ex), "File not found")
        self.assertEqual(ex.code, "file_system_error")
        self.assertIsNone(ex.path)
        
        # Test with path
        path = Path("D:/Downloads/video.mp4")
        ex = FileSystemException("Permission denied", path=path)
        self.assertEqual(str(ex), "Permission denied")
        self.assertEqual(ex.path, path)
        
        # Test with string path
        ex = FileSystemException("Disk full", path="D:/Downloads/video.mp4")
        self.assertEqual(str(ex), "Disk full")
        self.assertEqual(ex.path, Path("D:/Downloads/video.mp4"))
        
        # Test with custom code
        ex = FileSystemException(
            "Invalid filename", 
            path=Path("D:/Downloads/invalid<>file.mp4"), 
            code="invalid_filename"
        )
        self.assertEqual(str(ex), "Invalid filename")
        self.assertEqual(ex.path, Path("D:/Downloads/invalid<>file.mp4"))
        self.assertEqual(ex.code, "invalid_filename")

if __name__ == "__main__":
    unittest.main()