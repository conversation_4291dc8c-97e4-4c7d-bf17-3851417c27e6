#!/usr/bin/env python3
"""
Download Widget Module

This module defines the download widget for managing video downloads.
"""

import sys
from typing import Dict, Any, List

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar,
        QComboBox, QFrame, QSplitter, QMenu, QDialog, QFormLayout,
        QLineEdit, QDialogButtonBox, QCheckBox, QSpinBox
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QSize
    from PyQt6.QtGui import QIcon, QAction
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
            QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar,
            QComboBox, QFrame, QSplitter, QMenu, QDialog, QFormLayout,
            QLineEdit, QDialogButtonBox, QCheckBox, QSpinBox
        )
        from PySide6.QtCore import Qt, Signal as pyqtSignal, QSize
        from PySide6.QtGui import QIcon, QAction
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required.")
        sys.exit(1)

try:
    import qtawesome as qta
    HAS_QTAWESOME = True
except ImportError:
    HAS_QTAWESOME = False

from src.config.app_config import AppConfig
from src.services.service_factory import ServiceFactory
from src.services.download_service import DownloadService
from src.ui.components.progress_bar import ProgressBar
from src.ui.utils.ui_validation import validate_download_options


class DownloadOptionsDialog(QDialog):
    """Dialog for configuring download options."""
    
    def __init__(self, parent=None):
        """Initialize the download options dialog.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Download Options")
        self.setMinimumWidth(400)
        
        # Initialize UI components
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        
        # Form layout for options
        form_layout = QFormLayout()
        
        # Format selection
        self.format_combo = QComboBox()
        self.format_combo.addItems(["Best Quality", "MP4", "MP3 (Audio Only)", "Custom"])
        form_layout.addRow("Format:", self.format_combo)
        
        # Output directory
        self.output_dir = QLineEdit()
        self.output_dir.setPlaceholderText("Default download directory")
        browse_button = QPushButton("Browse")
        
        output_layout = QHBoxLayout()
        output_layout.addWidget(self.output_dir)
        output_layout.addWidget(browse_button)
        form_layout.addRow("Save to:", output_layout)
        
        # Filename template
        self.filename_template = QLineEdit()
        self.filename_template.setPlaceholderText("%(title)s-%(id)s.%(ext)s")
        form_layout.addRow("Filename template:", self.filename_template)
        
        # Quality options (for video)
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["1080p", "720p", "480p", "360p", "240p", "144p"])
        form_layout.addRow("Video quality:", self.quality_combo)
        
        # Audio quality (for audio)
        self.audio_quality_combo = QComboBox()
        self.audio_quality_combo.addItems(["Best", "High", "Medium", "Low"])
        form_layout.addRow("Audio quality:", self.audio_quality_combo)
        
        # Advanced options
        advanced_frame = QFrame()
        advanced_frame.setFrameShape(QFrame.Shape.StyledPanel)
        advanced_layout = QVBoxLayout(advanced_frame)
        
        # Concurrent downloads
        concurrent_layout = QHBoxLayout()
        self.concurrent_check = QCheckBox("Limit concurrent downloads")
        self.concurrent_spin = QSpinBox()
        self.concurrent_spin.setRange(1, 10)
        self.concurrent_spin.setValue(3)
        self.concurrent_spin.setEnabled(False)
        concurrent_layout.addWidget(self.concurrent_check)
        concurrent_layout.addWidget(self.concurrent_spin)
        advanced_layout.addLayout(concurrent_layout)
        
        # Connect concurrent check
        self.concurrent_check.stateChanged.connect(
            lambda state: self.concurrent_spin.setEnabled(state == Qt.CheckState.Checked)
        )
        
        # Other options
        self.subtitle_check = QCheckBox("Download subtitles if available")
        advanced_layout.addWidget(self.subtitle_check)
        
        self.thumbnail_check = QCheckBox("Download thumbnail")
        advanced_layout.addWidget(self.thumbnail_check)
        
        self.metadata_check = QCheckBox("Embed metadata")
        self.metadata_check.setChecked(True)
        advanced_layout.addWidget(self.metadata_check)
        
        form_layout.addRow("Advanced:", advanced_frame)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_options(self) -> Dict[str, Any]:
        """Get the selected download options.
        
        Returns:
            Dictionary containing download options
        """
        options = {
            'format': self.format_combo.currentText(),
            'output_dir': self.output_dir.text() if self.output_dir.text() else None,
            'filename_template': self.filename_template.text() if self.filename_template.text() else None,
            'video_quality': self.quality_combo.currentText(),
            'audio_quality': self.audio_quality_combo.currentText(),
            'limit_concurrent': self.concurrent_check.isChecked(),
            'max_concurrent': self.concurrent_spin.value() if self.concurrent_check.isChecked() else None,
            'download_subtitles': self.subtitle_check.isChecked(),
            'download_thumbnail': self.thumbnail_check.isChecked(),
            'embed_metadata': self.metadata_check.isChecked()
        }
        
        # Remove None values
        return {k: v for k, v in options.items() if v is not None}


class DownloadWidget(QWidget):
    """Widget for managing video downloads."""
    
    # Signals
    download_started = pyqtSignal(dict)
    download_progress = pyqtSignal(str, float)
    download_completed = pyqtSignal(str)
    download_error = pyqtSignal(str, str)
    
    def __init__(self, config: AppConfig):
        """Initialize the download widget.
        
        Args:
            config: Application configuration
        """
        super().__init__()
        self.config = config
        
        # Initialize services
        self.service_factory = ServiceFactory()
        self.download_service = self.service_factory.get_service('download')
        
        # Track active downloads
        self.active_downloads = {}
        
        # Initialize UI components
        self._init_ui()
        
        # Connect signals and slots
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.layout = QVBoxLayout(self)
        
        # Control buttons
        self._create_control_buttons()
        
        # Downloads table
        self._create_downloads_table()
        
        # Status label
        self.status_label = QLabel("No active downloads")
        self.layout.addWidget(self.status_label)
    
    def _create_control_buttons(self):
        """Create the control buttons."""
        buttons_layout = QHBoxLayout()
        
        # Add URL button
        self.add_url_button = QPushButton("Add URL")
        if HAS_QTAWESOME:
            self.add_url_button.setIcon(qta.icon("fa5s.plus"))
        buttons_layout.addWidget(self.add_url_button)
        
        # Pause all button
        self.pause_all_button = QPushButton("Pause All")
        if HAS_QTAWESOME:
            self.pause_all_button.setIcon(qta.icon("fa5s.pause"))
        buttons_layout.addWidget(self.pause_all_button)
        
        # Resume all button
        self.resume_all_button = QPushButton("Resume All")
        if HAS_QTAWESOME:
            self.resume_all_button.setIcon(qta.icon("fa5s.play"))
        buttons_layout.addWidget(self.resume_all_button)
        
        # Cancel all button
        self.cancel_all_button = QPushButton("Cancel All")
        if HAS_QTAWESOME:
            self.cancel_all_button.setIcon(qta.icon("fa5s.times"))
        buttons_layout.addWidget(self.cancel_all_button)
        
        # Options button
        self.options_button = QPushButton("Options")
        if HAS_QTAWESOME:
            self.options_button.setIcon(qta.icon("fa5s.cog"))
        buttons_layout.addWidget(self.options_button)
        
        self.layout.addLayout(buttons_layout)
    
    def _create_downloads_table(self):
        """Create the downloads table."""
        # Table widget
        self.downloads_table = QTableWidget()
        self.downloads_table.setColumnCount(5)
        self.downloads_table.setHorizontalHeaderLabels(["Title", "Status", "Progress", "Speed", "Actions"])
        
        # Set column widths
        header = self.downloads_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Title
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Status
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Progress
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Speed
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Actions
        
        self.layout.addWidget(self.downloads_table)
    
    def _connect_signals(self):
        """Connect signals and slots."""
        # Connect add URL button
        self.add_url_button.clicked.connect(self._on_add_url_clicked)
        
        # Connect pause all button
        self.pause_all_button.clicked.connect(self._on_pause_all_clicked)
        
        # Connect resume all button
        self.resume_all_button.clicked.connect(self._on_resume_all_clicked)
        
        # Connect cancel all button
        self.cancel_all_button.clicked.connect(self._on_cancel_all_clicked)
        
        # Connect options button
        self.options_button.clicked.connect(self._on_options_clicked)
        
        # Connect download signals
        self.download_started.connect(self._on_download_started)
        self.download_progress.connect(self._on_download_progress)
        self.download_completed.connect(self._on_download_completed)
        self.download_error.connect(self._on_download_error)
    
    def _on_add_url_clicked(self):
        """Handle add URL button click."""
        # Create a simple dialog to get the URL
        dialog = QDialog(self)
        dialog.setWindowTitle("Add Download URL")
        dialog.setMinimumWidth(400)
        
        layout = QVBoxLayout(dialog)
        
        # URL input
        url_layout = QHBoxLayout()
        url_label = QLabel("URL:")
        url_input = QLineEdit()
        url_layout.addWidget(url_label)
        url_layout.addWidget(url_input)
        layout.addLayout(url_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        
        # Show dialog
        if dialog.exec() == QDialog.DialogCode.Accepted:
            url = url_input.text().strip()
            if url:
                self._show_download_options(url)
    
    def _show_download_options(self, url: str):
        """Show download options dialog and start download if accepted.
        
        Args:
            url: URL to download
        """
        # Validate URL
        validation_result = validate_download_options({'url': url})
        if not validation_result['valid']:
            # Show error message
            QMessageBox.warning(self, "Invalid URL", validation_result['message'])
            return
        
        # Show options dialog
        options_dialog = DownloadOptionsDialog(self)
        if options_dialog.exec() == QDialog.DialogCode.Accepted:
            options = options_dialog.get_options()
            options['url'] = url
            
            # Start download
            self._start_download(options)
    
    def _start_download(self, options: Dict[str, Any]):
        """Start a download with the given options.
        
        Args:
            options: Dictionary containing download options
        """
        # This would typically be done in a worker thread
        # For now, we'll just call the service directly
        try:
            # Call the service
            download_id = self.download_service.start_download(options['url'], **options)
            
            # Emit download started signal
            self.download_started.emit({
                'id': download_id,
                'url': options['url'],
                'options': options
            })
        except Exception as e:
            # Emit download error signal
            self.download_error.emit(options['url'], str(e))
    
    def _on_pause_all_clicked(self):
        """Handle pause all button click."""
        # Pause all active downloads
        for download_id in self.active_downloads:
            self.download_service.pause_download(download_id)
        
        # Update status
        self.status_label.setText("All downloads paused")
    
    def _on_resume_all_clicked(self):
        """Handle resume all button click."""
        # Resume all active downloads
        for download_id in self.active_downloads:
            self.download_service.resume_download(download_id)
        
        # Update status
        self.status_label.setText("All downloads resumed")
    
    def _on_cancel_all_clicked(self):
        """Handle cancel all button click."""
        # Cancel all active downloads
        for download_id in self.active_downloads:
            self.download_service.cancel_download(download_id)
        
        # Clear the table
        self.downloads_table.setRowCount(0)
        self.active_downloads.clear()
        
        # Update status
        self.status_label.setText("All downloads cancelled")
    
    def _on_options_clicked(self):
        """Handle options button click."""
        # Show global download options dialog
        options_dialog = DownloadOptionsDialog(self)
        if options_dialog.exec() == QDialog.DialogCode.Accepted:
            options = options_dialog.get_options()
            
            # Update global download options
            # This would typically update the configuration
            pass
    
    def _on_download_started(self, download_info: Dict[str, Any]):
        """Handle download started event.
        
        Args:
            download_info: Dictionary containing download information
        """
        download_id = download_info['id']
        url = download_info['url']
        
        # Add to active downloads
        self.active_downloads[download_id] = download_info
        
        # Add to table
        row = self.downloads_table.rowCount()
        self.downloads_table.insertRow(row)
        
        # Set title (placeholder for now)
        title_item = QTableWidgetItem("Loading title...")
        self.downloads_table.setItem(row, 0, title_item)
        
        # Set status
        status_item = QTableWidgetItem("Starting...")
        self.downloads_table.setItem(row, 1, status_item)
        
        # Set progress
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 100)
        progress_bar.setValue(0)
        self.downloads_table.setCellWidget(row, 2, progress_bar)
        
        # Set speed
        speed_item = QTableWidgetItem("0 KB/s")
        self.downloads_table.setItem(row, 3, speed_item)
        
        # Set actions
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        
        # Pause button
        pause_button = QPushButton()
        if HAS_QTAWESOME:
            pause_button.setIcon(qta.icon("fa5s.pause"))
        pause_button.setFixedSize(24, 24)
        pause_button.clicked.connect(lambda: self._on_pause_clicked(download_id))
        actions_layout.addWidget(pause_button)
        
        # Cancel button
        cancel_button = QPushButton()
        if HAS_QTAWESOME:
            cancel_button.setIcon(qta.icon("fa5s.times"))
        cancel_button.setFixedSize(24, 24)
        cancel_button.clicked.connect(lambda: self._on_cancel_clicked(download_id))
        actions_layout.addWidget(cancel_button)
        
        self.downloads_table.setCellWidget(row, 4, actions_widget)
        
        # Store row index in download info
        self.active_downloads[download_id]['row'] = row
        
        # Update status
        self.status_label.setText(f"Started download: {url}")
    
    def _on_download_progress(self, download_id: str, progress: float):
        """Handle download progress event.
        
        Args:
            download_id: Download ID
            progress: Progress percentage (0-100)
        """
        if download_id not in self.active_downloads:
            return
        
        download_info = self.active_downloads[download_id]
        row = download_info['row']
        
        # Update progress bar
        progress_bar = self.downloads_table.cellWidget(row, 2)
        if progress_bar:
            progress_bar.setValue(int(progress))
        
        # Update status
        status_item = self.downloads_table.item(row, 1)
        if status_item:
            status_item.setText("Downloading...")
        
        # Update speed (placeholder for now)
        speed_item = self.downloads_table.item(row, 3)
        if speed_item:
            speed_item.setText("Calculating...")
        
        # Update status label for the most recent progress
        self.status_label.setText(f"Downloading: {progress:.1f}%")
    
    def _on_download_completed(self, download_id: str):
        """Handle download completed event.
        
        Args:
            download_id: Download ID
        """
        if download_id not in self.active_downloads:
            return
        
        download_info = self.active_downloads[download_id]
        row = download_info['row']
        
        # Update progress bar
        progress_bar = self.downloads_table.cellWidget(row, 2)
        if progress_bar:
            progress_bar.setValue(100)
        
        # Update status
        status_item = self.downloads_table.item(row, 1)
        if status_item:
            status_item.setText("Completed")
        
        # Update speed
        speed_item = self.downloads_table.item(row, 3)
        if speed_item:
            speed_item.setText("--")
        
        # Remove from active downloads
        del self.active_downloads[download_id]
        
        # Update status label
        self.status_label.setText(f"Download completed: {download_info['url']}")
    
    def _on_download_error(self, download_id: str, error_message: str):
        """Handle download error event.
        
        Args:
            download_id: Download ID
            error_message: Error message
        """
        if download_id not in self.active_downloads:
            return
        
        download_info = self.active_downloads[download_id]
        row = download_info['row']
        
        # Update status
        status_item = self.downloads_table.item(row, 1)
        if status_item:
            status_item.setText("Error")
        
        # Remove from active downloads
        del self.active_downloads[download_id]
        
        # Update status label
        self.status_label.setText(f"Download error: {error_message}")
    
    def _on_pause_clicked(self, download_id: str):
        """Handle pause button click for a specific download.
        
        Args:
            download_id: Download ID
        """
        if download_id not in self.active_downloads:
            return
        
        # Pause the download
        self.download_service.pause_download(download_id)
        
        # Update status
        row = self.active_downloads[download_id]['row']
        status_item = self.downloads_table.item(row, 1)
        if status_item:
            status_item.setText("Paused")
        
        # Update status label
        self.status_label.setText(f"Download paused: {self.active_downloads[download_id]['url']}")
    
    def _on_cancel_clicked(self, download_id: str):
        """Handle cancel button click for a specific download.
        
        Args:
            download_id: Download ID
        """
        if download_id not in self.active_downloads:
            return
        
        # Cancel the download
        self.download_service.cancel_download(download_id)
        
        # Remove from table
        row = self.active_downloads[download_id]['row']
        self.downloads_table.removeRow(row)
        
        # Update row indices for remaining downloads
        for d_id, info in self.active_downloads.items():
            if info['row'] > row:
                info['row'] -= 1
        
        # Remove from active downloads
        del self.active_downloads[download_id]
        
        # Update status label
        self.status_label.setText("Download cancelled")
    
    def add_download(self, video_data: Dict[str, Any]):
        """Add a download from video data.
        
        Args:
            video_data: Dictionary containing video information
        """
        # Show download options dialog
        url = f"https://www.youtube.com/watch?v={video_data.get('video_id')}"
        self._show_download_options(url)