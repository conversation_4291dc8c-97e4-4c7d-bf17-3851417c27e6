"""Theme Switcher Component for YouTube Downloader V2.

This module provides a UI component for switching between different application themes.
"""

# Try to import from either PyQt6 or PySide6
try:
    from PyQt6.QtWidgets import QWidget, QComboBox, QLabel, QHBoxLayout, QVBoxLayout
    from PyQt6.QtCore import pyqtSignal as Signal
except ImportError:
    try:
        from PySide6.QtWidgets import <PERSON>Widget, QComboBox, QLabel, QHBoxLayout, QVBoxLayout
        from PySide6.QtCore import Signal
    except ImportError:
        raise ImportError("Neither PyQt6 nor PySide6 could be imported. Please install one of them.")

from ..utils.style_manager import StyleManager


class ThemeSwitcher(QWidget):
    """A widget for switching between different application themes."""
    
    # Signal emitted when the theme is changed
    themeChanged = Signal(str)
    
    def __init__(self, parent=None, current_theme=StyleManager.THEME_SYSTEM):
        """Initialize the theme switcher.
        
        Args:
            parent (QWidget, optional): The parent widget. Defaults to None.
            current_theme (str, optional): The current theme. Defaults to StyleManager.THEME_SYSTEM.
        """
        super().__init__(parent)
        self.current_theme = current_theme
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Create layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create theme label
        self.label = QLabel("Theme:")
        layout.addWidget(self.label)
        
        # Create theme combo box
        self.theme_combo = QComboBox()
        
        # Add available themes to combo box
        available_themes = StyleManager.get_available_themes()
        for theme in available_themes:
            self.theme_combo.addItem(StyleManager.get_theme_display_name(theme), theme)
        
        # Set current theme
        index = self.theme_combo.findData(self.current_theme)
        if index >= 0:
            self.theme_combo.setCurrentIndex(index)
        
        # Connect signals
        self.theme_combo.currentIndexChanged.connect(self._on_theme_changed)
        
        layout.addWidget(self.theme_combo)
        self.setLayout(layout)
    
    def _on_theme_changed(self, index):
        """Handle theme change event.
        
        Args:
            index (int): The index of the selected theme in the combo box.
        """
        theme = self.theme_combo.itemData(index)
        if theme != self.current_theme:
            self.current_theme = theme
            self.themeChanged.emit(theme)
    
    def get_current_theme(self):
        """Get the current theme.
        
        Returns:
            str: The current theme identifier.
        """
        return self.current_theme
    
    def set_current_theme(self, theme):
        """Set the current theme.
        
        Args:
            theme (str): The theme identifier to set.
            
        Returns:
            bool: True if the theme was set successfully, False otherwise.
        """
        index = self.theme_combo.findData(theme)
        if index >= 0:
            self.theme_combo.setCurrentIndex(index)
            return True
        return False


class ThemePreview(QWidget):
    """A widget for previewing different application themes."""
    
    def __init__(self, parent=None):
        """Initialize the theme preview.
        
        Args:
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Create layout
        layout = QVBoxLayout(self)
        
        # Create theme switcher
        self.theme_switcher = ThemeSwitcher(self)
        layout.addWidget(self.theme_switcher)
        
        # Create preview elements
        self.header_label = QLabel("Theme Preview")
        self.header_label.setProperty("header", True)
        layout.addWidget(self.header_label)
        
        self.subheader_label = QLabel("This is how the application will look with this theme.")
        self.subheader_label.setProperty("subheader", True)
        layout.addWidget(self.subheader_label)
        
        # Connect signals
        self.theme_switcher.themeChanged.connect(self._on_theme_changed)
        
        self.setLayout(layout)
    
    def _on_theme_changed(self, theme):
        """Handle theme change event.
        
        Args:
            theme (str): The selected theme identifier.
        """
        # This method can be extended to show a real-time preview of the theme
        # For now, we just emit the signal to the parent
        self.themeChanged.emit(theme)
    
    # Forward the themeChanged signal
    themeChanged = Signal(str)