#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Validation Framework for YouTube Downloader V2

This module provides a comprehensive validation framework for the YouTube Downloader V2 application.
It includes validation result classes, validators, and utilities for validating inputs and outputs
across the service layer.
"""

from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Any, Callable, Dict, Generic, List, Optional, Type, TypeVar, Union
from pathlib import Path
import re
import os

# Type variables for generic validators
T = TypeVar('T')
R = TypeVar('R')


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""
    INFO = auto()
    WARNING = auto()
    ERROR = auto()
    CRITICAL = auto()


class ValidationIssue:
    """Represents a validation issue with message and severity."""
    
    def __init__(self, message: str, severity: ValidationSeverity = ValidationSeverity.ERROR):
        self.message = message
        self.severity = severity
    
    def __str__(self) -> str:
        return f"{self.severity.name}: {self.message}"


class ValidationResult:
    """Validation result container."""
    
    def __init__(self, is_valid: bool = True):
        self.is_valid = is_valid
        self.issues: List[ValidationIssue] = []
    
    def add_issue(self, message: str, severity: ValidationSeverity = ValidationSeverity.ERROR) -> 'ValidationResult':
        """Add an issue to the validation result."""
        self.issues.append(ValidationIssue(message, severity))
        if severity in (ValidationSeverity.ERROR, ValidationSeverity.CRITICAL):
            self.is_valid = False
        return self
    
    def add_error(self, message: str) -> 'ValidationResult':
        """Add an error to the validation result."""
        return self.add_issue(message, ValidationSeverity.ERROR)
    
    def add_warning(self, message: str) -> 'ValidationResult':
        """Add a warning to the validation result."""
        return self.add_issue(message, ValidationSeverity.WARNING)
    
    def add_info(self, message: str) -> 'ValidationResult':
        """Add an informational message to the validation result."""
        return self.add_issue(message, ValidationSeverity.INFO)
    
    def merge(self, other: 'ValidationResult') -> 'ValidationResult':
        """Merge another validation result into this one."""
        if not other.is_valid:
            self.is_valid = False
        self.issues.extend(other.issues)
        return self
    
    @classmethod
    def success(cls) -> 'ValidationResult':
        """Create a successful validation result."""
        return cls(True)
    
    @classmethod
    def failure(cls, message: str) -> 'ValidationResult':
        """Create a failed validation result with an error message."""
        result = cls(False)
        result.add_error(message)
        return result
    
    def get_errors(self) -> List[ValidationIssue]:
        """Get all error and critical issues."""
        return [issue for issue in self.issues 
                if issue.severity in (ValidationSeverity.ERROR, ValidationSeverity.CRITICAL)]
    
    def get_warnings(self) -> List[ValidationIssue]:
        """Get all warning issues."""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.WARNING]
    
    def get_infos(self) -> List[ValidationIssue]:
        """Get all info issues."""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.INFO]
    
    def __bool__(self) -> bool:
        return self.is_valid


class Validator(Generic[T], ABC):
    """Base validator interface."""
    
    @abstractmethod
    def validate(self, value: T) -> ValidationResult:
        """Validate the given value."""
        pass


class CompositeValidator(Validator[T]):
    """Validator that combines multiple validators."""
    
    def __init__(self, validators: List[Validator[T]]):
        self.validators = validators
    
    def validate(self, value: T) -> ValidationResult:
        """Run all validators and combine results."""
        result = ValidationResult()
        for validator in self.validators:
            result.merge(validator.validate(value))
        return result


class PredicateValidator(Validator[T]):
    """Validator that uses a predicate function."""
    
    def __init__(self, predicate: Callable[[T], bool], error_message: str):
        self.predicate = predicate
        self.error_message = error_message
    
    def validate(self, value: T) -> ValidationResult:
        """Validate using the predicate function."""
        if self.predicate(value):
            return ValidationResult.success()
        return ValidationResult.failure(self.error_message)


class YouTubeUrlValidator(Validator[str]):
    """Validator for YouTube URLs."""
    
    def __init__(self):
        # Regular expression for YouTube URLs
        self.youtube_regex = re.compile(
            r'^(https?://)?(www\.)?(youtube\.com|youtu\.be)/'
            r'(watch\?v=|embed/|v/|shorts/|playlist\?list=|channel/|user/)?([^\s&?/]+)'
        )
    
    def validate(self, url: str) -> ValidationResult:
        """Validate if the URL is a valid YouTube URL."""
        if not url:
            return ValidationResult.failure("URL cannot be empty")
        
        if not self.youtube_regex.match(url):
            return ValidationResult.failure("Invalid YouTube URL format")
        
        return ValidationResult.success()


class FilePathValidator(Validator[Path]):
    """Validator for file paths."""
    
    def __init__(self, must_exist: bool = False, must_be_writable: bool = False):
        self.must_exist = must_exist
        self.must_be_writable = must_be_writable
    
    def validate(self, path: Path) -> ValidationResult:
        """Validate if the path is valid and meets requirements."""
        result = ValidationResult()
        
        if not path:
            return ValidationResult.failure("Path cannot be empty")
        
        # Check for invalid characters in Windows paths
        if os.name == 'nt':
            invalid_chars = '<>:"|?*'
            for char in invalid_chars:
                if char in str(path):
                    result.add_error(f"Path contains invalid character: {char}")
        
        # Check if path exists if required
        if self.must_exist and not path.exists():
            result.add_error(f"Path does not exist: {path}")
        
        # Check if path is writable if required
        if self.must_be_writable:
            if path.exists():
                if not os.access(path, os.W_OK):
                    result.add_error(f"Path is not writable: {path}")
            else:
                # Check if parent directory is writable
                parent = path.parent
                if parent.exists() and not os.access(parent, os.W_OK):
                    result.add_error(f"Parent directory is not writable: {parent}")
        
        # Check path length for Windows
        if os.name == 'nt' and len(str(path)) > 260:
            result.add_warning("Path exceeds Windows MAX_PATH limit (260 characters)")
        
        return result


class DownloadConfigValidator(Validator[Dict[str, Any]]):
    """Validator for download configuration."""
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate download configuration."""
        result = ValidationResult()
        
        # Check required fields
        required_fields = ['download_dir', 'default_format', 'max_concurrent_downloads']
        for field in required_fields:
            if field not in config:
                result.add_error(f"Missing required field: {field}")
        
        # Validate download directory if present
        if 'download_dir' in config:
            path_validator = FilePathValidator(must_be_writable=True)
            path_result = path_validator.validate(Path(config['download_dir']))
            result.merge(path_result)
        
        # Validate max concurrent downloads if present
        if 'max_concurrent_downloads' in config:
            max_downloads = config['max_concurrent_downloads']
            if not isinstance(max_downloads, int):
                result.add_error("max_concurrent_downloads must be an integer")
            elif max_downloads < 1:
                result.add_error("max_concurrent_downloads must be at least 1")
            elif max_downloads > 10:
                result.add_warning("High number of concurrent downloads may affect performance")
        
        # Validate default format if present
        if 'default_format' in config:
            format_value = config['default_format']
            valid_formats = ['best', 'worst', '720p', '1080p', '480p', '360p', '240p']
            if format_value not in valid_formats:
                result.add_error(f"Invalid default_format: {format_value}. Must be one of {valid_formats}")
        
        return result


class ValidationManager:
    """Manager for validators."""
    
    def __init__(self):
        self.validators: Dict[Type, Validator] = {}
    
    def register_validator(self, value_type: Type, validator: Validator) -> None:
        """Register a validator for a specific type."""
        self.validators[value_type] = validator
    
    def validate(self, value: Any) -> ValidationResult:
        """Validate a value using the appropriate validator."""
        value_type = type(value)
        
        if value_type in self.validators:
            return self.validators[value_type].validate(value)
        
        # Try to find a compatible validator
        for type_, validator in self.validators.items():
            if isinstance(value, type_):
                return validator.validate(value)
        
        return ValidationResult.failure(f"No validator found for type: {value_type}")


# Create default validation manager with common validators
def create_default_validation_manager() -> ValidationManager:
    """Create a validation manager with default validators."""
    manager = ValidationManager()
    
    # Register validators
    manager.register_validator(str, YouTubeUrlValidator())
    manager.register_validator(Path, FilePathValidator())
    
    return manager