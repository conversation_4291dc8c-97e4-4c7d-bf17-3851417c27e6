#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for settings manager.
"""

import unittest
import tempfile
import json
from pathlib import Path

from src.config.base import LogLevel, BaseConfiguration
from src.config.settings import SettingsManager, ConfigurationChangedEvent


class TestSettingsManager(unittest.TestCase):
    """Test SettingsManager class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a new instance for each test
        self.settings_manager = SettingsManager()
        
        # Reset the singleton instance
        SettingsManager._instance = None
        SettingsManager._instance = self.settings_manager
        self.settings_manager._initialized = False
        self.settings_manager.__init__()
        
        # Create a temporary directory for config files
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config_file = self.temp_dir / "config.json"
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove temporary files
        if self.config_file.exists():
            self.config_file.unlink()
        
        if self.temp_dir.exists():
            self.temp_dir.rmdir()
    
    def test_singleton_behavior(self):
        """Test that SettingsManager is a singleton."""
        manager1 = SettingsManager()
        manager2 = SettingsManager()
        self.assertIs(manager1, manager2)
    
    def test_default_config(self):
        """Test default configuration."""
        config = self.settings_manager.get_config()
        self.assertIsInstance(config, BaseConfiguration)
        self.assertEqual(config.app.log_level, LogLevel.INFO)
    
    def test_initialize_with_config_file(self):
        """Test initialization with config file."""
        # Initialize with non-existent file (should create it)
        self.settings_manager.initialize(self.config_file)
        self.assertTrue(self.config_file.exists())
        
        # Check that the file contains valid JSON
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        self.assertIn('app', config_dict)
        self.assertIn('paths', config_dict)
        self.assertIn('downloads', config_dict)
    
    def test_get_setting(self):
        """Test getting settings using dot notation."""
        self.assertEqual(
            self.settings_manager.get_setting('app.name'),
            "YouTube Downloader V3"
        )
        
        self.assertEqual(
            self.settings_manager.get_setting('app.log_level'),
            LogLevel.INFO
        )
        
        # Test nested settings
        self.assertEqual(
            self.settings_manager.get_setting('downloads.default_quality'),
            "1080p"  # HIGH quality
        )
        
        # Test invalid key
        with self.assertRaises(KeyError):
            self.settings_manager.get_setting('invalid.key')
    
    def test_update_setting(self):
        """Test updating settings using dot notation."""
        # Update a simple setting
        self.settings_manager.update_setting('app.name', "New App Name")
        self.assertEqual(
            self.settings_manager.get_setting('app.name'),
            "New App Name"
        )
        
        # Update an enum setting
        self.settings_manager.update_setting('app.log_level', LogLevel.DEBUG)
        self.assertEqual(
            self.settings_manager.get_setting('app.log_level'),
            LogLevel.DEBUG
        )
        
        # Test invalid key
        with self.assertRaises(KeyError):
            self.settings_manager.update_setting('invalid.key', "value")
    
    def test_save_and_load_config(self):
        """Test saving and loading configuration."""
        # Modify configuration
        self.settings_manager.update_setting('app.name', "Test App")
        self.settings_manager.update_setting('app.log_level', LogLevel.DEBUG)
        
        # Save configuration
        self.settings_manager.save_to_file(self.config_file)
        
        # Create a new settings manager
        new_manager = SettingsManager()
        SettingsManager._instance = new_manager
        new_manager._initialized = False
        new_manager.__init__()
        
        # Load configuration
        new_manager.load_from_file(self.config_file)
        
        # Check that settings were loaded correctly
        self.assertEqual(
            new_manager.get_setting('app.name'),
            "Test App"
        )
        
        self.assertEqual(
            new_manager.get_setting('app.log_level'),
            LogLevel.DEBUG
        )
    
    def test_change_callbacks(self):
        """Test change notification callbacks."""
        # Create a callback function
        callback_called = False
        callback_key = None
        callback_old_value = None
        callback_new_value = None
        
        def callback(event: ConfigurationChangedEvent):
            nonlocal callback_called, callback_key, callback_old_value, callback_new_value
            callback_called = True
            callback_key = event.key
            callback_old_value = event.old_value
            callback_new_value = event.new_value
        
        # Register callback
        self.settings_manager.register_change_callback(callback)
        
        # Update a setting
        old_value = self.settings_manager.get_setting('app.name')
        new_value = "Callback Test"
        self.settings_manager.update_setting('app.name', new_value)
        
        # Check that callback was called with correct values
        self.assertTrue(callback_called)
        self.assertEqual(callback_key, 'app.name')
        self.assertEqual(callback_old_value, old_value)
        self.assertEqual(callback_new_value, new_value)
        
        # Reset callback variables
        callback_called = False
        callback_key = None
        callback_old_value = None
        callback_new_value = None
        
        # Unregister callback
        self.settings_manager.unregister_change_callback(callback)
        
        # Update another setting
        self.settings_manager.update_setting('app.version', "2.0.0")
        
        # Check that callback was not called
        self.assertFalse(callback_called)
    
    def test_set_config(self):
        """Test setting a new configuration."""
        # Create a new configuration
        new_config = BaseConfiguration()
        new_config.app.name = "New Config"
        new_config.app.log_level = LogLevel.DEBUG
        
        # Set the new configuration
        self.settings_manager.set_config(new_config)
        
        # Check that the configuration was set correctly
        config = self.settings_manager.get_config()
        self.assertEqual(config.app.name, "New Config")
        self.assertEqual(config.app.log_level, LogLevel.DEBUG)


if __name__ == "__main__":
    unittest.main()