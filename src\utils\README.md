# Utility Modules

## Logging Utilities

### Overview

The `logging_utils.py` module provides a robust logging infrastructure for the YouTube Downloader application. It includes:

- Functions for setting up logging with file and console handlers
- Support for different log levels
- File rotation to manage log file size
- Colored console output for better readability
- A context manager for capturing logs during testing

### Components

#### Core Functions

- `setup_logging(log_level, log_file=None, console_output=True)`: Configures logging with file and console handlers
  - `log_level`: The minimum log level to capture (from `LogLevel` enum)
  - `log_file`: Optional path to the log file
  - `console_output`: Whether to output logs to the console

- `get_logger(name)`: Creates and returns a named logger
  - `name`: The name of the logger (typically the module name)

#### Testing Utilities

- `LogCapture`: Context manager for capturing log records during testing
  - `records`: List of captured log records
  - `get_logs()`: Returns formatted log messages

### Usage Examples

#### Basic Logging

```python
from src.utils.logging_utils import setup_logging, get_logger
from src.config import LogLevel

# Set up logging with both file and console output
setup_logging(
    log_level=LogLevel.INFO,
    log_file="app.log",
    console_output=True
)

# Get a logger for the current module
logger = get_logger(__name__)

# Log messages at different levels
logger.debug("This is a debug message")  # Won't be logged at INFO level
logger.info("This is an info message")
logger.warning("This is a warning message")
logger.error("This is an error message")
logger.critical("This is a critical message")
```

#### File-Only Logging

```python
from src.utils.logging_utils import setup_logging, get_logger
from src.config import LogLevel

# Set up logging with file output only
setup_logging(
    log_level=LogLevel.DEBUG,
    log_file="debug.log",
    console_output=False
)

logger = get_logger("file_logger")
logger.debug("This debug message will be written to the log file")
```

#### Console-Only Logging

```python
from src.utils.logging_utils import setup_logging, get_logger
from src.config import LogLevel

# Set up logging with console output only
setup_logging(
    log_level=LogLevel.WARNING,
    log_file=None,
    console_output=True
)

logger = get_logger("console_logger")
logger.info("This info message won't be logged")  # Below WARNING level
logger.warning("This warning message will be displayed in the console")
```

#### Integration with Configuration System

```python
from src.utils.logging_utils import setup_logging, get_logger
from src.config import settings_manager, path_manager
from pathlib import Path

# Get log level and log directory from configuration
log_level = settings_manager.get_setting("app.log_level")
log_dir = Path(settings_manager.get_setting("paths.log_dir"))

# Ensure log directory exists
path_manager.ensure_directory(log_dir)

# Set up logging
log_file = log_dir / "app.log"
setup_logging(
    log_level=log_level,
    log_file=log_file,
    console_output=True
)

logger = get_logger("app")
logger.info(f"Application started with log level: {log_level}")
```

#### Capturing Logs for Testing

```python
import unittest
from src.utils.logging_utils import setup_logging, get_logger, LogCapture
from src.config import LogLevel

class TestLogging(unittest.TestCase):
    def setUp(self):
        setup_logging(LogLevel.DEBUG)
        self.logger = get_logger("test_logger")
    
    def test_log_capture(self):
        with LogCapture() as log_capture:
            self.logger.info("Test message")
            self.logger.error("Error message")
            
            # Check captured records
            self.assertEqual(len(log_capture.records), 2)
            self.assertEqual(log_capture.records[0].getMessage(), "Test message")
            self.assertEqual(log_capture.records[1].getMessage(), "Error message")
            
            # Check formatted output
            logs = log_capture.get_logs()
            self.assertIn("Test message", logs)
            self.assertIn("Error message", logs)
```

### Testing

The logging utilities include comprehensive unit and integration tests:

- `tests/test_utils/test_logging_utils.py`: Tests for setup_logging, get_logger, and LogCapture
- `tests/test_config/test_integration.py`: Integration tests with the configuration system