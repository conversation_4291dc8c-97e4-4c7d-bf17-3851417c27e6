# YouTube Downloader Application V3

A robust, asynchronous YouTube video and playlist downloader built with Python, featuring a clean architecture with service interfaces, validation framework, and a command-line interface. This version includes enhanced configuration management, cross-platform path handling, and comprehensive logging infrastructure.

## Features

- Download YouTube videos and playlists with progress tracking
- Multiple video quality options
- Concurrent downloads with configurable limits
- Pause, resume, and cancel downloads
- Enhanced configuration management with Pydantic models and change notifications
- Cross-platform path operations with sanitization and temporary file management
- Comprehensive logging infrastructure with file rotation and console output
- File system operations with long path support for Windows
- Validation framework for input validation

## Architecture

The application follows a clean architecture with the following components:

### Service Interfaces

The core of the application is built around service interfaces that define the contract for various components:

- `IYouTubeService`: For interacting with YouTube, extracting video information
- `IDownloadService`: For managing download tasks and tracking progress
- `IFileService`: For file system operations
- `IConfigService`: For configuration management (being migrated to the new configuration system)
- `IServiceFactory`: For creating and managing service instances

### Service Implementations

- `YouTubeService`: Implements `IYouTubeService` using yt-dlp
- `DownloadService`: Implements `IDownloadService` with asyncio and yt-dlp
- `FileService`: Implements `IFileService` with pathlib and shutil
- `ConfigService`: Implements `IConfigService` with JSON configuration (being migrated to `SettingsManager`)
- `ServiceFactory`: Implements `IServiceFactory` with dependency injection

### Configuration System

- `SettingsManager`: Singleton for managing application configuration with dot notation access
- `PathManager`: Singleton for cross-platform path operations and temporary file management
- Pydantic models for configuration validation (`AppConfig`, `PathConfig`, `DownloadConfig`)
- Factory functions for different environments (development, production, testing)

### Logging Infrastructure

- `setup_logging`: Configures logging with file and console handlers
- `get_logger`: Creates and returns named loggers
- File rotation to manage log file size
- Colored console output for better readability
- `LogCapture` context manager for testing

### Validation Framework

A flexible validation framework for input validation (fully implemented and validated):

- `ValidationSeverity`: Enum for validation issue severity levels
- `ValidationIssue`: Class for representing validation issues
- `ValidationResult`: Class for collecting validation results
- `Validator`: Abstract base class for validators
- `CompositeValidator`: Combines multiple validators
- `PredicateValidator`: Validates using a predicate function
- `StringValidator`: Validates string data
- `YouTubeUrlValidator`: Validates YouTube URLs
- `FilePathValidator`: Validates file paths
- `DownloadConfigValidator`: Validates download configuration
- `ValidationManager`: Manages validators and validation processes

The validation framework has been fully tested and verified with the `validate_phase3.py` script, which reported no validation issues.

## Project Structure

```
app_V3_optPlanning/
├── src/
│   ├── config/                  # Configuration system
│   │   ├── __init__.py          # Package exports
│   │   ├── base.py              # Core enums and models
│   │   ├── settings.py          # Settings manager
│   │   ├── paths.py             # Path manager
│   │   └── README.md            # Documentation
│   ├── services/                # Service components
│   │   ├── config_service.py    # Legacy config service
│   │   ├── download_service.py  # Download service
│   │   ├── logging_service.py   # Legacy logging service
│   │   └── service_interfaces.py # Service interfaces and data models
│   └── utils/                   # Utility modules
│       ├── logging_utils.py     # Logging infrastructure
│       ├── validation_framework.py # Validation framework
│       └── README.md            # Documentation
├── tests/                       # Test suite
│   ├── test_config/             # Configuration tests
│   │   ├── test_base.py         # Tests for models and enums
│   │   ├── test_settings.py     # Tests for settings manager
│   │   ├── test_paths.py        # Tests for path manager
│   │   └── test_integration.py  # Integration tests
│   └── test_utils/              # Utility tests
│       └── test_logging_utils.py # Tests for logging
└── _docs_planning/               # Documentation
    └── PHASE1_IMPLEMENTATION_PLAN.md # Implementation plan
```

## Usage

### Installation

1. Clone the repository
2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Command-line Interface

The application provides a command-line interface for downloading videos and playlists:

```bash
# Download a single video
python app_example.py download https://www.youtube.com/watch?v=VIDEO_ID

# Download a playlist
python app_example.py download-playlist https://www.youtube.com/playlist?list=PLAYLIST_ID

# List all downloads
python app_example.py list

# Update configuration
python app_example.py config --download-dir "path/to/directory" --max-concurrent 3 --format high

# Show current configuration
python app_example.py config --show
```

### Example Scripts

The application includes several example scripts in the `_examples` directory that demonstrate different features:

- **simple_example.py**: Basic example of downloading a single video
- **playlist_example.py**: Example of downloading videos from a YouTube playlist
- **search_example.py**: Example of searching for YouTube videos and downloading selected results
- **batch_download_example.py**: Example of batch downloading multiple videos from a text file
- **concurrent_downloads_example.py**: Example of downloading multiple videos concurrently
- **custom_format_example.py**: Example of downloading videos with custom format options
- **config_example.py**: Example of managing configuration settings
- **file_operations_example.py**: Example of performing various file operations
- **error_handling_example.py**: Example of handling errors and exceptions

To run any of these examples:

```bash
python _examples/simple_example.py
python _examples/playlist_example.py
python _examples/search_example.py
python _examples/batch_download_example.py
python _examples/concurrent_downloads_example.py
python _examples/custom_format_example.py
python _examples/config_example.py
python _examples/file_operations_example.py
python _examples/error_handling_example.py
```

## Video Quality Options

- `VERY_HIGH`: 1080p or higher
- `HIGH`: 720p

## Configuration

The application stores configuration in a JSON file in the user's home directory:

- Windows: `%USERPROFILE%\.youtube_downloader\config.json`

Configuration includes:

- `download_dir`: Directory for downloaded videos
- `max_concurrent_downloads`: Maximum number of concurrent downloads
- `default_format`: Default video quality

## License

This project is open source and available under the MIT License.
