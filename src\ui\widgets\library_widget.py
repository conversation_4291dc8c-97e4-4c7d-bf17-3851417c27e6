#!/usr/bin/env python3
"""
Library Widget Module

This module defines the library widget for managing downloaded videos.
"""

import sys
import os
from typing import Dict, Any, List

try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QListWidget, QListWidgetItem, QMenu, QFileDialog, QInputDialog,
        QMessageBox, QComboBox, QLineEdit, QSplitter, QFrame, QGridLayout
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QSize, QUrl
    from PyQt6.QtGui import QIcon, QDesktopServices
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
            QListWidget, QListWidgetItem, QMenu, QFileDialog, QInputDialog,
            QMessageBox, QComboBox, QLineEdit, QSplitter, QFrame, QGridLayout
        )
        from PySide6.QtCore import Qt, Signal as pyqtSignal, QSize, QUrl
        from PySide6.QtGui import QIcon, QDesktopServices
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required.")
        sys.exit(1)

try:
    import qtawesome as qta
    HAS_QTAWESOME = True
except ImportError:
    HAS_QTAWESOME = False

from src.config.app_config import AppConfig
from src.services.service_factory import ServiceFactory
from src.services.file_service import FileService


class LibraryWidget(QWidget):
    """Widget for managing downloaded videos."""
    
    # Signals
    video_selected = pyqtSignal(dict)
    playlist_selected = pyqtSignal(str)
    
    def __init__(self, config: AppConfig):
        """Initialize the library widget.
        
        Args:
            config: Application configuration
        """
        super().__init__()
        self.config = config
        
        # Initialize services
        self.service_factory = ServiceFactory()
        self.file_service = self.service_factory.get_service('file')
        
        # Initialize UI components
        self._init_ui()
        
        # Connect signals and slots
        self._connect_signals()
        
        # Load library data
        self._load_library()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.layout = QVBoxLayout(self)
        
        # Create splitter for playlists and videos
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Create playlists panel
        self._create_playlists_panel()
        
        # Create videos panel
        self._create_videos_panel()
        
        # Add panels to splitter
        self.splitter.addWidget(self.playlists_panel)
        self.splitter.addWidget(self.videos_panel)
        
        # Set initial sizes
        self.splitter.setSizes([200, 600])
        
        # Add splitter to layout
        self.layout.addWidget(self.splitter)
        
        # Status label
        self.status_label = QLabel("Library loaded")
        self.layout.addWidget(self.status_label)
    
    def _create_playlists_panel(self):
        """Create the playlists panel."""
        # Playlists panel
        self.playlists_panel = QFrame()
        self.playlists_panel.setFrameShape(QFrame.Shape.StyledPanel)
        playlists_layout = QVBoxLayout(self.playlists_panel)
        
        # Playlists header
        playlists_header = QLabel("Playlists")
        playlists_header.setStyleSheet("font-weight: bold; font-size: 14px;")
        playlists_layout.addWidget(playlists_header)
        
        # Playlists controls
        playlists_controls = QHBoxLayout()
        
        # Add playlist button
        self.add_playlist_button = QPushButton("Add")
        if HAS_QTAWESOME:
            self.add_playlist_button.setIcon(qta.icon("fa5s.plus"))
        playlists_controls.addWidget(self.add_playlist_button)
        
        # Remove playlist button
        self.remove_playlist_button = QPushButton("Remove")
        if HAS_QTAWESOME:
            self.remove_playlist_button.setIcon(qta.icon("fa5s.minus"))
        playlists_controls.addWidget(self.remove_playlist_button)
        
        playlists_layout.addLayout(playlists_controls)
        
        # Playlists list
        self.playlists_list = QListWidget()
        playlists_layout.addWidget(self.playlists_list)
        
        # Add default playlists
        self.playlists_list.addItem("All Videos")
        self.playlists_list.addItem("Recently Added")
        self.playlists_list.addItem("Favorites")
    
    def _create_videos_panel(self):
        """Create the videos panel."""
        # Videos panel
        self.videos_panel = QFrame()
        self.videos_panel.setFrameShape(QFrame.Shape.StyledPanel)
        videos_layout = QVBoxLayout(self.videos_panel)
        
        # Videos header
        self.videos_header = QLabel("All Videos")
        self.videos_header.setStyleSheet("font-weight: bold; font-size: 14px;")
        videos_layout.addWidget(self.videos_header)
        
        # Search and filter controls
        filter_layout = QHBoxLayout()
        
        # Search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search library...")
        filter_layout.addWidget(self.search_input)
        
        # Sort by
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["Date Added", "Title", "Duration"])
        filter_layout.addWidget(self.sort_combo)
        
        videos_layout.addLayout(filter_layout)
        
        # Videos grid
        self.videos_grid = QGridLayout()
        videos_layout.addLayout(self.videos_grid)
        
        # Add stretch to push everything to the top
        videos_layout.addStretch()
    
    def _connect_signals(self):
        """Connect signals and slots."""
        # Connect playlist buttons
        self.add_playlist_button.clicked.connect(self._on_add_playlist_clicked)
        self.remove_playlist_button.clicked.connect(self._on_remove_playlist_clicked)
        
        # Connect playlists list
        self.playlists_list.currentItemChanged.connect(self._on_playlist_changed)
        
        # Connect search input
        self.search_input.textChanged.connect(self._on_search_text_changed)
        
        # Connect sort combo
        self.sort_combo.currentIndexChanged.connect(self._on_sort_changed)
    
    def _load_library(self):
        """Load the library data."""
        # This would typically load from the file service
        # For now, we'll just use placeholder data
        try:
            # Get library data from service
            library_data = self.file_service.get_library()
            
            # Display videos
            self._display_videos(library_data.get('videos', []))
            
            # Add custom playlists
            for playlist in library_data.get('playlists', []):
                if playlist['name'] not in ["All Videos", "Recently Added", "Favorites"]:
                    self.playlists_list.addItem(playlist['name'])
            
            # Update status
            self.status_label.setText(f"Loaded {len(library_data.get('videos', []))} videos")
        except Exception as e:
            # Show error message
            self.status_label.setText(f"Error loading library: {str(e)}")
    
    def _display_videos(self, videos: List[Dict[str, Any]]):
        """Display videos in the grid.
        
        Args:
            videos: List of video dictionaries
        """
        # Clear existing videos
        self._clear_videos_grid()
        
        if not videos:
            # Add empty message
            empty_label = QLabel("No videos found")
            empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.videos_grid.addWidget(empty_label, 0, 0)
            return
        
        # Add videos to grid
        row, col = 0, 0
        max_cols = 3  # Number of columns in the grid
        
        for video in videos:
            # Create video item
            video_widget = self._create_video_widget(video)
            self.videos_grid.addWidget(video_widget, row, col)
            
            # Update row and column
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
    
    def _create_video_widget(self, video: Dict[str, Any]) -> QWidget:
        """Create a widget for displaying a video.
        
        Args:
            video: Video dictionary
            
        Returns:
            QWidget: Video widget
        """
        # Video widget
        video_widget = QFrame()
        video_widget.setFrameShape(QFrame.Shape.StyledPanel)
        video_widget.setFixedSize(200, 180)
        
        # Video layout
        video_layout = QVBoxLayout(video_widget)
        
        # Thumbnail (placeholder)
        thumbnail_label = QLabel()
        thumbnail_label.setFixedSize(180, 100)
        thumbnail_label.setStyleSheet("background-color: #ddd;")
        thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        thumbnail_label.setText("Thumbnail")
        video_layout.addWidget(thumbnail_label)
        
        # Title
        title_label = QLabel(video.get('title', 'Unknown Title'))
        title_label.setWordWrap(True)
        title_label.setStyleSheet("font-weight: bold;")
        video_layout.addWidget(title_label)
        
        # Actions
        actions_layout = QHBoxLayout()
        
        # Play button
        play_button = QPushButton("Play")
        if HAS_QTAWESOME:
            play_button.setIcon(qta.icon("fa5s.play"))
        play_button.clicked.connect(lambda: self._on_play_clicked(video))
        actions_layout.addWidget(play_button)
        
        # Info button
        info_button = QPushButton("Info")
        if HAS_QTAWESOME:
            info_button.setIcon(qta.icon("fa5s.info-circle"))
        info_button.clicked.connect(lambda: self._on_info_clicked(video))
        actions_layout.addWidget(info_button)
        
        video_layout.addLayout(actions_layout)
        
        return video_widget
    
    def _clear_videos_grid(self):
        """Clear the videos grid."""
        # Remove all widgets from the grid
        while self.videos_grid.count() > 0:
            item = self.videos_grid.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
    
    def _on_add_playlist_clicked(self):
        """Handle add playlist button click."""
        # Show input dialog
        playlist_name, ok = QInputDialog.getText(
            self, "Add Playlist", "Enter playlist name:"
        )
        
        if ok and playlist_name:
            # Check if playlist already exists
            for i in range(self.playlists_list.count()):
                if self.playlists_list.item(i).text() == playlist_name:
                    QMessageBox.warning(
                        self, "Duplicate Playlist", 
                        f"Playlist '{playlist_name}' already exists."
                    )
                    return
            
            # Add to list
            self.playlists_list.addItem(playlist_name)
            
            # Create playlist in service
            self.file_service.create_playlist(playlist_name)
            
            # Update status
            self.status_label.setText(f"Created playlist: {playlist_name}")
    
    def _on_remove_playlist_clicked(self):
        """Handle remove playlist button click."""
        # Get selected playlist
        current_item = self.playlists_list.currentItem()
        if not current_item:
            return
        
        playlist_name = current_item.text()
        
        # Don't allow removing default playlists
        if playlist_name in ["All Videos", "Recently Added", "Favorites"]:
            QMessageBox.warning(
                self, "Cannot Remove", 
                f"Cannot remove default playlist '{playlist_name}'."
            )
            return
        
        # Confirm removal
        reply = QMessageBox.question(
            self, "Remove Playlist", 
            f"Are you sure you want to remove playlist '{playlist_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Remove from list
            row = self.playlists_list.row(current_item)
            self.playlists_list.takeItem(row)
            
            # Remove playlist in service
            self.file_service.delete_playlist(playlist_name)
            
            # Update status
            self.status_label.setText(f"Removed playlist: {playlist_name}")
    
    def _on_playlist_changed(self, current, previous):
        """Handle playlist selection change.
        
        Args:
            current: Current item
            previous: Previous item
        """
        if not current:
            return
        
        playlist_name = current.text()
        
        # Update header
        self.videos_header.setText(playlist_name)
        
        # Load videos for playlist
        try:
            # Get videos from service
            videos = self.file_service.get_playlist_videos(playlist_name)
            
            # Display videos
            self._display_videos(videos)
            
            # Update status
            self.status_label.setText(f"Loaded playlist: {playlist_name} ({len(videos)} videos)")
            
            # Emit playlist selected signal
            self.playlist_selected.emit(playlist_name)
        except Exception as e:
            # Show error message
            self.status_label.setText(f"Error loading playlist: {str(e)}")
    
    def _on_search_text_changed(self, text):
        """Handle search text change.
        
        Args:
            text: Search text
        """
        # Get current playlist
        current_item = self.playlists_list.currentItem()
        if not current_item:
            return
        
        playlist_name = current_item.text()
        
        # Search videos
        try:
            # Get videos from service with search filter
            videos = self.file_service.search_videos(text, playlist_name)
            
            # Display videos
            self._display_videos(videos)
            
            # Update status
            self.status_label.setText(f"Found {len(videos)} videos matching '{text}'")
        except Exception as e:
            # Show error message
            self.status_label.setText(f"Error searching videos: {str(e)}")
    
    def _on_sort_changed(self, index):
        """Handle sort order change.
        
        Args:
            index: Combo box index
        """
        sort_by = self.sort_combo.currentText()
        
        # Get current playlist
        current_item = self.playlists_list.currentItem()
        if not current_item:
            return
        
        playlist_name = current_item.text()
        
        # Sort videos
        try:
            # Get videos from service with sort order
            videos = self.file_service.get_playlist_videos(playlist_name, sort_by=sort_by)
            
            # Display videos
            self._display_videos(videos)
            
            # Update status
            self.status_label.setText(f"Sorted videos by {sort_by}")
        except Exception as e:
            # Show error message
            self.status_label.setText(f"Error sorting videos: {str(e)}")
    
    def _on_play_clicked(self, video: Dict[str, Any]):
        """Handle play button click.
        
        Args:
            video: Video dictionary
        """
        # Get video file path
        file_path = video.get('file_path')
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(
                self, "File Not Found", 
                f"Video file not found: {file_path}"
            )
            return
        
        # Open video file with default player
        QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
    
    def _on_info_clicked(self, video: Dict[str, Any]):
        """Handle info button click.
        
        Args:
            video: Video dictionary
        """
        # Show video information dialog
        QMessageBox.information(
            self, "Video Information", 
            f"<b>Title:</b> {video.get('title', 'Unknown')}<br>"
            f"<b>Channel:</b> {video.get('channel_title', 'Unknown')}<br>"
            f"<b>Duration:</b> {video.get('duration', 'Unknown')}<br>"
            f"<b>File:</b> {video.get('file_path', 'Unknown')}<br>"
            f"<b>Date Added:</b> {video.get('date_added', 'Unknown')}<br>"
        )
        
        # Emit video selected signal
        self.video_selected.emit(video)