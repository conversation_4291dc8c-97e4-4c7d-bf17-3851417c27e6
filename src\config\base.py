#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuration Base Models

This module defines the core configuration models and enumerations for the application.
It uses Pydantic for type validation and provides factory functions for different environments.
"""

from enum import Enum, auto
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from pathlib import Path


class LogLevel(str, Enum):
    """Log level options"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DownloadQuality(str, Enum):
    """Video quality options"""
    MEDIUM = "720p"
    BEST = "best"


class VideoFormat(str, Enum):
    """Video format options"""
    MP4 = "mp4"


class AudioFormat(str, Enum):
    """Audio format options"""
    MP3 = "mp3"
    AAC = "aac"
    OGG = "ogg"
    M4A = "m4a"


class AppConfig(BaseModel):
    """Application-level settings"""
    name: str = "YouTube Downloader V3"
    version: str = "3.0.0"
    log_level: LogLevel = LogLevel.INFO
    max_concurrent_downloads: int = Field(default=3, ge=1, le=10)
    auto_update_check: bool = True
    user_agent: Optional[str] = None


class PathConfig(BaseModel):
    """Directory and file paths"""
    downloads_dir: Path = Field(default=Path.home() / "Downloads" / "YouTubeDownloader")
    config_dir: Path = Field(default=Path.home() / ".youtube_downloader")
    temp_dir: Path = Field(default=Path.home() / ".youtube_downloader" / "temp")
    log_dir: Path = Field(default=Path.home() / ".youtube_downloader" / "logs")
    
    @validator("*")
    def validate_paths(cls, v):
        """Ensure paths are absolute"""
        if not isinstance(v, Path):
            v = Path(v)
        return v.absolute()


class DownloadConfig(BaseModel):
    """Download-related settings"""
    default_quality: DownloadQuality = DownloadQuality.HIGH
    default_video_format: VideoFormat = VideoFormat.MP4
    default_audio_format: AudioFormat = AudioFormat.MP3
    create_thumbnail: bool = True
    create_info_json: bool = False
    max_retries: int = Field(default=3, ge=0, le=10)
    timeout: int = Field(default=30, ge=5, le=300)
    rate_limit: Optional[str] = None  # e.g., "1M"


class BaseConfiguration(BaseModel):
    """Main configuration class"""
    app: AppConfig = Field(default_factory=AppConfig)
    paths: PathConfig = Field(default_factory=PathConfig)
    downloads: DownloadConfig = Field(default_factory=DownloadConfig)
    
    class Config:
        validate_assignment = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Path: lambda v: str(v)
        }


def create_development_config() -> BaseConfiguration:
    """Create a configuration for development environment"""
    config = BaseConfiguration()
    config.app.log_level = LogLevel.DEBUG
    config.app.max_concurrent_downloads = 1
    config.downloads.create_info_json = True
    return config


def create_production_config() -> BaseConfiguration:
    """Create a configuration for production environment"""
    config = BaseConfiguration()
    config.app.log_level = LogLevel.INFO
    config.app.max_concurrent_downloads = 3
    return config


def create_testing_config() -> BaseConfiguration:
    """Create a configuration for testing environment"""
    config = BaseConfiguration()
    config.app.log_level = LogLevel.DEBUG
    config.paths.downloads_dir = Path("./test_downloads")
    config.paths.temp_dir = Path("./test_temp")
    config.paths.log_dir = Path("./test_logs")
    return config