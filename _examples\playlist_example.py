#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Playlist Example of YouTube Downloader

This script demonstrates how to download a YouTube playlist using the
YouTube Downloader services.
"""

import asyncio
import os
import re
from pathlib import Path
from typing import Dict, List, Set

from src.services.service_interfaces import VideoQuality, DownloadStatus, DownloadTask
from src.services.service_factory import AsyncServiceFactory


async def main():
    """Example of downloading a YouTube playlist."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    youtube_service = await factory.create_youtube_service_async()
    download_service = await factory.create_download_service_async()
    file_service = await factory.create_file_service_async()
    config_service = await factory.create_config_service_async()
    
    # Load configuration
    config = await config_service.load_config()
    print(f"Download directory: {config.download_dir}")
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    
    # Example playlist URL - a short playlist for demonstration
    # This is a playlist of TED Talks under 5 minutes
    playlist_url = "https://www.youtube.com/playlist?list=PLOGi5-fAu8bFBNzTcXSFBLpGJRw0C-HEh"
    
    # Validate URL
    validation_result = await youtube_service.validate_url(playlist_url, is_playlist=True)
    if not validation_result.is_valid:
        print(f"Error: {validation_result.message}")
        return
    
    # Get playlist information
    print(f"Fetching playlist information for {playlist_url}...")
    playlist_info = await youtube_service.extract_playlist_info(playlist_url)
    
    if not playlist_info or not playlist_info.videos:
        print("Error: Failed to extract playlist information or playlist is empty.")
        return
    
    print(f"Playlist title: {playlist_info.title}")
    print(f"Number of videos: {len(playlist_info.videos)}")
    
    # Create playlist directory
    playlist_dir_name = create_safe_filename(playlist_info.title)
    playlist_dir = Path(config.download_dir) / playlist_dir_name
    await file_service.create_directory(playlist_dir)
    
    # Start downloads for each video (limit to first 3 for demonstration)
    max_videos = min(3, len(playlist_info.videos))
    task_ids = []
    
    for i, video_info in enumerate(playlist_info.videos[:max_videos]):
        # Create safe filename
        filename = f"{i+1:03d}_{create_safe_filename(video_info.title)}.mp4"
        destination = playlist_dir / filename
        
        # Create download task
        task = DownloadTask(
            url=video_info.url,
            destination=str(destination),
            quality=VideoQuality.MEDIUM,  # You can change this to any quality
            video_info=video_info
        )
        
        # Start download
        task_id = await download_service.start_download(task)
        task_ids.append(task_id)
        print(f"Started download for '{video_info.title}' with task ID: {task_id}")
        
        # Wait a bit before starting next download to avoid rate limiting
        await asyncio.sleep(0.5)
    
    # Monitor progress for all tasks
    await monitor_multiple_download_progress(download_service, task_ids)


def create_safe_filename(title: str) -> str:
    """Create a safe filename from a title.
    
    Args:
        title: The title to convert to a safe filename
        
    Returns:
        A safe filename
    """
    # Remove invalid characters
    safe_title = re.sub(r'[\\/*?:"<>|]', '', title)
    
    # Replace spaces with underscores
    safe_title = safe_title.replace(' ', '_')
    
    # Limit length
    if len(safe_title) > 100:
        safe_title = safe_title[:100]
        
    return safe_title


async def monitor_multiple_download_progress(download_service, task_ids: List[str]):
    """Monitor download progress for multiple tasks.
    
    Args:
        download_service: Download service instance
        task_ids: List of download task IDs
    """
    try:
        # Set to track completed tasks
        completed_tasks: Set[str] = set()
        
        # Monitor progress until all tasks are complete, failed, or canceled
        while len(completed_tasks) < len(task_ids):
            # Clear screen
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Print header
            print(f"Monitoring {len(task_ids)} downloads:")
            print("-" * 80)
            
            # Get all tasks
            all_tasks = await download_service.get_all_tasks()
            
            # Create a dictionary of tasks by ID for easy lookup
            tasks_dict: Dict[str, DownloadTask] = {task.task_id: task for task in all_tasks if task.task_id in task_ids}
            
            # Print progress for each task
            for task_id in task_ids:
                if task_id not in tasks_dict:
                    print(f"Task {task_id}: Not found")
                    completed_tasks.add(task_id)
                    continue
                    
                task = tasks_dict[task_id]
                progress = task.progress
                
                if not progress:
                    print(f"Task {task_id}: No progress information")
                    continue
                    
                # Get video title
                title = task.video_info.title if task.video_info else "Unknown"
                
                # Print progress
                status_text = progress.status.name
                if progress.status == DownloadStatus.DOWNLOADING:
                    # Calculate speed in MB/s
                    speed_mb = progress.speed / (1024 * 1024)
                    
                    # Format ETA
                    eta_min = progress.eta // 60
                    eta_sec = progress.eta % 60
                    eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                    
                    # Print progress bar
                    bar_length = 30
                    filled_length = int(bar_length * progress.progress_percentage / 100)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    
                    print(f"[{bar}] {progress.progress_percentage:.1f}% - {speed_mb:.2f} MB/s - ETA: {eta_text}")
                    print(f"Title: {title}")
                else:
                    # Print status for non-downloading states
                    print(f"Status: {status_text}")
                    print(f"Title: {title}")
                    
                print("-" * 80)
                
                # Check if download is complete, failed, or canceled
                if progress.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELED]:
                    completed_tasks.add(task_id)
                    
            # Print summary
            print(f"Completed: {len(completed_tasks)}/{len(task_ids)}")
            
            # Wait before checking again
            await asyncio.sleep(1)
            
        print("All downloads finished.")
            
    except KeyboardInterrupt:
        # Cancel all downloads if user presses Ctrl+C
        print("\nCanceling all downloads...")
        for task_id in task_ids:
            try:
                await download_service.cancel_download(task_id)
            except Exception:
                pass
        print("All downloads canceled.")
        
    except Exception as e:
        print(f"Error monitoring downloads: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())