#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Settings Manager

This module provides a singleton settings manager for handling application configuration.
It supports loading, saving, and updating configuration with change notifications.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, List, Callable, Union

from .base import BaseConfiguration, create_production_config


class ConfigurationChangedEvent:
    """Event for configuration changes"""
    def __init__(self, key: str, old_value: Any, new_value: Any):
        self.key = key
        self.old_value = old_value
        self.new_value = new_value


class SettingsManager:
    """Singleton settings manager"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SettingsManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._config = create_production_config()
        self._config_file = None
        self._change_callbacks: List[Callable[[ConfigurationChangedEvent], None]] = []
        self._initialized = True
    
    def initialize(self, config_file: Optional[Union[str, Path]] = None):
        """Initialize settings manager with optional config file"""
        if config_file:
            self._config_file = Path(config_file)
            if self._config_file.exists():
                self.load_from_file(self._config_file)
            else:
                # Create directories if they don't exist
                self._config_file.parent.mkdir(parents=True, exist_ok=True)
                self.save_to_file(self._config_file)
        
        # Create necessary directories
        self._ensure_directories_exist()
    
    def _ensure_directories_exist(self):
        """Ensure all configured directories exist"""
        for path_attr in ["downloads_dir", "config_dir", "temp_dir", "log_dir"]:
            path = getattr(self._config.paths, path_attr)
            path.mkdir(parents=True, exist_ok=True)
    
    def get_config(self) -> BaseConfiguration:
        """Get the current configuration"""
        return self._config
    
    def set_config(self, config: BaseConfiguration):
        """Set a new configuration"""
        old_config = self._config
        self._config = config
        
        # Notify about changes
        self._notify_changes("config", old_config, config)
        
        # Save if we have a config file
        if self._config_file:
            self.save_to_file(self._config_file)
    
    def get_setting(self, key: str) -> Any:
        """Get a setting using dot notation (e.g., 'app.name')"""
        parts = key.split('.')
        value = self._config
        
        for part in parts:
            if hasattr(value, part):
                value = getattr(value, part)
            else:
                raise KeyError(f"Setting '{key}' not found")
        
        return value
    
    def update_setting(self, key: str, value: Any):
        """Update a setting using dot notation"""
        parts = key.split('.')
        target = self._config
        
        # Navigate to the parent object
        for part in parts[:-1]:
            if hasattr(target, part):
                target = getattr(target, part)
            else:
                raise KeyError(f"Setting '{key}' not found")
        
        # Get the old value
        old_value = getattr(target, parts[-1]) if hasattr(target, parts[-1]) else None
        
        # Set the new value
        setattr(target, parts[-1], value)
        
        # Notify about the change
        self._notify_changes(key, old_value, value)
        
        # Save if we have a config file
        if self._config_file:
            self.save_to_file(self._config_file)
    
    def load_from_file(self, file_path: Union[str, Path]):
        """Load configuration from a JSON file"""
        file_path = Path(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            # Convert to BaseConfiguration
            old_config = self._config
            self._config = BaseConfiguration.parse_obj(config_dict)
            
            # Notify about changes
            self._notify_changes("config", old_config, self._config)
            
            return True
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return False
    
    def save_to_file(self, file_path: Union[str, Path]):
        """Save configuration to a JSON file"""
        file_path = Path(file_path)
        
        try:
            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert to dict and save
            config_dict = self._config.dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"Error saving configuration: {e}")
            return False
    
    def register_change_callback(self, callback: Callable[[ConfigurationChangedEvent], None]):
        """Register a callback for configuration changes"""
        if callback not in self._change_callbacks:
            self._change_callbacks.append(callback)
    
    def unregister_change_callback(self, callback: Callable[[ConfigurationChangedEvent], None]):
        """Unregister a callback for configuration changes"""
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)
    
    def _notify_changes(self, key: str, old_value: Any, new_value: Any):
        """Notify all registered callbacks about a configuration change"""
        event = ConfigurationChangedEvent(key, old_value, new_value)
        
        for callback in self._change_callbacks:
            try:
                callback(event)
            except Exception as e:
                print(f"Error in configuration change callback: {e}")


# Singleton instance
settings_manager = SettingsManager()