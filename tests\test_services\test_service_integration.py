#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Integration Tests

This module contains integration tests for service layer interactions.
"""

import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from src.services.service_factory import ServiceFactory, AsyncServiceFactory
from src.services.exceptions import YouTubeServiceException, DownloadServiceException
from src.services.validation import ServiceValidationResult
from src.services.utils.progress_tracker import ProgressTracker, ProgressEvent


class TestServiceIntegration:
    """Integration tests for service layer."""
    
    @pytest.fixture
    def service_factory(self):
        """Create a service factory for testing."""
        return ServiceFactory()
    
    @pytest.fixture
    def async_service_factory(self):
        """Create an async service factory for testing."""
        return AsyncServiceFactory()
    
    @pytest.fixture
    def temp_dir(self, tmp_path):
        """Create a temporary directory for testing."""
        return tmp_path / "test_downloads"
    
    def test_service_factory_creation(self, service_factory):
        """Test that service factory creates all required services."""
        # Test synchronous service creation
        youtube_service = service_factory.create_youtube_service()
        download_service = service_factory.create_download_service()
        file_service = service_factory.create_file_service()
        config_service = service_factory.create_config_service()
        logging_service = service_factory.create_logging_service()
        
        assert youtube_service is not None
        assert download_service is not None
        assert file_service is not None
        assert config_service is not None
        assert logging_service is not None
        
        # Test singleton behavior
        youtube_service2 = service_factory.create_youtube_service()
        assert youtube_service is youtube_service2
    
    @pytest.mark.asyncio
    async def test_async_service_factory_creation(self, async_service_factory):
        """Test that async service factory creates all required services."""
        # Test asynchronous service creation
        youtube_service = await async_service_factory.create_youtube_service_async()
        download_service = await async_service_factory.create_download_service_async()
        file_service = await async_service_factory.create_file_service_async()
        config_service = await async_service_factory.create_config_service_async()
        logging_service = await async_service_factory.create_logging_service_async()
        
        assert youtube_service is not None
        assert download_service is not None
        assert file_service is not None
        assert config_service is not None
        assert logging_service is not None
    
    @pytest.mark.asyncio
    async def test_service_validation_integration(self, async_service_factory):
        """Test integration between services and validation framework."""
        youtube_service = await async_service_factory.create_youtube_service_async()
        
        # Test valid URL validation
        valid_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        validation_result = await youtube_service.validate_url(valid_url)
        assert validation_result.is_valid
        
        # Test invalid URL validation
        invalid_url = "https://example.com/not-youtube"
        validation_result = await youtube_service.validate_url(invalid_url)
        assert not validation_result.is_valid
    
    @pytest.mark.asyncio
    async def test_progress_tracking_integration(self, temp_dir):
        """Test integration of progress tracking with services."""
        # Create progress tracker with temporary storage
        progress_tracker = ProgressTracker(storage_path=temp_dir)
        
        # Test progress tracking
        task_id = "test_task_123"
        
        # Simulate progress updates
        event1 = progress_tracker.update_progress(task_id, 1024, 10240, "downloading")
        assert event1.task_id == task_id
        assert event1.bytes_downloaded == 1024
        assert event1.total_bytes == 10240
        
        event2 = progress_tracker.update_progress(task_id, 5120, 10240, "downloading")
        assert event2.bytes_downloaded == 5120
        assert event2.speed_bps > 0  # Should have calculated speed
        
        # Test progress retrieval
        last_progress = progress_tracker.get_last_progress(task_id)
        assert last_progress is not None
        assert last_progress.bytes_downloaded == 5120
        
        # Test cleanup
        progress_tracker.cleanup_task(task_id)
        assert task_id not in progress_tracker.estimators
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, async_service_factory):
        """Test error handling across service boundaries."""
        youtube_service = await async_service_factory.create_youtube_service_async()
        
        # Test that service exceptions are properly raised
        with pytest.raises(YouTubeServiceException):
            await youtube_service.extract_video_info("")  # Empty URL should fail
        
        with pytest.raises(YouTubeServiceException):
            await youtube_service.extract_video_info("invalid-url")  # Invalid URL should fail
    
    @pytest.mark.asyncio
    async def test_config_service_integration(self, async_service_factory, temp_dir):
        """Test configuration service integration."""
        config_service = await async_service_factory.create_config_service_async()
        
        # Test configuration loading and saving
        test_config = {
            'download_dir': str(temp_dir),
            'max_concurrent_downloads': 3,
            'default_format': '720p'
        }
        
        # Save configuration
        config_path = temp_dir / "test_config.json"
        save_result = await config_service.save_config_to_file(test_config, config_path)
        assert save_result
        
        # Load configuration
        loaded_config = await config_service.load_config_from_file(config_path)
        assert loaded_config is not None
        assert loaded_config['download_dir'] == str(temp_dir)
        assert loaded_config['max_concurrent_downloads'] == 3
    
    @pytest.mark.asyncio
    async def test_file_service_integration(self, async_service_factory, temp_dir):
        """Test file service integration."""
        file_service = await async_service_factory.create_file_service_async()
        
        # Test directory creation
        test_dir = temp_dir / "test_subdir"
        result = await file_service.create_directory(test_dir)
        assert result
        assert test_dir.exists()
        
        # Test path validation
        validation_result = await file_service.validate_path(test_dir)
        assert validation_result
        
        # Test file operations
        test_file = test_dir / "test_file.txt"
        test_file.write_text("test content")
        
        # Test file size
        size_info = await file_service.get_file_size(test_file)
        assert size_info is not None
        assert size_info.bytes > 0
    
    @pytest.mark.asyncio
    async def test_service_exception_propagation(self, async_service_factory):
        """Test that exceptions propagate correctly through service layers."""
        download_service = await async_service_factory.create_download_service_async()
        
        # Test invalid task operations
        with pytest.raises(DownloadServiceException):
            await download_service.pause_download("nonexistent_task")
        
        with pytest.raises(DownloadServiceException):
            await download_service.resume_download("nonexistent_task")
        
        with pytest.raises(DownloadServiceException):
            await download_service.cancel_download("nonexistent_task")


class TestServiceValidationIntegration:
    """Integration tests for service validation framework."""
    
    def test_validation_result_creation(self):
        """Test creation and manipulation of validation results."""
        result = ServiceValidationResult(service_name="TestService", operation="test_operation")
        
        # Test adding issues
        result.add_error("Test error message")
        result.add_warning("Test warning message")
        result.add_info("Test info message")
        
        assert not result.is_valid  # Should be invalid due to error
        assert result.has_errors()
        assert result.has_warnings()
        assert len(result.issues) == 3
        
        # Test error message retrieval
        error_messages = result.get_error_messages()
        assert "Test error message" in error_messages
        
        warning_messages = result.get_warning_messages()
        assert "Test warning message" in warning_messages
    
    def test_validation_result_merging(self):
        """Test merging of validation results."""
        result1 = ServiceValidationResult(service_name="Service1")
        result1.add_error("Error from service 1")
        
        result2 = ServiceValidationResult(service_name="Service2")
        result2.add_warning("Warning from service 2")
        
        # Merge results
        result1.merge(result2)
        
        assert not result1.is_valid  # Should be invalid due to error
        assert len(result1.issues) == 2
        assert result1.has_errors()
        assert result1.has_warnings()
    
    def test_validation_result_serialization(self):
        """Test serialization of validation results."""
        result = ServiceValidationResult(service_name="TestService", operation="test_op")
        result.add_error("Test error", field="test_field", value="test_value")
        result.metadata["test_key"] = "test_value"
        
        # Convert to dictionary
        result_dict = result.to_dict()
        
        assert result_dict['service_name'] == "TestService"
        assert result_dict['operation'] == "test_op"
        assert not result_dict['is_valid']
        assert len(result_dict['issues']) == 1
        assert result_dict['metadata']['test_key'] == "test_value"
        assert result_dict['summary']['errors'] == 1


class TestProgressTrackerIntegration:
    """Integration tests for progress tracking functionality."""
    
    def test_progress_tracker_callbacks(self, tmp_path):
        """Test progress tracker callback functionality."""
        progress_tracker = ProgressTracker(storage_path=tmp_path)
        
        # Track callback invocations
        callback_events = []
        
        def test_callback(event: ProgressEvent):
            callback_events.append(event)
        
        # Register callback
        progress_tracker.register_callback(test_callback)
        
        # Update progress
        task_id = "test_task"
        progress_tracker.update_progress(task_id, 1024, 10240)
        progress_tracker.update_progress(task_id, 2048, 10240)
        
        # Check that callbacks were called
        assert len(callback_events) == 2
        assert callback_events[0].bytes_downloaded == 1024
        assert callback_events[1].bytes_downloaded == 2048
        
        # Unregister callback
        progress_tracker.unregister_callback(test_callback)
        
        # Update progress again
        progress_tracker.update_progress(task_id, 3072, 10240)
        
        # Should still be 2 events (callback not called)
        assert len(callback_events) == 2
    
    def test_progress_persistence(self, tmp_path):
        """Test progress persistence functionality."""
        progress_tracker = ProgressTracker(storage_path=tmp_path)
        
        task_id = "persistent_task"
        
        # Update progress
        event = progress_tracker.update_progress(task_id, 5120, 10240, "downloading")
        
        # Create new tracker instance (simulating restart)
        new_tracker = ProgressTracker(storage_path=tmp_path)
        
        # Should be able to load previous progress
        loaded_progress = new_tracker.get_last_progress(task_id)
        assert loaded_progress is not None
        assert loaded_progress.bytes_downloaded == 5120
        assert loaded_progress.total_bytes == 10240
        assert loaded_progress.status == "downloading"
