<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- UI Architecture Diagram for YouTube Downloader V2 -->
  
  <!-- Title -->
  <text x="400" y="30" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold">YouTube Downloader V2 - UI Architecture</text>
  
  <!-- Main Application -->
  <rect x="300" y="60" width="200" height="60" rx="10" ry="10" fill="#f9d5e5" stroke="#333" stroke-width="2"/>
  <text x="400" y="95" font-family="Arial" font-size="16" text-anchor="middle">Main Application</text>
  
  <!-- Main Window -->
  <rect x="300" y="150" width="200" height="60" rx="10" ry="10" fill="#d5f9e6" stroke="#333" stroke-width="2"/>
  <text x="400" y="185" font-family="Arial" font-size="16" text-anchor="middle">Main Window</text>
  
  <!-- Widgets -->
  <rect x="100" y="250" width="150" height="50" rx="10" ry="10" fill="#e6f9d5" stroke="#333" stroke-width="2"/>
  <text x="175" y="280" font-family="Arial" font-size="14" text-anchor="middle">Search Widget</text>
  
  <rect x="300" y="250" width="150" height="50" rx="10" ry="10" fill="#e6f9d5" stroke="#333" stroke-width="2"/>
  <text x="375" y="280" font-family="Arial" font-size="14" text-anchor="middle">Download Widget</text>
  
  <rect x="500" y="250" width="150" height="50" rx="10" ry="10" fill="#e6f9d5" stroke="#333" stroke-width="2"/>
  <text x="575" y="280" font-family="Arial" font-size="14" text-anchor="middle">Library Widget</text>
  
  <!-- Components -->
  <rect x="100" y="350" width="150" height="50" rx="10" ry="10" fill="#d5e6f9" stroke="#333" stroke-width="2"/>
  <text x="175" y="380" font-family="Arial" font-size="14" text-anchor="middle">Video Card</text>
  
  <rect x="300" y="350" width="150" height="50" rx="10" ry="10" fill="#d5e6f9" stroke="#333" stroke-width="2"/>
  <text x="375" y="380" font-family="Arial" font-size="14" text-anchor="middle">Progress Bar</text>
  
  <rect x="500" y="350" width="150" height="50" rx="10" ry="10" fill="#d5e6f9" stroke="#333" stroke-width="2"/>
  <text x="575" y="380" font-family="Arial" font-size="14" text-anchor="middle">Notification</text>
  
  <!-- Service Layer -->
  <rect x="200" y="450" width="400" height="60" rx="10" ry="10" fill="#f9e6d5" stroke="#333" stroke-width="2"/>
  <text x="400" y="485" font-family="Arial" font-size="16" text-anchor="middle">Service Layer (Phase 3)</text>
  
  <!-- Connections -->
  <!-- Main App to Main Window -->
  <line x1="400" y1="120" x2="400" y2="150" stroke="#333" stroke-width="2"/>
  
  <!-- Main Window to Widgets -->
  <line x1="400" y1="210" x2="175" y2="250" stroke="#333" stroke-width="2"/>
  <line x1="400" y1="210" x2="375" y2="250" stroke="#333" stroke-width="2"/>
  <line x1="400" y1="210" x2="575" y2="250" stroke="#333" stroke-width="2"/>
  
  <!-- Widgets to Components -->
  <line x1="175" y1="300" x2="175" y2="350" stroke="#333" stroke-width="2"/>
  <line x1="375" y1="300" x2="375" y2="350" stroke="#333" stroke-width="2"/>
  <line x1="575" y1="300" x2="575" y2="350" stroke="#333" stroke-width="2"/>
  
  <!-- Components to Service Layer -->
  <line x1="175" y1="400" x2="300" y2="450" stroke="#333" stroke-width="2"/>
  <line x1="375" y1="400" x2="375" y2="450" stroke="#333" stroke-width="2"/>
  <line x1="575" y1="400" x2="500" y2="450" stroke="#333" stroke-width="2"/>
  
  <!-- Legend -->
  <rect x="600" y="500" width="20" height="20" fill="#f9d5e5" stroke="#333" stroke-width="1"/>
  <text x="625" y="515" font-family="Arial" font-size="12" text-anchor="start">Application Entry Point</text>
  
  <rect x="600" y="525" width="20" height="20" fill="#d5f9e6" stroke="#333" stroke-width="1"/>
  <text x="625" y="540" font-family="Arial" font-size="12" text-anchor="start">Windows</text>
  
  <rect x="600" y="550" width="20" height="20" fill="#e6f9d5" stroke="#333" stroke-width="1"/>
  <text x="625" y="565" font-family="Arial" font-size="12" text-anchor="start">Widgets</text>
  
  <rect x="600" y="575" width="20" height="20" fill="#d5e6f9" stroke="#333" stroke-width="1"/>
  <text x="625" y="590" font-family="Arial" font-size="12" text-anchor="start">Components</text>
</svg>