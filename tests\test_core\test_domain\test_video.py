import unittest
from datetime import datetime
from pydantic import ValidationError

from src.core.domain.models.base import SizeInfo
from src.core.domain.models.video import (
    VideoQuality, VideoCodec, AudioCodec, VideoContainer,
    VideoFormat, VideoInfo
)

class TestVideoEnums(unittest.TestCase):
    def test_video_quality_enum(self):
        self.assertEqual(VideoQuality.AUDIO_ONLY, "audio_only")
        self.assertEqual(VideoQuality.LOW_144P, "144p")
        self.assertEqual(VideoQuality.MEDIUM_360P, "360p")
        self.assertEqual(VideoQuality.HIGH_1080P, "1080p")
        self.assertEqual(VideoQuality.ULTRA_4320P, "4320p")
    
    def test_video_codec_enum(self):
        self.assertEqual(VideoCodec.H264, "h264")
        self.assertEqual(VideoCodec.VP9, "vp9")
        self.assertEqual(VideoCodec.AV1, "av1")
    
    def test_audio_codec_enum(self):
        self.assertEqual(AudioCodec.MP3, "mp3")
        self.assertEqual(AudioCodec.AAC, "aac")
        self.assertEqual(AudioCodec.OPUS, "opus")
    
    def test_video_container_enum(self):
        self.assertEqual(VideoContainer.MP4, "mp4")
        self.assertEqual(VideoContainer.MKV, "mkv")
        self.assertEqual(VideoContainer.WEBM, "webm")

class TestVideoFormat(unittest.TestCase):
    def test_video_format_creation(self):
        format = VideoFormat(
            format_id="22",
            quality=VideoQuality.HIGH_720P,
            container=VideoContainer.MP4,
            video_codec=VideoCodec.H264,
            audio_codec=AudioCodec.AAC,
            file_size=SizeInfo(bytes=1024 * 1024 * 10),  # 10 MB
            bitrate=1500,
            fps=30,
            width=1280,
            height=720
        )
        
        self.assertEqual(format.format_id, "22")
        self.assertEqual(format.quality, VideoQuality.HIGH_720P)
        self.assertEqual(format.container, VideoContainer.MP4)
        self.assertEqual(format.video_codec, VideoCodec.H264)
        self.assertEqual(format.audio_codec, AudioCodec.AAC)
        self.assertEqual(format.file_size.bytes, 1024 * 1024 * 10)
        self.assertEqual(format.bitrate, 1500)
        self.assertEqual(format.fps, 30)
        self.assertEqual(format.width, 1280)
        self.assertEqual(format.height, 720)
        self.assertFalse(format.is_audio_only)
    
    def test_audio_only_format(self):
        format = VideoFormat(
            format_id="140",
            quality=VideoQuality.AUDIO_ONLY,
            container=VideoContainer.MP4,
            audio_codec=AudioCodec.MP3,
            is_audio_only=True,
            file_size=SizeInfo(bytes=1024 * 1024 * 3),  # 3 MB
            bitrate=128
        )
        
        self.assertEqual(format.quality, VideoQuality.AUDIO_ONLY)
        self.assertTrue(format.is_audio_only)
        self.assertIsNone(format.video_codec)
        self.assertEqual(format.audio_codec, AudioCodec.MP3)
    
    def test_resolution_property(self):
        # With width and height
        format = VideoFormat(
            format_id="22",
            quality=VideoQuality.HIGH_720P,
            container=VideoContainer.MP4,
            width=1280,
            height=720
        )
        self.assertEqual(format.resolution, "1280x720")
        
        # Without width and height
        format = VideoFormat(
            format_id="140",
            quality=VideoQuality.AUDIO_ONLY,
            container=VideoContainer.MP4,
            is_audio_only=True
        )
        self.assertIsNone(format.resolution)
    
    def test_display_name_property(self):
        # Video format
        format = VideoFormat(
            format_id="22",
            quality=VideoQuality.HIGH_720P,
            container=VideoContainer.MP4,
            video_codec=VideoCodec.H264
        )
        self.assertEqual(format.display_name, "720p (mp4, h264)")
        
        # Audio format
        format = VideoFormat(
            format_id="140",
            quality=VideoQuality.AUDIO_ONLY,
            container=VideoContainer.MP4,
            audio_codec=AudioCodec.MP3,
            is_audio_only=True,
            bitrate=128
        )
        self.assertEqual(format.display_name, "Audio only (mp3, 128 kbps)")
        
        # Unknown codec
        format = VideoFormat(
            format_id="unknown",
            quality=VideoQuality.HIGH_720P,
            container=VideoContainer.MP4
        )
        self.assertEqual(format.display_name, "720p (mp4, unknown)")

class TestVideoInfo(unittest.TestCase):
    def setUp(self):
        # Create a sample video format
        self.format_720p = VideoFormat(
            format_id="22",
            quality=VideoQuality.HIGH_720P,
            container=VideoContainer.MP4,
            video_codec=VideoCodec.H264,
            audio_codec=AudioCodec.AAC,
            file_size=SizeInfo(bytes=1024 * 1024 * 10),  # 10 MB
            bitrate=1500,
            fps=30,
            width=1280,
            height=720
        )
        
        self.format_1080p = VideoFormat(
            format_id="137",
            quality=VideoQuality.HIGH_1080P,
            container=VideoContainer.MP4,
            video_codec=VideoCodec.H264,
            file_size=SizeInfo(bytes=1024 * 1024 * 20),  # 20 MB
            bitrate=3000,
            fps=30,
            width=1920,
            height=1080
        )
        
        self.format_audio = VideoFormat(
            format_id="140",
            quality=VideoQuality.AUDIO_ONLY,
            container=VideoContainer.MP4,
            audio_codec=AudioCodec.MP3,
            is_audio_only=True,
            file_size=SizeInfo(bytes=1024 * 1024 * 3),  # 3 MB
            bitrate=128
        )
        
        # Create a sample video info
        self.video_info = VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title="Rick Astley - Never Gonna Give You Up (Official Music Video)",
            description="The official music video for \"Never Gonna Give You Up\" by Rick Astley",
            channel_id="UCuAXFkgsw1L7xaCfnd5JJOw",
            channel_name="Rick Astley",
            duration=212,  # 3:32
            view_count=1234567890,
            upload_date=datetime(2009, 10, 25),
            thumbnail_url="https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
            available_formats=[self.format_720p, self.format_1080p, self.format_audio],
            tags=["Rick Astley", "Never Gonna Give You Up", "Official Music Video"],
            categories=["Music"],
            is_live=False
        )
    
    def test_video_info_creation(self):
        self.assertEqual(self.video_info.video_id, "dQw4w9WgXcQ")
        self.assertEqual(self.video_info.url, "https://www.youtube.com/watch?v=dQw4w9WgXcQ")
        self.assertEqual(self.video_info.title, "Rick Astley - Never Gonna Give You Up (Official Music Video)")
        self.assertEqual(self.video_info.channel_name, "Rick Astley")
        self.assertEqual(self.video_info.duration, 212)
        self.assertEqual(len(self.video_info.available_formats), 3)
        self.assertEqual(len(self.video_info.tags), 3)
    
    def test_video_id_validation(self):
        # Valid video ID
        VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title="Test Video"
        )
        
        # Invalid video ID (too short)
        with self.assertRaises(ValidationError):
            VideoInfo(
                video_id="short",
                url="https://www.youtube.com/watch?v=short",
                title="Test Video"
            )
        
        # Invalid video ID (invalid characters)
            with self.assertRaises(ValidationError):
                VideoInfo(
                    video_id="invalid!@#",
                    url="https://www.youtube.com/watch?v=invalid!@#",
                    title="Test Video"
                )
    
    def test_url_validation(self):
        # Valid URLs
        VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title="Test Video"
        )
        
        VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://youtu.be/dQw4w9WgXcQ",
            title="Test Video"
        )
        
        # Invalid URL (not YouTube)
        with self.assertRaises(ValidationError):
            VideoInfo(
                video_id="dQw4w9WgXcQ",
                url="https://example.com/video",
                title="Test Video"
            )
    
    def test_duration_formatted(self):
        # Test minutes:seconds format
        self.assertEqual(self.video_info.duration_formatted, "03:32")
        
        # Test hours:minutes:seconds format
        video_info = VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title="Long Video",
            duration=3661  # 1:01:01
        )
        self.assertEqual(video_info.duration_formatted, "01:01:01")
        
        # Test unknown duration
        video_info = VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title="Unknown Duration"
        )
        self.assertEqual(video_info.duration_formatted, "Unknown")
    
    def test_get_safe_filename(self):
        # Test normal title
        self.assertEqual(
            self.video_info.get_safe_filename(),
            "Rick Astley - Never Gonna Give You Up (Official Music Video)_dQw4w9WgXcQ"
        )
        
        # Test title with invalid characters
        video_info = VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title="Invalid/File:Name?*"
        )
        self.assertEqual(
            video_info.get_safe_filename(),
            "Invalid_File_Name___dQw4w9WgXcQ"
        )
        
        # Test no title
        video_info = VideoInfo(
            video_id="dQw4w9WgXcQ",
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            title=""
        )
        self.assertEqual(
            video_info.get_safe_filename(),
            "video_dQw4w9WgXcQ"
        )
    
    def test_get_best_format(self):
        # Test get best format without criteria (should return highest quality)
        best_format = self.video_info.get_best_format()
        self.assertEqual(best_format.quality, VideoQuality.HIGH_1080P)
        
        # Test get best format with quality criteria
        best_format = self.video_info.get_best_format(quality=VideoQuality.HIGH_720P)
        self.assertEqual(best_format.quality, VideoQuality.HIGH_720P)
        
        # Test get best format with container criteria
        best_format = self.video_info.get_best_format(container=VideoContainer.MP4)
        self.assertEqual(best_format.container, VideoContainer.MP4)
        
        # Test get best audio format
        best_format = self.video_info.get_best_format(audio_only=True)
        self.assertTrue(best_format.is_audio_only)
        
        # Test no matching format
        best_format = self.video_info.get_best_format(quality=VideoQuality.ULTRA_4320P)
        self.assertIsNone(best_format)

if __name__ == "__main__":
    unittest.main()