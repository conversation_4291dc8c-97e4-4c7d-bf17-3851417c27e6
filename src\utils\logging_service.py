#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Logging Service Implementation

This module implements a logging service for the application, providing
structured logging with different output formats and destinations.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from enum import Enum, auto
from pathlib import Path
from typing import Dict, List, Optional, Union, Any


class LogLevel(str, Enum):
    """Log level options"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogFormat(str, Enum):
    """Log format options"""
    TEXT = "text"
    JSON = "json"


class LogDestination(str, Enum):
    """Log destination options"""
    CONSOLE = "console"
    FILE = "file"
    BOTH = "both"


class LoggingServiceException(Exception):
    """Base exception for logging service errors."""
    pass


class LoggingConfigError(LoggingServiceException):
    """Raised when logging configuration is invalid."""
    pass


class LoggingService:
    """Service for application logging."""

    def __init__(self, app_name: str = "YouTubeDownloader"):
        """Initialize logging service.
        
        Args:
            app_name: Name of the application for logging context
        """
        self.app_name = app_name
        self.logger = logging.getLogger(app_name)
        self.handlers: Dict[str, logging.Handler] = {}
        self.initialized = False

    async def setup(self, 
                   log_level: Union[LogLevel, str] = LogLevel.INFO,
                   log_format: Union[LogFormat, str] = LogFormat.TEXT,
                   log_destination: Union[LogDestination, str] = LogDestination.BOTH,
                   log_dir: Optional[Union[str, Path]] = None) -> None:
        """Set up logging configuration.
        
        Args:
            log_level: Logging level
            log_format: Logging format (text or JSON)
            log_destination: Where to send logs (console, file, or both)
            log_dir: Directory for log files (required if destination includes file)
            
        Raises:
            LoggingConfigError: If configuration is invalid
        """
        # Convert string enums to enum values if needed
        if isinstance(log_level, str):
            try:
                log_level = LogLevel(log_level.upper())
            except ValueError:
                raise LoggingConfigError(f"Invalid log level: {log_level}")
                
        if isinstance(log_format, str):
            try:
                log_format = LogFormat(log_format.lower())
            except ValueError:
                raise LoggingConfigError(f"Invalid log format: {log_format}")
                
        if isinstance(log_destination, str):
            try:
                log_destination = LogDestination(log_destination.lower())
            except ValueError:
                raise LoggingConfigError(f"Invalid log destination: {log_destination}")
        
        # Set up root logger
        root_logger = logging.getLogger()
        
        # Convert log level to standard logging level
        if isinstance(log_level, LogLevel):
            level_map = {
                LogLevel.DEBUG: logging.DEBUG,
                LogLevel.INFO: logging.INFO,
                LogLevel.WARNING: logging.WARNING,
                LogLevel.ERROR: logging.ERROR,
                LogLevel.CRITICAL: logging.CRITICAL
            }
            log_level = level_map.get(log_level, logging.INFO)
        elif isinstance(log_level, str):
            level_map = {
                "DEBUG": logging.DEBUG,
                "INFO": logging.INFO,
                "WARNING": logging.WARNING,
                "ERROR": logging.ERROR,
                "CRITICAL": logging.CRITICAL
            }
            log_level = level_map.get(log_level.upper(), logging.INFO)
            
        root_logger.setLevel(log_level)
        
        # Clear existing handlers
        await self.shutdown()
        
        # Create formatters
        if log_format == LogFormat.TEXT:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        else:  # JSON format
            formatter = self._create_json_formatter()
        
        # Set up handlers based on destination
        if log_destination in [LogDestination.CONSOLE, LogDestination.BOTH]:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
            self.handlers['console'] = console_handler
        
        if log_destination in [LogDestination.FILE, LogDestination.BOTH]:
            if not log_dir:
                raise LoggingConfigError("Log directory must be specified for file logging")
                
            # Ensure log directory exists
            log_dir_path = Path(log_dir)
            os.makedirs(log_dir_path, exist_ok=True)
            
            # Create timestamped log file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = log_dir_path / f"{self.app_name}_{timestamp}.log"
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            self.handlers['file'] = file_handler
        
        self.initialized = True
        self.logger.info(f"Logging initialized: level={log_level}, format={log_format}, destination={log_destination}")

    def _create_json_formatter(self) -> logging.Formatter:
        """Create a JSON formatter for structured logging."""
        class JsonFormatter(logging.Formatter):
            def format(self, record):
                log_data = {
                    "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                    "level": record.levelname,
                    "logger": record.name,
                    "message": record.getMessage(),
                    "module": record.module,
                    "function": record.funcName,
                    "line": record.lineno
                }
                
                # Add exception info if available
                if record.exc_info:
                    log_data["exception"] = self.formatException(record.exc_info)
                    
                # Add extra attributes
                for key, value in record.__dict__.items():
                    if key.startswith('_') or key in log_data:
                        continue
                    log_data[key] = value
                    
                import json
                return json.dumps(log_data)
                
        return JsonFormatter()

    async def shutdown(self) -> None:
        """Shutdown logging system and remove handlers."""
        root_logger = logging.getLogger()
        
        # Remove and close all handlers
        for handler in root_logger.handlers[:]:
            handler.close()
            root_logger.removeHandler(handler)
            
        self.handlers.clear()
        self.initialized = False

    def get_logger(self, name: str) -> logging.Logger:
        """Get a named logger.
        
        Args:
            name: Logger name, typically the module name
            
        Returns:
            Logger instance
        """
        if not self.initialized:
            self.logger.warning("Logging service not initialized, using default configuration")
            
        return logging.getLogger(f"{self.app_name}.{name}")

    def add_context(self, **context) -> None:
        """Add context to all log records.
        
        Args:
            **context: Key-value pairs to add to log context
        """
        class ContextFilter(logging.Filter):
            def __init__(self, context_data):
                super().__init__()
                self.context_data = context_data
                
            def filter(self, record):
                for key, value in self.context_data.items():
                    setattr(record, key, value)
                return True
                
        # Create and add the filter to the root logger
        context_filter = ContextFilter(context)
        logging.getLogger().addFilter(context_filter)


# Global instance for convenience
logging_service = LoggingService()


# Convenience functions
async def setup_logging(**kwargs) -> None:
    """Set up logging with the given configuration."""
    await logging_service.setup(**kwargs)


def get_logger(name: str) -> logging.Logger:
    """Get a named logger."""
    return logging_service.get_logger(name)


# Example usage
async def main():
    """Example usage of LoggingService."""
    # Set up logging
    await setup_logging(
        log_level=LogLevel.DEBUG,
        log_format=LogFormat.TEXT,
        log_destination=LogDestination.BOTH,
        log_dir="./logs"
    )
    
    # Get a logger
    logger = get_logger("example")
    
    # Add context
    logging_service.add_context(user_id="test_user", session_id="123456")
    
    # Log messages
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    try:
        # Simulate an error
        raise ValueError("Example error")
    except Exception as e:
        logger.exception("An error occurred")
    
    # Shutdown logging
    await logging_service.shutdown()


if __name__ == "__main__":
    asyncio.run(main())