/* YouTube Downloader V2 Dark Theme Styles */

/* General <PERSON> */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
    background-color: #2d2d2d;
    color: #e0e0e0;
}

QMainWindow, QDialog {
    background-color: #2d2d2d;
}

/* Header Styles */
QLabel[header="true"] {
    font-size: 16pt;
    font-weight: bold;
    color: #ff4444;
}

QLabel[subheader="true"] {
    font-size: 12pt;
    font-weight: bold;
    color: #e0e0e0;
}

/* Button Styles */
QPushButton {
    background-color: #3d3d3d;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 6px 12px;
    color: #e0e0e0;
}

QPushButton:hover {
    background-color: #4d4d4d;
    border-color: #666666;
}

QPushButton:pressed {
    background-color: #5d5d5d;
    border-color: #777777;
}

QPushButton:disabled {
    background-color: #353535;
    border-color: #444444;
    color: #777777;
}

/* Primary Button */
QPushButton[primary="true"] {
    background-color: #cc0000;
    border-color: #aa0000;
    color: white;
}

QPushButton[primary="true"]:hover {
    background-color: #bb0000;
    border-color: #990000;
}

QPushButton[primary="true"]:pressed {
    background-color: #aa0000;
    border-color: #880000;
}

QPushButton[primary="true"]:disabled {
    background-color: #662222;
    border-color: #551111;
    color: #aa7777;
}

/* Secondary Button */
QPushButton[secondary="true"] {
    background-color: #4285f4;
    border-color: #3275e4;
    color: white;
}

QPushButton[secondary="true"]:hover {
    background-color: #3275e4;
    border-color: #2265d4;
}

QPushButton[secondary="true"]:pressed {
    background-color: #2265d4;
    border-color: #1255c4;
}

QPushButton[secondary="true"]:disabled {
    background-color: #2a4277;
    border-color: #1a3267;
    color: #7a92b7;
}

/* Input Styles */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #3d3d3d;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px;
    color: #e0e0e0;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #4285f4;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background-color: #353535;
    border-color: #444444;
    color: #777777;
}

/* Combo Box Styles */
QComboBox {
    background-color: #3d3d3d;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px;
    color: #e0e0e0;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: right center;
    width: 20px;
    border-left: 1px solid #555555;
}

QComboBox::down-arrow {
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #555555;
    background-color: #3d3d3d;
    selection-background-color: #4285f4;
    selection-color: white;
}

/* Tab Widget Styles */
QTabWidget::pane {
    border: 1px solid #555555;
    border-top: 0px;
    background-color: #2d2d2d;
}

QTabBar::tab {
    background-color: #3d3d3d;
    border: 1px solid #555555;
    border-bottom: 0px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 6px 12px;
    color: #e0e0e0;
}

QTabBar::tab:selected {
    background-color: #2d2d2d;
    border-bottom: 0px;
}

QTabBar::tab:hover:!selected {
    background-color: #4d4d4d;
}

/* Progress Bar Styles */
QProgressBar {
    border: 1px solid #555555;
    border-radius: 4px;
    background-color: #3d3d3d;
    text-align: center;
    color: #e0e0e0;
}

QProgressBar::chunk {
    background-color: #4285f4;
    width: 10px;
    margin: 0.5px;
}

/* Table View Styles */
QTableView {
    border: 1px solid #555555;
    background-color: #2d2d2d;
    gridline-color: #3d3d3d;
    selection-background-color: #4285f4;
    selection-color: white;
    alternate-background-color: #333333;
}

QTableView QHeaderView::section {
    background-color: #3d3d3d;
    border: 1px solid #555555;
    border-left: 0px;
    border-top: 0px;
    padding: 4px;
    color: #e0e0e0;
    font-weight: bold;
}

QTableView QHeaderView::section:first {
    border-left: 1px solid #555555;
}

/* List View Styles */
QListView {
    border: 1px solid #555555;
    background-color: #2d2d2d;
    alternate-background-color: #333333;
    selection-background-color: #4285f4;
    selection-color: white;
}

QListView::item {
    padding: 4px;
}

QListView::item:hover {
    background-color: #3d3d3d;
}

QListView::item:selected {
    background-color: #4285f4;
    color: white;
}

/* Scroll Bar Styles */
QScrollBar:vertical {
    border: none;
    background-color: #3d3d3d;
    width: 12px;
    margin: 12px 0px 12px 0px;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar::add-line:vertical {
    border: none;
    background-color: #3d3d3d;
    height: 12px;
    subcontrol-position: bottom;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:vertical {
    border: none;
    background-color: #3d3d3d;
    height: 12px;
    subcontrol-position: top;
    subcontrol-origin: margin;
}

QScrollBar:horizontal {
    border: none;
    background-color: #3d3d3d;
    height: 12px;
    margin: 0px 12px 0px 12px;
}

QScrollBar::handle:horizontal {
    background-color: #555555;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #666666;
}

QScrollBar::add-line:horizontal {
    border: none;
    background-color: #3d3d3d;
    width: 12px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:horizontal {
    border: none;
    background-color: #3d3d3d;
    width: 12px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}

/* Menu Styles */
QMenuBar {
    background-color: #2d2d2d;
    border-bottom: 1px solid #3d3d3d;
}

QMenuBar::item {
    spacing: 6px;
    padding: 4px 8px;
    background: transparent;
}

QMenuBar::item:selected {
    background-color: #3d3d3d;
}

QMenu {
    background-color: #2d2d2d;
    border: 1px solid #555555;
}

QMenu::item {
    padding: 6px 24px 6px 24px;
}

QMenu::item:selected {
    background-color: #4285f4;
    color: white;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 4px 0px 4px 0px;
}

/* Toolbar Styles */
QToolBar {
    background-color: #2d2d2d;
    border-bottom: 1px solid #3d3d3d;
    spacing: 4px;
}

QToolBar::separator {
    width: 1px;
    background-color: #555555;
    margin: 4px 4px 4px 4px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 4px;
}

QToolButton:hover {
    background-color: #3d3d3d;
    border-color: #555555;
}

QToolButton:pressed {
    background-color: #4d4d4d;
    border-color: #666666;
}

/* Status Bar Styles */
QStatusBar {
    background-color: #2d2d2d;
    border-top: 1px solid #3d3d3d;
}

QStatusBar::item {
    border: none;
}

/* Group Box Styles */
QGroupBox {
    border: 1px solid #555555;
    border-radius: 4px;
    margin-top: 12px;
    padding-top: 12px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0px 4px;
    color: #e0e0e0;
    font-weight: bold;
}

/* Spin Box Styles */
QSpinBox, QDoubleSpinBox {
    background-color: #3d3d3d;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px;
    color: #e0e0e0;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    width: 16px;
    border-left: 1px solid #555555;
    border-bottom: 1px solid #555555;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    width: 16px;
    border-left: 1px solid #555555;
    border-top: 1px solid #555555;
}

/* Check Box Styles */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #555555;
    background-color: #3d3d3d;
    border-radius: 3px;
}

QCheckBox::indicator:checked {
    border: 1px solid #4285f4;
    background-color: #4285f4;
    border-radius: 3px;
}

/* Radio Button Styles */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
}

QRadioButton::indicator:unchecked {
    border: 1px solid #555555;
    background-color: #3d3d3d;
    border-radius: 9px;
}

QRadioButton::indicator:checked {
    border: 1px solid #4285f4;
    background-color: #4285f4;
    border-radius: 9px;
}

/* Slider Styles */
QSlider::groove:horizontal {
    border: 1px solid #555555;
    height: 8px;
    background-color: #3d3d3d;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background-color: #4285f4;
    border: 1px solid #3275e4;
    width: 18px;
    height: 18px;
    margin: -6px 0px;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background-color: #3275e4;
    border-color: #2265d4;
}

/* Custom Styles for YouTube Downloader */

/* Video Card */
QFrame[videoCard="true"] {
    background-color: #333333;
    border: 1px solid #444444;
    border-radius: 8px;
}

QFrame[videoCard="true"]:hover {
    border-color: #4285f4;
    background-color: #3a3a3a;
}

/* Video Title */
QLabel[videoTitle="true"] {
    font-weight: bold;
    font-size: 11pt;
    color: #e0e0e0;
}

/* Video Channel */
QLabel[videoChannel="true"] {
    color: #ff4444;
    font-size: 10pt;
}

/* Video Stats */
QLabel[videoStats="true"] {
    color: #aaaaaa;
    font-size: 9pt;
}

/* Download Button */
QPushButton[downloadButton="true"] {
    background-color: #cc0000;
    border-color: #aa0000;
    color: white;
    font-weight: bold;
}

QPushButton[downloadButton="true"]:hover {
    background-color: #bb0000;
    border-color: #990000;
}

/* Watch Button */
QPushButton[watchButton="true"] {
    background-color: #4285f4;
    border-color: #3275e4;
    color: white;
}

QPushButton[watchButton="true"]:hover {
    background-color: #3275e4;
    border-color: #2265d4;
}

/* Search Box */
QLineEdit[searchBox="true"] {
    border: 1px solid #555555;
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 11pt;
    background-color: #3d3d3d;
}

QLineEdit[searchBox="true"]:focus {
    border-color: #4285f4;
}

/* Search Button */
QPushButton[searchButton="true"] {
    background-color: #3d3d3d;
    border: 1px solid #555555;
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: bold;
}

QPushButton[searchButton="true"]:hover {
    background-color: #4d4d4d;
    border-color: #666666;
}

/* Download Progress */
QProgressBar[downloadProgress="true"] {
    border: 1px solid #555555;
    border-radius: 4px;
    background-color: #3d3d3d;
    text-align: center;
    color: #e0e0e0;
    font-weight: bold;
}

QProgressBar[downloadProgress="true"]::chunk {
    background-color: #4285f4;
    width: 10px;
    margin: 0.5px;
}

/* Library Item */
QFrame[libraryItem="true"] {
    background-color: #333333;
    border: 1px solid #444444;
    border-radius: 4px;
}

QFrame[libraryItem="true"]:hover {
    border-color: #4285f4;
    background-color: #3a3a3a;
}

/* Playlist Item */
QFrame[playlistItem="true"] {
    background-color: #333333;
    border: 1px solid #444444;
    border-radius: 4px;
    padding: 4px;
}

QFrame[playlistItem="true"]:hover {
    border-color: #4285f4;
    background-color: #3a3a3a;
}

/* Status Message */
QLabel[statusMessage="true"] {
    color: #aaaaaa;
    font-style: italic;
}

/* Error Message */
QLabel[errorMessage="true"] {
    color: #ff4444;
    font-weight: bold;
}

/* Success Message */
QLabel[successMessage="true"] {
    color: #4CAF50;
    font-weight: bold;
}