#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
YouTube Downloader Application Example

This module demonstrates how to use the service layer to build a command-line
application for downloading YouTube videos and playlists.
"""

import argparse
import asyncio
import os
import re
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Set

from src.services.service_interfaces import (
    IServiceFactory, IYouTubeService, IDownloadService, 
    IFileService, IConfigService, DownloadTask, VideoQuality, DownloadStatus
)
from src.services.service_factory import AsyncServiceFactory
from src.utils.validation_framework import ValidationResult


class YouTubeDownloaderApp:
    """YouTube Downloader Application."""
    
    def __init__(self):
        """Initialize application."""
        self.service_factory = AsyncServiceFactory()
        self.youtube_service: Optional[IYouTubeService] = None
        self.download_service: Optional[IDownloadService] = None
        self.file_service: Optional[IFileService] = None
        self.config_service: Optional[IConfigService] = None
        self.config = None
        
    async def initialize(self):
        """Initialize services."""
        # Create services
        services = await self.service_factory.create_all_services_async()
        
        # Get individual services
        self.youtube_service = services[IYouTubeService]
        self.download_service = services[IDownloadService]
        self.file_service = services[IFileService]
        self.config_service = services[IConfigService]
        
        # Load configuration
        await self.load_config()
        
    async def load_config(self):
        """Load configuration."""
        self.config = await self.config_service.load_config()
        
        # Ensure download directory exists
        await self.ensure_download_directory()
        
    async def ensure_download_directory(self):
        """Ensure download directory exists."""
        if not self.config.download_dir:
            print("Error: Download directory not set in configuration.")
            return
            
        try:
            await self.file_service.create_directory(self.config.download_dir)
            print(f"Download directory: {self.config.download_dir}")
        except Exception as e:
            print(f"Error creating download directory: {e}")
            
    async def download_video(self, url: str, quality: Optional[VideoQuality] = None):
        """Download a single video.
        
        Args:
            url: YouTube video URL
            quality: Optional video quality
        """
        try:
            # Validate URL
            validation_result = await self.youtube_service.validate_url(url)
            if not validation_result.is_valid:
                print(f"Error: {validation_result.message}")
                return
                
            # Get video info
            print(f"Fetching video information for {url}...")
            video_info = await self.youtube_service.extract_video_info(url)
            
            if not video_info:
                print("Error: Failed to extract video information.")
                return
                
            print(f"Video title: {video_info.title}")
            print(f"Duration: {video_info.duration} seconds")
            
            # Create safe filename
            filename = self._create_safe_filename(video_info.title) + ".mp4"
            destination = Path(self.config.download_dir) / filename
            
            # Use provided quality or default from config
            video_quality = quality or self.config.default_format
            
            # Create download task
            task = DownloadTask(
                url=url,
                destination=str(destination),
                quality=video_quality,
                video_info=video_info
            )
            
            # Start download
            task_id = await self.download_service.start_download(task)
            print(f"Started download with task ID: {task_id}")
            
            # Monitor progress
            await self._monitor_download_progress(task_id)
            
        except Exception as e:
            print(f"Error downloading video: {e}")
            
    async def download_playlist(self, url: str, quality: Optional[VideoQuality] = None):
        """Download a playlist.
        
        Args:
            url: YouTube playlist URL
            quality: Optional video quality
        """
        try:
            # Validate URL
            validation_result = await self.youtube_service.validate_url(url, is_playlist=True)
            if not validation_result.is_valid:
                print(f"Error: {validation_result.message}")
                return
                
            # Get playlist info
            print(f"Fetching playlist information for {url}...")
            playlist_info = await self.youtube_service.extract_playlist_info(url)
            
            if not playlist_info or not playlist_info.videos:
                print("Error: Failed to extract playlist information or playlist is empty.")
                return
                
            print(f"Playlist title: {playlist_info.title}")
            print(f"Number of videos: {len(playlist_info.videos)}")
            
            # Create playlist directory
            playlist_dir_name = self._create_safe_filename(playlist_info.title)
            playlist_dir = Path(self.config.download_dir) / playlist_dir_name
            await self.file_service.create_directory(playlist_dir)
            
            # Use provided quality or default from config
            video_quality = quality or self.config.default_format
            
            # Start downloads for each video
            task_ids = []
            for i, video_info in enumerate(playlist_info.videos):
                # Create safe filename
                filename = f"{i+1:03d}_{self._create_safe_filename(video_info.title)}.mp4"
                destination = playlist_dir / filename
                
                # Create download task
                task = DownloadTask(
                    url=video_info.url,
                    destination=str(destination),
                    quality=video_quality,
                    video_info=video_info
                )
                
                # Start download
                task_id = await self.download_service.start_download(task)
                task_ids.append(task_id)
                print(f"Started download for '{video_info.title}' with task ID: {task_id}")
                
                # Wait a bit before starting next download to avoid rate limiting
                await asyncio.sleep(0.5)
                
            # Monitor progress for all tasks
            await self._monitor_multiple_download_progress(task_ids)
            
        except Exception as e:
            print(f"Error downloading playlist: {e}")
            
    async def _monitor_download_progress(self, task_id: str):
        """Monitor download progress for a single task.
        
        Args:
            task_id: Download task ID
        """
        try:
            # Monitor progress until complete, failed, or canceled
            while True:
                # Get progress
                progress = await self.download_service.get_progress(task_id)
                
                if not progress:
                    print("Error: Failed to get progress information.")
                    break
                    
                # Print progress
                status_text = progress.status.name
                if progress.status == DownloadStatus.DOWNLOADING:
                    # Calculate speed in MB/s
                    speed_mb = progress.speed / (1024 * 1024)
                    
                    # Format ETA
                    eta_min = progress.eta // 60
                    eta_sec = progress.eta % 60
                    eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                    
                    # Print progress bar
                    bar_length = 30
                    filled_length = int(bar_length * progress.progress_percentage / 100)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    
                    print(f"\r[{bar}] {progress.progress_percentage:.1f}% - {speed_mb:.2f} MB/s - ETA: {eta_text}", end="")
                else:
                    # Print status for non-downloading states
                    print(f"\rStatus: {status_text}", end="")
                    
                # Check if download is complete, failed, or canceled
                if progress.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELED]:
                    print("\nDownload finished with status:", status_text)
                    break
                    
                # Wait before checking again
                await asyncio.sleep(0.5)
                
        except Exception as e:
            print(f"\nError monitoring download: {e}")
            
    async def _monitor_multiple_download_progress(self, task_ids: List[str]):
        """Monitor download progress for multiple tasks.
        
        Args:
            task_ids: List of download task IDs
        """
        try:
            # Set to track completed tasks
            completed_tasks: Set[str] = set()
            
            # Monitor progress until all tasks are complete, failed, or canceled
            while len(completed_tasks) < len(task_ids):
                # Clear screen
                os.system('cls' if os.name == 'nt' else 'clear')
                
                # Print header
                print(f"Monitoring {len(task_ids)} downloads:")
                print("-" * 80)
                
                # Get all tasks
                all_tasks = await self.download_service.get_all_tasks()
                
                # Create a dictionary of tasks by ID for easy lookup
                tasks_dict: Dict[str, DownloadTask] = {task.task_id: task for task in all_tasks if task.task_id in task_ids}
                
                # Print progress for each task
                for task_id in task_ids:
                    if task_id not in tasks_dict:
                        print(f"Task {task_id}: Not found")
                        completed_tasks.add(task_id)
                        continue
                        
                    task = tasks_dict[task_id]
                    progress = task.progress
                    
                    if not progress:
                        print(f"Task {task_id}: No progress information")
                        continue
                        
                    # Get video title
                    title = task.video_info.title if task.video_info else "Unknown"
                    
                    # Print progress
                    status_text = progress.status.name
                    if progress.status == DownloadStatus.DOWNLOADING:
                        # Calculate speed in MB/s
                        speed_mb = progress.speed / (1024 * 1024)
                        
                        # Format ETA
                        eta_min = progress.eta // 60
                        eta_sec = progress.eta % 60
                        eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                        
                        # Print progress bar
                        bar_length = 30
                        filled_length = int(bar_length * progress.progress_percentage / 100)
                        bar = '█' * filled_length + '░' * (bar_length - filled_length)
                        
                        print(f"[{bar}] {progress.progress_percentage:.1f}% - {speed_mb:.2f} MB/s - ETA: {eta_text}")
                        print(f"Title: {title}")
                    else:
                        # Print status for non-downloading states
                        print(f"Status: {status_text}")
                        print(f"Title: {title}")
                        
                    print("-" * 80)
                    
                    # Check if download is complete, failed, or canceled
                    if progress.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELED]:
                        completed_tasks.add(task_id)
                        
                # Print summary
                print(f"Completed: {len(completed_tasks)}/{len(task_ids)}")
                
                # Wait before checking again
                await asyncio.sleep(1)
                
            print("All downloads finished.")
                
        except Exception as e:
            print(f"Error monitoring downloads: {e}")
            
    async def list_downloads(self):
        """List all download tasks."""
        try:
            # Get all tasks
            tasks = await self.download_service.get_all_tasks()
            
            if not tasks:
                print("No download tasks found.")
                return
                
            # Print header
            print(f"Found {len(tasks)} download tasks:")
            print("-" * 80)
            
            # Print task information
            for i, task in enumerate(tasks):
                progress = task.progress
                status_text = progress.status.name if progress else "Unknown"
                
                # Get video title
                title = task.video_info.title if task.video_info else "Unknown"
                
                # Print task information
                print(f"Task {i+1}:")
                print(f"  ID: {task.task_id}")
                print(f"  Title: {title}")
                print(f"  URL: {task.url}")
                print(f"  Destination: {task.destination}")
                print(f"  Status: {status_text}")
                
                if progress and progress.status == DownloadStatus.DOWNLOADING:
                    print(f"  Progress: {progress.progress_percentage:.1f}%")
                    print(f"  Speed: {progress.speed / (1024 * 1024):.2f} MB/s")
                    
                print("-" * 80)
                
        except Exception as e:
            print(f"Error listing downloads: {e}")
            
    async def update_config(self, download_dir: Optional[str] = None, max_concurrent_downloads: Optional[int] = None, default_format: Optional[str] = None):
        """Update configuration settings.
        
        Args:
            download_dir: Optional download directory
            max_concurrent_downloads: Optional maximum concurrent downloads
            default_format: Optional default format
        """
        try:
            # Load current config
            config = await self.config_service.load_config()
            
            # Update settings if provided
            if download_dir is not None:
                # Validate and create directory
                try:
                    await self.file_service.validate_path(download_dir)
                    await self.file_service.create_directory(download_dir)
                    await self.config_service.update_setting('download_dir', download_dir)
                    print(f"Updated download directory to: {download_dir}")
                except Exception as e:
                    print(f"Error updating download directory: {e}")
                    
            if max_concurrent_downloads is not None:
                try:
                    await self.config_service.update_setting('max_concurrent_downloads', max_concurrent_downloads)
                    print(f"Updated maximum concurrent downloads to: {max_concurrent_downloads}")
                except Exception as e:
                    print(f"Error updating maximum concurrent downloads: {e}")
                    
            if default_format is not None:
                try:
                    # Convert string to VideoQuality enum
                    quality = VideoQuality[default_format.upper()]
                    await self.config_service.update_setting('default_format', quality)
                    print(f"Updated default format to: {quality.name}")
                except KeyError:
                    print(f"Error: Invalid format '{default_format}'. Valid formats are: {', '.join([q.name for q in VideoQuality])}")
                except Exception as e:
                    print(f"Error updating default format: {e}")
                    
            # Reload config
            await self.load_config()
            
        except Exception as e:
            print(f"Error updating configuration: {e}")
            
    async def show_config(self):
        """Show current configuration."""
        try:
            # Load current config
            config = await self.config_service.load_config()
            
            # Print configuration
            print("Current configuration:")
            print(f"  Download directory: {config.download_dir}")
            print(f"  Maximum concurrent downloads: {config.max_concurrent_downloads}")
            print(f"  Default format: {config.default_format.name}")
            
        except Exception as e:
            print(f"Error showing configuration: {e}")
            
    def _create_safe_filename(self, title: str) -> str:
        """Create a safe filename from a video title.
        
        Args:
            title: Video title
            
        Returns:
            Safe filename
        """
        # Remove invalid characters
        safe_title = re.sub(r'[\\/*?:"<>|]', '', title)
        
        # Replace spaces with underscores
        safe_title = safe_title.replace(' ', '_')
        
        # Limit length
        if len(safe_title) > 100:
            safe_title = safe_title[:100]
            
        return safe_title


def main():
    """Main entry point."""
    # Create argument parser
    parser = argparse.ArgumentParser(description="YouTube Downloader")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Download video command
    download_parser = subparsers.add_parser("download", help="Download a video")
    download_parser.add_argument("url", help="YouTube video URL")
    download_parser.add_argument("--quality", choices=[q.name for q in VideoQuality], help="Video quality")
    
    # Download playlist command
    playlist_parser = subparsers.add_parser("playlist", help="Download a playlist")
    playlist_parser.add_argument("url", help="YouTube playlist URL")
    playlist_parser.add_argument("--quality", choices=[q.name for q in VideoQuality], help="Video quality")
    
    # List downloads command
    list_parser = subparsers.add_parser("list", help="List all downloads")
    
    # Config command
    config_parser = subparsers.add_parser("config", help="Manage configuration")
    config_parser.add_argument("--show", action="store_true", help="Show current configuration")
    config_parser.add_argument("--download-dir", help="Set download directory")
    config_parser.add_argument("--max-concurrent", type=int, help="Set maximum concurrent downloads")
    config_parser.add_argument("--default-format", choices=[q.name for q in VideoQuality], help="Set default format")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Create and run application
    async def run_app():
        app = YouTubeDownloaderApp()
        await app.initialize()
        
        if args.command == "download":
            # Convert quality string to enum if provided
            quality = VideoQuality[args.quality] if args.quality else None
            await app.download_video(args.url, quality)
        elif args.command == "playlist":
            # Convert quality string to enum if provided
            quality = VideoQuality[args.quality] if args.quality else None
            await app.download_playlist(args.url, quality)
        elif args.command == "list":
            await app.list_downloads()
        elif args.command == "config":
            if args.show:
                await app.show_config()
            else:
                await app.update_config(
                    download_dir=args.download_dir,
                    max_concurrent_downloads=args.max_concurrent,
                    default_format=args.default_format
                )
        else:
            parser.print_help()
    
    # Run the async application
    asyncio.run(run_app())


if __name__ == "__main__":
    main()