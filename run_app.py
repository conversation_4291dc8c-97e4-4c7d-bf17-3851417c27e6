#!/usr/bin/env python3
"""
YouTube Downloader V2 Launcher

This script serves as the entry point for the YouTube Downloader V2 application.
It checks dependencies, sets up logging, and launches the main application.
"""

import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(Path.home() / '.youtube_downloader_v2' / 'app.log')
    ]
)

logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if all required dependencies are installed.
    
    Returns:
        bool: True if all dependencies are installed, False otherwise
    """
    required_packages = ['PyQt6', 'qtawesome', 'qdarkstyle']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("The following required packages are missing:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install them using pip:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def main():
    """Main entry point for the application."""
    print("YouTube Downloader V2")
    print("=" * 40)
    
    # Create log directory if it doesn't exist
    log_dir = Path.home() / '.youtube_downloader_v2'
    log_dir.mkdir(exist_ok=True)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # Import and run the application
        from src.ui.main_app import main as run_app
        
        logger.info("Starting YouTube Downloader V2")
        run_app()
    except Exception as e:
        logger.exception("An error occurred while running the application")
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()