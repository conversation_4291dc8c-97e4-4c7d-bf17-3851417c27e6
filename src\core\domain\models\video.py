from enum import Enum
from typing import Dict, List, Optional, Any, Set, Union
from pydantic import BaseModel, Field, validator, root_validator
from datetime import datetime, timedelta
from pathlib import Path
import re

from .base import BaseEntity, SizeInfo, Status, WindowsPathMixin

class VideoQuality(str, Enum):
    """Video quality options"""
    AUDIO_ONLY = "audio_only"
    LOW_144P = "144p"
    LOW_240P = "240p"
    MEDIUM_360P = "360p"
    MEDIUM_480P = "480p"
    HIGH_720P = "720p"
    HIGH_1080P = "1080p"
    ULTRA_1440P = "1440p"
    ULTRA_2160P = "2160p"
    ULTRA_4320P = "4320p"

class VideoCodec(str, Enum):
    """Video codec options"""
    H264 = "h264"
    H265 = "h265"
    VP9 = "vp9"
    AV1 = "av1"
    WEBM = "webm"

class AudioCodec(str, Enum):
    """Audio codec options"""
    MP3 = "mp3"
    AAC = "aac"
    OPUS = "opus"
    VORBIS = "vorbis"
    FLAC = "flac"

class VideoContainer(str, Enum):
    """Video container options"""
    MP4 = "mp4"
    MKV = "mkv"
    WEBM = "webm"
    AVI = "avi"
    FLV = "flv"

class VideoFormat(BaseModel):
    """Represents a video format option"""
    format_id: str = Field(description="Format ID from YouTube")
    quality: VideoQuality = Field(description="Video quality")
    container: VideoContainer = Field(description="Container format")
    video_codec: Optional[VideoCodec] = Field(default=None, description="Video codec")
    audio_codec: Optional[AudioCodec] = Field(default=None, description="Audio codec")
    is_audio_only: bool = Field(default=False, description="Whether this is an audio-only format")
    file_size: Optional[SizeInfo] = Field(default=None, description="Estimated file size")
    bitrate: Optional[int] = Field(default=None, description="Bitrate in kbps")
    fps: Optional[int] = Field(default=None, description="Frames per second")
    width: Optional[int] = Field(default=None, description="Video width")
    height: Optional[int] = Field(default=None, description="Video height")
    
    @property
    def resolution(self) -> Optional[str]:
        """Get the resolution as a string"""
        if self.width and self.height:
            return f"{self.width}x{self.height}"
        return None
    
    @property
    def display_name(self) -> str:
        """Get a user-friendly display name"""
        if self.is_audio_only:
            # Convert audio codec enum value to string value for display
            codec_str = self.audio_codec.value if isinstance(self.audio_codec, AudioCodec) else str(self.audio_codec or 'unknown')
            return f"Audio only ({codec_str}, {self.bitrate or '?'} kbps)"
        else:
            # Convert enum values to their string values for display
            quality_str = self.quality.value if isinstance(self.quality, VideoQuality) else str(self.quality)
            container_str = self.container.value if isinstance(self.container, VideoContainer) else str(self.container)
            codec_str = self.video_codec.value if isinstance(self.video_codec, VideoCodec) else str(self.video_codec or 'unknown')
            return f"{quality_str} ({container_str}, {codec_str})"

class VideoInfo(BaseEntity, WindowsPathMixin):
    """Represents information about a YouTube video"""
    video_id: str = Field(description="YouTube video ID")
    url: str = Field(description="Full YouTube URL")
    title: str = Field(description="Video title")
    description: Optional[str] = Field(default=None, description="Video description")
    channel_id: Optional[str] = Field(default=None, description="Channel ID")
    channel_name: Optional[str] = Field(default=None, description="Channel name")
    duration: Optional[int] = Field(default=None, description="Duration in seconds")
    view_count: Optional[int] = Field(default=None, description="View count")
    upload_date: Optional[datetime] = Field(default=None, description="Upload date")
    thumbnail_url: Optional[str] = Field(default=None, description="Thumbnail URL")
    available_formats: List[VideoFormat] = Field(default_factory=list, description="Available formats")
    tags: List[str] = Field(default_factory=list, description="Video tags")
    categories: List[str] = Field(default_factory=list, description="Video categories")
    is_live: bool = Field(default=False, description="Whether this is a live stream")
    
    @validator('video_id')
    def validate_video_id(cls, v):
        """Validate YouTube video ID format"""
        if not re.match(r'^[A-Za-z0-9_-]{11}$', v):
            raise ValueError("Invalid YouTube video ID format")
        return v
    
    @validator('url')
    def validate_url(cls, v):
        """Validate YouTube URL format"""
        if not re.match(r'^https?://(www\.)?(youtube\.com|youtu\.be)/.+$', v):
            raise ValueError("Invalid YouTube URL format")
        return v
    
    @property
    def duration_formatted(self) -> str:
        """Get formatted duration (HH:MM:SS)"""
        if not self.duration:
            return "Unknown"
        
        td = timedelta(seconds=self.duration)
        hours, remainder = divmod(td.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def get_safe_filename(self) -> str:
        """Get a safe filename based on the video title"""
        if not self.title:
            return f"video_{self.video_id}"
        
        # Sanitize the title
        safe_title = self.sanitize_windows_filename(self.title)
        
        # Add video ID to ensure uniqueness
        return f"{safe_title}_{self.video_id}"
    
    def get_best_format(self, quality: Optional[VideoQuality] = None, 
                       container: Optional[VideoContainer] = None,
                       audio_only: bool = False) -> Optional[VideoFormat]:
        """Get the best format matching the criteria"""
        if not self.available_formats:
            return None
        
        # Filter formats
        matching_formats = self.available_formats
        
        if audio_only:
            matching_formats = [f for f in matching_formats if f.is_audio_only]
        elif quality:
            matching_formats = [f for f in matching_formats if f.quality == quality]
        
        if container:
            matching_formats = [f for f in matching_formats if f.container == container]
        
        if not matching_formats:
            return None
        
        # Sort by quality and bitrate
        if audio_only:
            return sorted(matching_formats, key=lambda f: f.bitrate or 0, reverse=True)[0]
        else:
            # Sort by resolution and then by bitrate
            return sorted(matching_formats, 
                          key=lambda f: ((f.height or 0) * (f.width or 0), f.bitrate or 0), 
                          reverse=True)[0]