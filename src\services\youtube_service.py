#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
YouTube Service Implementation

This module implements the IYouTubeService interface using yt-dlp library
to interact with YouTube and extract video information.
"""

import asyncio
import datetime
import re
from functools import lru_cache
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import parse_qs, urlparse

import yt_dlp

from src.services.service_interfaces import IYouTubeService, VideoInfo, VideoQuality
from src.utils.validation_framework import ValidationR<PERSON>ult, YouTubeUrlValidator


# Constants
YOUTUBE_URL_PATTERNS = [
    r'^(https?://)?(www\.)?(youtube\.com|youtu\.be)/.+$',
    r'^(https?://)?(www\.)?youtube\.com/watch\?v=[\w-]+(&[\w=]+)*$',
    r'^(https?://)?(www\.)?youtu\.be/[\w-]+$',
    r'^(https?://)?(www\.)?youtube\.com/playlist\?list=[\w-]+(&[\w=]+)*$',
]

# Cache settings
CACHE_SIZE = 100
CACHE_EXPIRY = 3600  # seconds


class YouTubeServiceException(Exception):
    """Base exception for YouTube service errors."""
    pass


class VideoExtractionError(YouTubeServiceException):
    """Raised when video information extraction fails."""
    pass


class PlaylistExtractionError(YouTubeServiceException):
    """Raised when playlist information extraction fails."""
    pass


class InvalidURLError(YouTubeServiceException):
    """Raised when URL is not a valid YouTube URL."""
    pass


class NetworkError(YouTubeServiceException):
    """Raised when network-related errors occur."""
    pass


class YouTubeService(IYouTubeService):
    """Implementation of the YouTube service interface using yt-dlp."""

    def __init__(self):
        """Initialize YouTube service with yt-dlp options."""
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'skip_download': True,
            'extract_flat': 'in_playlist',
            'ignoreerrors': True,
        }
        self.url_validator = YouTubeUrlValidator()
        self._video_info_cache = {}
        self._cache_timestamps = {}

    async def extract_video_info(self, url: str) -> VideoInfo:
        """Extract video information from YouTube URL.
        
        Args:
            url: YouTube video URL
            
        Returns:
            VideoInfo object with extracted information
            
        Raises:
            InvalidURLError: If URL is not a valid YouTube URL
            VideoExtractionError: If video information extraction fails
            NetworkError: If network-related errors occur
        """
        # Validate URL first
        validation_result = self.url_validator.validate(url)
        if not validation_result.is_valid:
            raise InvalidURLError(f"Invalid YouTube URL: {validation_result.message}")

        # Check cache first
        video_id = self._extract_video_id(url)
        if video_id in self._video_info_cache:
            # Check if cache is still valid
            timestamp = self._cache_timestamps.get(video_id)
            if timestamp and (datetime.datetime.now() - timestamp).total_seconds() < CACHE_EXPIRY:
                return self._video_info_cache[video_id]

        try:
            # Run yt-dlp in a separate thread to avoid blocking the event loop
            info = await self._run_yt_dlp(url)
            
            # Extract relevant information
            video_info = self._parse_video_info(info)
            
            # Cache the result
            self._video_info_cache[video_id] = video_info
            self._cache_timestamps[video_id] = datetime.datetime.now()
            
            # Clean cache if it's too large
            if len(self._video_info_cache) > CACHE_SIZE:
                self._clean_cache()
                
            return video_info
            
        except yt_dlp.utils.DownloadError as e:
            if "Video unavailable" in str(e):
                raise VideoExtractionError(f"Video unavailable: {e}")
            elif any(net_err in str(e).lower() for net_err in ["network", "connection", "timeout"]):
                raise NetworkError(f"Network error: {e}")
            else:
                raise VideoExtractionError(f"Failed to extract video info: {e}")
        except Exception as e:
            raise VideoExtractionError(f"Unexpected error: {e}")

    async def get_available_formats(self, video_id: str) -> List[VideoQuality]:
        """Get available download formats for a video.
        
        Args:
            video_id: YouTube video ID
            
        Returns:
            List of available VideoQuality options
            
        Raises:
            VideoExtractionError: If format information extraction fails
        """
        url = f"https://www.youtube.com/watch?v={video_id}"
        
        try:
            # Use specific options to get format information
            ydl_opts = self.ydl_opts.copy()
            ydl_opts['listformats'] = True
            
            info = await self._run_yt_dlp(url, ydl_opts)
            
            # Map formats to VideoQuality enum
            return self._map_formats_to_quality(info)
            
        except Exception as e:
            raise VideoExtractionError(f"Failed to get available formats: {e}")

    async def validate_url(self, url: str) -> bool:
        """Validate if URL is a valid YouTube URL.
        
        Args:
            url: URL to validate
            
        Returns:
            True if URL is valid, False otherwise
        """
        validation_result = self.url_validator.validate(url)
        return validation_result.is_valid

    async def extract_playlist_info(self, url: str) -> List[VideoInfo]:
        """Extract playlist information.
        
        Args:
            url: YouTube playlist URL
            
        Returns:
            List of VideoInfo objects for each video in the playlist
            
        Raises:
            InvalidURLError: If URL is not a valid YouTube playlist URL
            PlaylistExtractionError: If playlist information extraction fails
        """
        # Validate URL first
        if not await self._is_playlist_url(url):
            raise InvalidURLError(f"Not a valid YouTube playlist URL: {url}")

        try:
            # Use specific options for playlist extraction
            ydl_opts = self.ydl_opts.copy()
            ydl_opts['extract_flat'] = True
            
            info = await self._run_yt_dlp(url, ydl_opts)
            
            # Extract playlist entries
            if 'entries' not in info:
                raise PlaylistExtractionError("No entries found in playlist")
                
            videos = []
            for entry in info['entries']:
                if entry is None:  # Skip private or deleted videos
                    continue
                    
                # For playlist entries, we might need to fetch full info for each video
                # But for performance, we'll create a basic VideoInfo from the entry data
                video_info = self._parse_playlist_entry(entry)
                videos.append(video_info)
                
            return videos
            
        except yt_dlp.utils.DownloadError as e:
            if "This playlist does not exist" in str(e):
                raise PlaylistExtractionError(f"Playlist does not exist: {e}")
            elif any(net_err in str(e).lower() for net_err in ["network", "connection", "timeout"]):
                raise NetworkError(f"Network error: {e}")
            else:
                raise PlaylistExtractionError(f"Failed to extract playlist info: {e}")
        except Exception as e:
            raise PlaylistExtractionError(f"Unexpected error: {e}")

    # Helper methods
    
    async def _run_yt_dlp(self, url: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run yt-dlp in a separate thread to avoid blocking the event loop."""
        opts = options or self.ydl_opts
        
        def _extract_info():
            with yt_dlp.YoutubeDL(opts) as ydl:
                return ydl.extract_info(url, download=False)
        
        # Run in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _extract_info)

    def _parse_video_info(self, info: Dict[str, Any]) -> VideoInfo:
        """Parse yt-dlp info dict into VideoInfo object."""
        # Extract upload date if available
        upload_date = None
        if 'upload_date' in info and info['upload_date']:
            try:
                # Format is usually YYYYMMDD
                date_str = info['upload_date']
                upload_date = datetime.date(
                    int(date_str[0:4]),
                    int(date_str[4:6]),
                    int(date_str[6:8])
                )
            except (ValueError, IndexError):
                pass
        
        # Extract duration, defaulting to 0 if not available
        duration = int(info.get('duration', 0))
        
        # Create VideoInfo object
        return VideoInfo(
            video_id=info.get('id', ''),
            title=info.get('title', 'Unknown Title'),
            description=info.get('description', ''),
            duration=duration,
            thumbnail_url=info.get('thumbnail', ''),
            author=info.get('uploader', 'Unknown'),
            view_count=info.get('view_count'),
            upload_date=upload_date,
            available_formats=self._map_formats_to_quality(info)
        )

    def _parse_playlist_entry(self, entry: Dict[str, Any]) -> VideoInfo:
        """Parse playlist entry into VideoInfo object."""
        # Playlist entries have less information, so we create a basic VideoInfo
        return VideoInfo(
            video_id=entry.get('id', ''),
            title=entry.get('title', 'Unknown Title'),
            description='',  # Usually not available in playlist entries
            duration=int(entry.get('duration', 0)),
            thumbnail_url=entry.get('thumbnail', ''),
            author=entry.get('uploader', 'Unknown'),
            view_count=None,  # Usually not available in playlist entries
            upload_date=None,  # Usually not available in playlist entries
            available_formats=None  # Need to fetch full info to get formats
        )

    def _map_formats_to_quality(self, info: Dict[str, Any]) -> List[VideoQuality]:
        """Map yt-dlp formats to VideoQuality enum."""
        formats = info.get('formats', [])
        qualities = set()
        
        for fmt in formats:
            height = fmt.get('height', 0)
            
            if height >= 1080:
                qualities.add(VideoQuality.VERY_HIGH)
            elif height >= 720:
                qualities.add(VideoQuality.HIGH)
            elif height >= 480:
                qualities.add(VideoQuality.MEDIUM)
            elif height > 0:
                qualities.add(VideoQuality.LOW)
        
        # If no formats with height info, add at least LOW quality
        if not qualities:
            qualities.add(VideoQuality.LOW)
            
        return list(qualities)

    def _extract_video_id(self, url: str) -> str:
        """Extract video ID from YouTube URL."""
        parsed_url = urlparse(url)
        
        if parsed_url.netloc in ('youtu.be', 'www.youtu.be'):
            return parsed_url.path.lstrip('/')
            
        if parsed_url.netloc in ('youtube.com', 'www.youtube.com'):
            if parsed_url.path == '/watch':
                query = parse_qs(parsed_url.query)
                return query.get('v', [''])[0]
                
        # If we can't extract ID from URL, use the whole URL as ID for caching
        return url

    async def _is_playlist_url(self, url: str) -> bool:
        """Check if URL is a YouTube playlist URL."""
        parsed_url = urlparse(url)
        
        if parsed_url.netloc in ('youtube.com', 'www.youtube.com'):
            query = parse_qs(parsed_url.query)
            return 'list' in query
            
        return False

    def _clean_cache(self):
        """Clean oldest entries from cache when it gets too large."""
        # Sort by timestamp and remove oldest entries
        if not self._cache_timestamps:
            return
            
        sorted_items = sorted(
            self._cache_timestamps.items(),
            key=lambda x: x[1]
        )
        
        # Remove oldest 20% of entries
        num_to_remove = max(1, len(sorted_items) // 5)
        for i in range(num_to_remove):
            video_id, _ = sorted_items[i]
            if video_id in self._video_info_cache:
                del self._video_info_cache[video_id]
            if video_id in self._cache_timestamps:
                del self._cache_timestamps[video_id]