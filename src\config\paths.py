#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Path Manager

This module provides a singleton path manager for cross-platform path operations.
It handles path sanitization, directory creation, and temporary file management.
"""

import os
import shutil
import tempfile
from pathlib import Path
from typing import Optional, List, Dict, Union
import platform


class PathManager:
    """Singleton path manager for cross-platform path operations"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PathManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._system = platform.system()
        self._temp_files: List[Path] = []
        self._initialized = True
    
    def is_windows(self) -> bool:
        """Check if running on Windows"""
        return self._system == "Windows"
    
    def is_macos(self) -> bool:
        """Check if running on macOS"""
        return self._system == "Darwin"
    
    def is_linux(self) -> bool:
        """Check if running on Linux"""
        return self._system == "Linux"
    
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for cross-platform compatibility"""
        # Remove invalid characters
        invalid_chars = '<>:"\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove trailing dots and spaces
        filename = filename.rstrip('. ')
        
        # Handle Windows reserved names
        if self.is_windows():
            reserved_names = {
                'CON', 'PRN', 'AUX', 'NUL',
                'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
                'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
            }
            
            name_without_ext = filename.rsplit('.', 1)[0] if '.' in filename else filename
            if name_without_ext.upper() in reserved_names:
                filename = f"_{filename}"
        
        # Limit length (Windows has 260 character path limit)
        if len(filename) > 200:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:200-len(ext)-1] + ('.' + ext if ext else '')
        
        return filename
    
    def ensure_directory(self, path: Union[str, Path]) -> Path:
        """Ensure directory exists and return Path object"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def is_path_writable(self, path: Union[str, Path]) -> bool:
        """Check if path is writable"""
        path = Path(path)
        
        if path.exists():
            # Check if existing path is writable
            return os.access(path, os.W_OK)
        else:
            # Check if parent directory is writable
            return os.access(path.parent, os.W_OK)
    
    def get_free_space(self, path: Union[str, Path]) -> int:
        """Get free space in bytes for the given path"""
        path = Path(path)
        
        if not path.exists():
            path = path.parent
        
        if not path.exists():
            return 0
        
        try:
            if self.is_windows():
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(str(path)), None, None, ctypes.pointer(free_bytes))
                return free_bytes.value
            else:
                stats = os.statvfs(path)
                return stats.f_frsize * stats.f_bavail
        except Exception:
            return 0
    
    def create_temp_file(self, suffix: Optional[str] = None, prefix: Optional[str] = None, 
                        dir: Optional[Union[str, Path]] = None) -> Path:
        """Create a temporary file and track it for cleanup"""
        if dir is not None:
            dir = Path(dir)
            dir.mkdir(parents=True, exist_ok=True)
        
        temp_file = Path(tempfile.mktemp(suffix=suffix, prefix=prefix, dir=dir))
        self._temp_files.append(temp_file)
        return temp_file
    
    def cleanup_temp_files(self):
        """Clean up all tracked temporary files"""
        for temp_file in self._temp_files:
            try:
                if temp_file.exists():
                    if temp_file.is_file():
                        temp_file.unlink()
                    elif temp_file.is_dir():
                        shutil.rmtree(temp_file)
            except Exception:
                pass
        
        self._temp_files = []


# Singleton instance
path_manager = PathManager()