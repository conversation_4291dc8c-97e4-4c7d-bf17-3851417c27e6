#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Phase 3 Validation Script for YouTube Downloader V2

This script validates that the service layer implementation meets the quality gates
and requirements defined in the validation framework.
"""

import argparse
import asyncio
import importlib.util
import inspect
import json
import os
import re
import sys
from dataclasses import dataclass
from enum import Enum, auto
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union

# Add colors for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


class ValidationLevel(Enum):
    INFO = auto()
    WARNING = auto()
    ERROR = auto()
    CRITICAL = auto()


@dataclass
class ValidationResult:
    level: ValidationLevel
    message: str
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    code_snippet: Optional[str] = None
    suggestion: Optional[str] = None

    def __str__(self) -> str:
        prefix = {
            ValidationLevel.INFO: f"{Colors.OKBLUE}[INFO]{Colors.ENDC}",
            ValidationLevel.WARNING: f"{Colors.WARNING}[WARNING]{Colors.ENDC}",
            ValidationLevel.ERROR: f"{Colors.FAIL}[ERROR]{Colors.ENDC}",
            ValidationLevel.CRITICAL: f"{Colors.FAIL}{Colors.BOLD}[CRITICAL]{Colors.ENDC}",
        }[self.level]
        
        location = ""
        if self.file_path:
            location = f"{self.file_path}"
            if self.line_number is not None:
                location += f":{self.line_number}"
            location = f" ({location})"
        
        result = f"{prefix}{location}: {self.message}"
        
        if self.code_snippet:
            result += f"\n{Colors.BOLD}Code:{Colors.ENDC}\n{self.code_snippet}"
        
        if self.suggestion:
            result += f"\n{Colors.BOLD}Suggestion:{Colors.ENDC} {self.suggestion}"
        
        return result


class ValidationReport:
    def __init__(self):
        self.results: List[ValidationResult] = []
    
    def add_result(self, result: ValidationResult) -> None:
        self.results.append(result)
    
    def has_critical_issues(self) -> bool:
        return any(r.level == ValidationLevel.CRITICAL for r in self.results)
    
    def has_errors(self) -> bool:
        return any(r.level in (ValidationLevel.ERROR, ValidationLevel.CRITICAL) for r in self.results)
    
    def count_by_level(self) -> Dict[ValidationLevel, int]:
        counts = {level: 0 for level in ValidationLevel}
        for result in self.results:
            counts[result.level] += 1
        return counts
    
    def __str__(self) -> str:
        if not self.results:
            return f"{Colors.OKGREEN}No validation issues found.{Colors.ENDC}"
        
        counts = self.count_by_level()
        summary = [f"{Colors.BOLD}Validation Report Summary:{Colors.ENDC}"]
        for level, count in counts.items():
            if count > 0:
                color = {
                    ValidationLevel.INFO: Colors.OKBLUE,
                    ValidationLevel.WARNING: Colors.WARNING,
                    ValidationLevel.ERROR: Colors.FAIL,
                    ValidationLevel.CRITICAL: f"{Colors.FAIL}{Colors.BOLD}",
                }[level]
                summary.append(f"  {color}{level.name}:{Colors.ENDC} {count}")
        
        details = [f"\n{Colors.BOLD}Validation Details:{Colors.ENDC}"] + [str(r) for r in self.results]
        
        return "\n".join(summary + details)


class ServiceValidator:
    """Validates the service layer implementation."""
    
    def __init__(self, src_dir: Path):
        self.src_dir = src_dir
        self.report = ValidationReport()
    
    async def validate_all(self) -> ValidationReport:
        """Run all validation checks and return the report."""
        # Check for required files
        self._validate_required_files()
        
        # Check interfaces
        self._validate_interfaces()
        
        # Check implementations
        self._validate_implementations()
        
        # Check validation framework
        self._validate_validation_framework()
        
        # Check error handling
        self._validate_error_handling()
        
        # Check async patterns
        self._validate_async_patterns()
        
        # Check documentation
        self._validate_documentation()
        
        return self.report
    
    def _validate_required_files(self) -> None:
        """Check that all required files exist."""
        required_files = [
            "service_interfaces.py",
            "youtube_service.py",
            "download_service.py",
            "file_service.py",
            "config_service.py",
            "logging_service.py",
            "service_factory.py"
        ]
        
        for file_name in required_files:
            file_path = self.src_dir / file_name
            if not file_path.exists():
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.CRITICAL,
                    message=f"Required file {file_name} is missing",
                    file_path=str(file_path),
                    suggestion=f"Create {file_name} according to the implementation plan"
                ))
    
    def _validate_interfaces(self) -> None:
        """Validate the service interfaces."""
        interface_file = self.src_dir / "service_interfaces.py"
        if not interface_file.exists():
            return  # Already reported in _validate_required_files
        
        with open(interface_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for required interfaces
        required_interfaces = [
            "IYouTubeService",
            "IDownloadService",
            "IFileService",
            "IConfigService",
            "ILoggingService",
            "IServiceFactory"
        ]
        
        for interface_name in required_interfaces:
            if not re.search(rf"class\s+{interface_name}\s*\(", content):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.CRITICAL,
                    message=f"Required interface {interface_name} is missing",
                    file_path=str(interface_file),
                    suggestion=f"Define {interface_name} interface with appropriate methods"
                ))
        
        # Check for abstract methods in interfaces
        for interface_name in required_interfaces:
            if re.search(rf"class\s+{interface_name}\s*\(", content):
                # Find the class definition
                class_match = re.search(rf"class\s+{interface_name}\s*\(.*?\):\s*(?P<body>.*?)(?=\n\n|$)", content, re.DOTALL)
                if class_match:
                    class_body = class_match.group("body")
                    if not re.search(r"@abstractmethod", class_body):
                        self.report.add_result(ValidationResult(
                            level=ValidationLevel.ERROR,
                            message=f"Interface {interface_name} has no abstract methods",
                            file_path=str(interface_file),
                            suggestion=f"Add @abstractmethod methods to {interface_name}"
                        ))
    
    def _validate_implementations(self) -> None:
        """Validate the service implementations."""
        implementations = [
            ("youtube_service.py", "YouTubeService", "IYouTubeService"),
            ("download_service.py", "DownloadService", "IDownloadService"),
            ("file_service.py", "FileService", "IFileService"),
            ("config_service.py", "ConfigService", "IConfigService"),
            ("logging_service.py", "LoggingService", "ILoggingService"),
            ("service_factory.py", "ServiceFactory", "IServiceFactory")
        ]
        
        for file_name, class_name, interface_name in implementations:
            file_path = self.src_dir / file_name
            if not file_path.exists():
                continue  # Already reported in _validate_required_files
            
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Check if class exists
            if not re.search(rf"class\s+{class_name}\s*\(", content):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.CRITICAL,
                    message=f"Required class {class_name} is missing in {file_name}",
                    file_path=str(file_path),
                    suggestion=f"Implement {class_name} class that inherits from {interface_name}"
                ))
                continue
            
            # Check if class inherits from interface
            if not re.search(rf"class\s+{class_name}\s*\(.*?{interface_name}.*?\):", content):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Class {class_name} does not inherit from {interface_name}",
                    file_path=str(file_path),
                    suggestion=f"Make {class_name} inherit from {interface_name}"
                ))
    
    def _validate_validation_framework(self) -> None:
        """Validate the validation framework implementation."""
        validation_file = self.src_dir / "../utils/validation_framework.py"
        if not validation_file.exists():
            self.report.add_result(ValidationResult(
                level=ValidationLevel.CRITICAL,
                message="Validation framework is missing",
                file_path=str(validation_file),
                suggestion="Create validation_framework.py according to the implementation plan"
            ))
            return
        
        with open(validation_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for required validation components
        required_components = [
            "ValidationSeverity",
            "ValidationIssue",
            "ValidationResult",
            "Validator",
            "YouTubeUrlValidator",
            "FilePathValidator",
            "DownloadConfigValidator"
        ]
        
        for component_name in required_components:
            if not re.search(rf"class\s+{component_name}\s*[\(:]|@dataclass\s+{component_name}", content):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.ERROR,
                    message=f"Required validation component {component_name} is missing",
                    file_path=str(validation_file),
                    suggestion=f"Implement {component_name} class in the validation framework"
                ))
    
    def _validate_error_handling(self) -> None:
        """Validate error handling in the service implementations."""
        service_files = [
            "youtube_service.py",
            "download_service.py",
            "file_service.py",
            "config_service.py"
        ]
        
        for file_name in service_files:
            file_path = self.src_dir / file_name
            if not file_path.exists():
                continue  # Already reported in _validate_required_files
            
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Check for custom exceptions
            if not re.search(r"class\s+\w+(?:Error|Exception)\s*\(", content):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"No custom exceptions defined in {file_name}",
                    file_path=str(file_path),
                    suggestion="Define custom exceptions for error handling"
                ))
            
            # Check for try-except blocks
            if not re.search(r"try\s*:.*?except\s+(?:\w+(?:Error|Exception)(?:\s+as\s+\w+)?|Exception\s+as\s+\w+):\s*", content, re.DOTALL):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"No proper try-except blocks found in {file_name}",
                    file_path=str(file_path),
                    suggestion="Implement proper error handling with try-except blocks"
                ))
    
    def _validate_async_patterns(self) -> None:
        """Validate async patterns in the service implementations."""
        service_files = [
            "youtube_service.py",
            "download_service.py",
            "file_service.py",
            "config_service.py"
        ]
        
        for file_name in service_files:
            file_path = self.src_dir / file_name
            if not file_path.exists():
                continue  # Already reported in _validate_required_files
            
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Check for async methods
            if not re.search(r"async\s+def\s+\w+\s*\(", content):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.INFO,
                    message=f"No async methods found in {file_name}",
                    file_path=str(file_path),
                    suggestion="Consider using async methods for I/O-bound operations"
                ))
            
            # Check for proper async/await usage
            if re.search(r"async\s+def\s+\w+\s*\(", content) and not re.search(r"await\s+\w+", content):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"Async methods without await found in {file_name}",
                    file_path=str(file_path),
                    suggestion="Use await with async calls inside async methods"
                ))
    
    def _validate_documentation(self) -> None:
        """Validate documentation in the service implementations."""
        all_files = [
            "service_interfaces.py",
            "youtube_service.py",
            "download_service.py",
            "file_service.py",
            "config_service.py",
            "service_factory.py"
        ]
        
        for file_name in all_files:
            file_path = self.src_dir / file_name
            if not file_path.exists():
                continue  # Already reported in _validate_required_files
            
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Check for module docstring
            if not re.search(r'(?:^|\n)\s*""".*?"""', content, re.DOTALL):
                self.report.add_result(ValidationResult(
                    level=ValidationLevel.WARNING,
                    message=f"No module docstring found in {file_name}",
                    file_path=str(file_path),
                    suggestion="Add a comprehensive module docstring"
                ))
            
            # Check for class docstrings
            class_matches = re.finditer(r"class\s+(\w+)\s*\(.*?\):\s*(?:\"\"\".*?\"\"\"|[^\n]*$)", content, re.DOTALL)
            for match in class_matches:
                class_name = match.group(1)
                class_def = match.group(0)
                if not re.search(r'\"\"\".*?\"\"\"', class_def, re.DOTALL):
                    self.report.add_result(ValidationResult(
                        level=ValidationLevel.WARNING,
                        message=f"No docstring for class {class_name} in {file_name}",
                        file_path=str(file_path),
                        suggestion=f"Add a docstring for class {class_name}"
                    ))
            
            # Check for method docstrings
            method_matches = re.finditer(r"def\s+(\w+)\s*\(.*?\)\s*->\s*.*?:\s*(?:\"\"\".*?\"\"\"|[^\n]*$)", content, re.DOTALL)
            for match in method_matches:
                method_name = match.group(1)
                method_def = match.group(0)
                if not re.search(r'\"\"\".*?\"\"\"', method_def, re.DOTALL):
                    self.report.add_result(ValidationResult(
                        level=ValidationLevel.INFO,
                        message=f"No docstring for method {method_name} in {file_name}",
                        file_path=str(file_path),
                        suggestion=f"Add a docstring for method {method_name}"
                    ))


async def main():
    parser = argparse.ArgumentParser(description="Validate Phase 3 implementation")
    parser.add_argument(
        "--src-dir", 
        type=Path, 
        default=Path("app_V3_optPlanning/src/services"),
        help="Directory containing the service layer implementation files"
    )
    parser.add_argument(
        "--output", 
        type=str, 
        default="phase3_validation_results.txt",
        help="Output file for validation results"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Print verbose output"
    )
    args = parser.parse_args()
    
    # Ensure src_dir exists
    if not args.src_dir.exists():
        print(f"{Colors.FAIL}Error: Directory {args.src_dir} does not exist{Colors.ENDC}")
        return 1
    
    # Run validation
    validator = ServiceValidator(args.src_dir)
    report = await validator.validate_all()
    
    # Print report
    if args.verbose or report.has_errors():
        print(report)
    
    # Write report to file
    with open(args.output, "w", encoding="utf-8") as f:
        f.write(str(report))
    
    # Return exit code
    if report.has_critical_issues():
        print(f"{Colors.FAIL}Validation failed with critical issues. See {args.output} for details.{Colors.ENDC}")
        return 2
    elif report.has_errors():
        print(f"{Colors.WARNING}Validation completed with errors. See {args.output} for details.{Colors.ENDC}")
        return 1
    else:
        print(f"{Colors.OKGREEN}Validation completed successfully!{Colors.ENDC}")
        return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)