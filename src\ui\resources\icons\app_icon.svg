<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="512"
   height="512"
   viewBox="0 0 512 512"
   version="1.1"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF0000;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CC0000;stop-opacity:1" />
    </linearGradient>
  </defs>
  <!-- Background Circle -->
  <circle
     cx="256"
     cy="256"
     r="240"
     fill="url(#gradient)"
     stroke="#FFFFFF"
     stroke-width="8" />
  <!-- YouTube Play Button -->
  <path
     d="M 204,160 L 204,352 L 352,256 L 204,160 Z"
     fill="#FFFFFF" />
  <!-- Download Arrow -->
  <g transform="translate(256, 400) scale(0.8)">
    <path
       d="M -40,-60 L 40,-60 L 40,-20 L 60,0 L 0,60 L -60,0 L -40,-20 Z"
       fill="#FFFFFF" />
  </g>
</svg>