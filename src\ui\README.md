# YouTube Downloader V2 - UI Implementation

This directory contains the user interface implementation for the YouTube Downloader V2 application. The UI is built using PyQt6 (or PySide6 as a fallback) and follows a modular architecture for maintainability and extensibility.

## Directory Structure

```
src/ui/
├── __init__.py             # Package initialization
├── main_app.py             # Application entry point
├── components/             # Reusable UI components
│   ├── __init__.py
│   ├── video_card.py       # Video information display component
│   └── progress_bar.py     # Download progress component
├── widgets/                # Main application widgets
│   ├── __init__.py
│   ├── search_widget.py    # YouTube search functionality
│   ├── download_widget.py  # Download management
│   └── library_widget.py   # Downloaded videos library
├── windows/                # Application windows
│   ├── __init__.py
│   ├── main_window.py      # Main application window
│   ├── about_window.py     # About dialog
│   └── settings_window.py  # Settings dialog
└── utils/                  # UI utilities
    ├── __init__.py
    └── ui_validation.py    # Input validation for UI
```

## Setup and Dependencies

The UI implementation requires the following dependencies:

- **PyQt6** (recommended) or **PySide6** as the UI framework
- **qtawesome** for icons
- **qdarkstyle** for dark theme support

Install the dependencies using pip:

```bash
pip install -r requirements-ui.txt
```

## Usage

To run the application, execute the `main_app.py` script:

```bash
python -m src.ui.main_app
```

Or from the project root:

```bash
python -m app_V3_optPlanning.src.ui.main_app
```

## UI Architecture

The UI follows a modular architecture with clear separation of concerns:

1. **Main Application** (`main_app.py`): Initializes the QApplication, sets up application metadata, and handles theme selection.

2. **Main Window** (`windows/main_window.py`): Provides the main application window with a tabbed layout, menu bar, toolbar, and status bar.

3. **Widgets** (`widgets/`): Implements the main functionality of the application:
   - **Search Widget**: YouTube video search and results display
   - **Download Widget**: Download management and progress tracking
   - **Library Widget**: Management of downloaded videos

4. **Components** (`components/`): Reusable UI components used across the application:
   - **Video Card**: Displays video information with thumbnail and metadata
   - **Progress Bar**: Shows download progress with speed information

5. **Utils** (`utils/`): Utility functions for UI operations:
   - **UI Validation**: Input validation for search queries, download options, etc.

## Service Integration

The UI components interact with the service layer through the `ServiceFactory` class, which provides access to:

- **YouTube Service**: For searching and retrieving video information
- **Download Service**: For downloading videos with progress tracking
- **File Service**: For managing downloaded files and playlists

## Theming

The application supports three themes:

- **Light**: Default light theme
- **Dark**: Dark theme using qdarkstyle
- **System**: Follows the system theme preference

Theme selection is managed through the application settings.

## Customization

The UI can be customized by modifying the following:

- **Icons**: Replace or modify icons in the `resources/icons` directory
- **Styles**: Customize styles in the `resources/styles` directory
- **Settings**: Adjust default settings in the `AppConfig` class

## Building and Deployment

To build the application for distribution, use the `build_installer.py` script in the project root:

```bash
python build_installer.py
```

This will create an executable and installer for the application.