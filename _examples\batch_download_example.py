#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Batch Download Example of YouTube Downloader

This script demonstrates how to batch download multiple YouTube videos
from a text file using the YouTube Downloader services.
"""

import asyncio
import os
import re
from pathlib import Path
from typing import Dict, List, Set, Optional

from src.services.service_interfaces import VideoQuality, DownloadStatus, DownloadTask
from src.services.service_factory import AsyncServiceFactory


async def main():
    """Example of batch downloading YouTube videos from a text file."""
    # Create service factory
    factory = AsyncServiceFactory()
    
    # Create services
    youtube_service = await factory.create_youtube_service_async()
    download_service = await factory.create_download_service_async()
    file_service = await factory.create_file_service_async()
    config_service = await factory.create_config_service_async()
    
    # Load configuration
    config = await config_service.load_config()
    print(f"Download directory: {config.download_dir}")
    
    # Ensure download directory exists
    await file_service.create_directory(config.download_dir)
    
    # Create batch download directory
    batch_dir = Path(config.download_dir) / "batch_download"
    await file_service.create_directory(batch_dir)
    
    # Create example URLs file if it doesn't exist
    urls_file = batch_dir / "urls.txt"
    if not urls_file.exists():
        create_example_urls_file(urls_file)
        print(f"Created example URLs file at {urls_file}")
    
    # Read URLs from file
    print(f"Reading URLs from {urls_file}...")
    urls = read_urls_from_file(urls_file)
    
    if not urls:
        print("No URLs found in the file.")
        return
    
    print(f"Found {len(urls)} URLs.")
    
    # Process each URL
    task_ids = []
    failed_urls = []
    
    for url in urls:
        # Validate URL
        validation_result = await youtube_service.validate_url(url)
        if not validation_result.is_valid:
            print(f"Invalid URL: {url} - {validation_result.message}")
            failed_urls.append((url, validation_result.message))
            continue
        
        try:
            # Extract video info
            print(f"Extracting info for {url}...")
            video_info = await youtube_service.extract_video_info(url)
            
            if not video_info:
                print(f"Failed to extract info for {url}")
                failed_urls.append((url, "Failed to extract video info"))
                continue
            
            # Create safe filename
            filename = f"{create_safe_filename(video_info.title)}.mp4"
            destination = batch_dir / filename
            
            # Create download task
            task = DownloadTask(
                url=url,
                destination=str(destination),
                quality=VideoQuality.MEDIUM,  # You can change this to any quality
                video_info=video_info
            )
            
            # Start download
            task_id = await download_service.start_download(task)
            task_ids.append(task_id)
            print(f"Started download for '{video_info.title}' with task ID: {task_id}")
            
            # Wait a bit before starting next download to avoid rate limiting
            await asyncio.sleep(0.5)
            
        except Exception as e:
            print(f"Error processing {url}: {e}")
            failed_urls.append((url, str(e)))
    
    if task_ids:
        # Monitor progress for all tasks
        await monitor_multiple_download_progress(download_service, task_ids)
    
    # Report failed URLs
    if failed_urls:
        print("\nFailed URLs:")
        for url, reason in failed_urls:
            print(f"  {url} - {reason}")
    
    print("\nBatch download complete.")


def create_example_urls_file(file_path: Path):
    """Create an example URLs file.
    
    Args:
        file_path: Path to the file to create
    """
    # Example URLs (short educational videos)
    example_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Astley - Never Gonna Give You Up
        "https://www.youtube.com/watch?v=jNQXAC9IVRw",  # Me at the zoo (first YouTube video)
        "https://www.youtube.com/watch?v=TcMBFSGVi1c"   # Avengers: Endgame Trailer
    ]
    
    # Write URLs to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write("# YouTube URLs to download (one per line)\n")
        f.write("# Lines starting with # are ignored\n\n")
        for url in example_urls:
            f.write(f"{url}\n")


def read_urls_from_file(file_path: Path) -> List[str]:
    """Read URLs from a text file.
    
    Args:
        file_path: Path to the file to read
        
    Returns:
        List of URLs
    """
    urls = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # Skip empty lines and comments
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # Add URL to list
                urls.append(line)
    except Exception as e:
        print(f"Error reading URLs file: {e}")
    
    return urls


def create_safe_filename(title: str) -> str:
    """Create a safe filename from a title.
    
    Args:
        title: The title to convert to a safe filename
        
    Returns:
        A safe filename
    """
    # Remove invalid characters
    safe_title = re.sub(r'[\\/*?:"<>|]', '', title)
    
    # Replace spaces with underscores
    safe_title = safe_title.replace(' ', '_')
    
    # Limit length
    if len(safe_title) > 100:
        safe_title = safe_title[:100]
        
    return safe_title


async def monitor_multiple_download_progress(download_service, task_ids: List[str]):
    """Monitor download progress for multiple tasks.
    
    Args:
        download_service: Download service instance
        task_ids: List of download task IDs
    """
    try:
        # Set to track completed tasks
        completed_tasks: Set[str] = set()
        
        # Monitor progress until all tasks are complete, failed, or canceled
        while len(completed_tasks) < len(task_ids):
            # Clear screen
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Print header
            print(f"Monitoring {len(task_ids)} downloads:")
            print("-" * 80)
            
            # Get all tasks
            all_tasks = await download_service.get_all_tasks()
            
            # Create a dictionary of tasks by ID for easy lookup
            tasks_dict: Dict[str, DownloadTask] = {task.task_id: task for task in all_tasks if task.task_id in task_ids}
            
            # Print progress for each task
            for task_id in task_ids:
                if task_id not in tasks_dict:
                    print(f"Task {task_id}: Not found")
                    completed_tasks.add(task_id)
                    continue
                    
                task = tasks_dict[task_id]
                progress = task.progress
                
                if not progress:
                    print(f"Task {task_id}: No progress information")
                    continue
                    
                # Get video title
                title = task.video_info.title if task.video_info else "Unknown"
                
                # Print progress
                status_text = progress.status.name
                if progress.status == DownloadStatus.DOWNLOADING:
                    # Calculate speed in MB/s
                    speed_mb = progress.speed / (1024 * 1024) if progress.speed else 0
                    
                    # Format ETA
                    eta_text = "Unknown"
                    if progress.eta is not None:
                        eta_min = progress.eta // 60
                        eta_sec = progress.eta % 60
                        eta_text = f"{eta_min}m {eta_sec}s" if eta_min > 0 else f"{eta_sec}s"
                    
                    # Print progress bar
                    bar_length = 30
                    filled_length = int(bar_length * progress.progress_percentage / 100)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    
                    print(f"[{bar}] {progress.progress_percentage:.1f}% - {speed_mb:.2f} MB/s - ETA: {eta_text}")
                    print(f"Title: {title}")
                else:
                    # Print status for non-downloading states
                    print(f"Status: {status_text}")
                    print(f"Title: {title}")
                    
                print("-" * 80)
                
                # Check if download is complete, failed, or canceled
                if progress.status in [DownloadStatus.COMPLETED, DownloadStatus.FAILED, DownloadStatus.CANCELED]:
                    completed_tasks.add(task_id)
                    
            # Print summary
            print(f"Completed: {len(completed_tasks)}/{len(task_ids)}")
            
            # Wait before checking again
            await asyncio.sleep(1)
            
        print("All downloads finished.")
            
    except KeyboardInterrupt:
        # Cancel all downloads if user presses Ctrl+C
        print("\nCanceling all downloads...")
        for task_id in task_ids:
            try:
                await download_service.cancel_download(task_id)
            except Exception:
                pass
        print("All downloads canceled.")
        
    except Exception as e:
        print(f"Error monitoring downloads: {e}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())