#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for logging utilities.
"""

import unittest
import logging
import os
import tempfile
from pathlib import Path

from src.config.base import LogLevel
from src.utils.logging_utils import setup_logging, get_logger, LogCapture


class TestLoggingUtils(unittest.TestCase):
    """Test logging utilities."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for log files
        self.temp_dir = Path(tempfile.mkdtemp())
        self.log_file = self.temp_dir / "test.log"
        
        # Reset the root logger
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:  # Make a copy of the list
            root_logger.removeHandler(handler)
        root_logger.setLevel(logging.INFO)
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove temporary files
        if self.log_file.exists():
            try:
                os.remove(self.log_file)
            except (Per<PERSON><PERSON><PERSON><PERSON>, OSE<PERSON>r):
                pass  # Ignore if file is locked
        
        # Remove temporary directory
        try:
            os.rmdir(self.temp_dir)
        except (<PERSON><PERSON><PERSON><PERSON><PERSON>, OSError):
            pass  # Ignore if directory is not empty or locked
        
        # Reset the root logger
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:  # Make a copy of the list
            root_logger.removeHandler(handler)
        root_logger.setLevel(logging.INFO)
    
    def test_setup_logging_file_only(self):
        """Test setting up logging with file handler only."""
        # Set up logging with file handler only
        setup_logging(
            log_level=LogLevel.INFO,
            log_file=self.log_file,
            console_output=False
        )
        
        # Get the root logger
        root_logger = logging.getLogger()
        
        # Check log level
        self.assertEqual(root_logger.level, logging.INFO)
        
        # Check handlers
        self.assertEqual(len(root_logger.handlers), 1)
        self.assertIsInstance(root_logger.handlers[0], logging.FileHandler)
        
        # Log a message
        test_message = "Test file logging"
        root_logger.info(test_message)
        
        # Check that the message was written to the log file
        with open(self.log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        self.assertIn(test_message, log_content)
    
    def test_setup_logging_console_only(self):
        """Test setting up logging with console handler only."""
        # Set up logging with console handler only
        setup_logging(
            log_level=LogLevel.DEBUG,
            log_file=None,
            console_output=True
        )
        
        # Get the root logger
        root_logger = logging.getLogger()
        
        # Check log level
        self.assertEqual(root_logger.level, logging.DEBUG)
        
        # Check handlers
        self.assertEqual(len(root_logger.handlers), 1)
        self.assertIsInstance(root_logger.handlers[0], logging.StreamHandler)
    
    def test_setup_logging_both_handlers(self):
        """Test setting up logging with both file and console handlers."""
        # Set up logging with both handlers
        setup_logging(
            log_level=LogLevel.WARNING,
            log_file=self.log_file,
            console_output=True
        )
        
        # Get the root logger
        root_logger = logging.getLogger()
        
        # Check log level
        self.assertEqual(root_logger.level, logging.WARNING)
        
        # Check handlers
        self.assertEqual(len(root_logger.handlers), 2)
        handler_types = [type(h) for h in root_logger.handlers]
        self.assertIn(logging.FileHandler, handler_types)
        self.assertIn(logging.StreamHandler, handler_types)
    
    def test_get_logger(self):
        """Test getting a named logger."""
        # Set up logging
        setup_logging(
            log_level=LogLevel.INFO,
            log_file=self.log_file,
            console_output=False
        )
        
        # Get a named logger
        logger_name = "test_logger"
        logger = get_logger(logger_name)
        
        # Check logger properties
        self.assertEqual(logger.name, logger_name)
        self.assertEqual(logger.level, logging.INFO)
        
        # Log a message
        test_message = "Test named logger"
        logger.info(test_message)
        
        # Check that the message was written to the log file
        with open(self.log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        self.assertIn(test_message, log_content)
        self.assertIn(logger_name, log_content)
    
    def test_log_levels(self):
        """Test different log levels."""
        # Set up logging with DEBUG level
        setup_logging(
            log_level=LogLevel.DEBUG,
            log_file=self.log_file,
            console_output=False
        )
        
        # Get a logger
        logger = get_logger("level_test")
        
        # Log messages at different levels
        debug_msg = "DEBUG message"
        info_msg = "INFO message"
        warning_msg = "WARNING message"
        error_msg = "ERROR message"
        critical_msg = "CRITICAL message"
        
        logger.debug(debug_msg)
        logger.info(info_msg)
        logger.warning(warning_msg)
        logger.error(error_msg)
        logger.critical(critical_msg)
        
        # Check that all messages were logged
        with open(self.log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        self.assertIn(debug_msg, log_content)
        self.assertIn(info_msg, log_content)
        self.assertIn(warning_msg, log_content)
        self.assertIn(error_msg, log_content)
        self.assertIn(critical_msg, log_content)
        
        # Set up logging with WARNING level
        self.tearDown()  # Clean up previous setup
        self.setUp()     # Set up new environment
        
        setup_logging(
            log_level=LogLevel.WARNING,
            log_file=self.log_file,
            console_output=False
        )
        
        # Get a logger
        logger = get_logger("level_test")
        
        # Log messages at different levels
        logger.debug(debug_msg)
        logger.info(info_msg)
        logger.warning(warning_msg)
        logger.error(error_msg)
        logger.critical(critical_msg)
        
        # Check that only WARNING and above messages were logged
        with open(self.log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        self.assertNotIn(debug_msg, log_content)
        self.assertNotIn(info_msg, log_content)
        self.assertIn(warning_msg, log_content)
        self.assertIn(error_msg, log_content)
        self.assertIn(critical_msg, log_content)
    
    def test_log_capture(self):
        """Test LogCapture context manager."""
        # Set up logging
        setup_logging(
            log_level=LogLevel.INFO,
            log_file=None,  # No file logging
            console_output=True
        )
        
        # Get a logger
        logger = get_logger("capture_test")
        
        # Use LogCapture to capture log messages
        with LogCapture() as log_capture:
            # Log some messages
            test_message1 = "Test message 1"
            test_message2 = "Test message 2"
            logger.info(test_message1)
            logger.error(test_message2)
            
            # Check captured messages
            captured_records = log_capture.records
            self.assertEqual(len(captured_records), 2)
            
            self.assertEqual(captured_records[0].getMessage(), test_message1)
            self.assertEqual(captured_records[0].levelno, logging.INFO)
            
            self.assertEqual(captured_records[1].getMessage(), test_message2)
            self.assertEqual(captured_records[1].levelno, logging.ERROR)
            
            # Check formatted output
            formatted_logs = log_capture.get_logs()
            self.assertIn(test_message1, formatted_logs)
            self.assertIn(test_message2, formatted_logs)


if __name__ == "__main__":
    unittest.main()