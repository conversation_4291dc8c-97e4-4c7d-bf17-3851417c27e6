#!/usr/bin/env python3
"""
YouTube Downloader Application

This module initializes the main application and sets up the UI environment.
"""

import os
import sys
from pathlib import Path

try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtGui import QIcon
    from PyQt6.QtCore import QSettings
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QIcon
        from PySide6.QtCore import QSettings
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required. Please install with 'pip install PyQt6' or 'pip install PySide6'")
        sys.exit(1)

# Import style manager
from src.ui.utils.style_manager import StyleManager

# Add parent directory to path to allow importing from other packages
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.config.app_config import AppConfig
from src.ui.windows.main_window import MainWindow


class YouTubeDownloaderApp:
    """Main application class for YouTube Downloader."""
    
    APP_NAME = "YouTube Downloader V2"
    APP_VERSION = "2.0.0"
    ORGANIZATION_NAME = "YouTubeDownloaderV2"
    
    def __init__(self):
        """Initialize the application."""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName(self.APP_NAME)
        self.app.setApplicationVersion(self.APP_VERSION)
        self.app.setOrganizationName(self.ORGANIZATION_NAME)
        
        # Load configuration
        self.config = AppConfig()
        
        # Set application icon
        self._set_app_icon()
        
        # Set application style
        self._set_app_style()
        
        # Create main window
        self.main_window = MainWindow(self.config, self.app)
    
    def _set_app_icon(self):
        """Set the application icon."""
        # Try to find the SVG icon first (preferred)
        icon_path = os.path.join(os.path.dirname(__file__), 'resources', 'icons', 'app_icon.svg')
        if not os.path.exists(icon_path):
            # Fall back to PNG if SVG is not available
            icon_path = os.path.join(os.path.dirname(__file__), 'resources', 'icons', 'app_icon.png')
            
        if os.path.exists(icon_path):
            self.app.setWindowIcon(QIcon(icon_path))
        else:
            print(f"Warning: Application icon not found at {icon_path}")
    
    def _set_app_style(self):
        """Set the application style based on user preferences."""
        # Get theme from settings
        theme = self.config.get_setting("app/theme", StyleManager.THEME_SYSTEM)
        
        # Apply the theme using StyleManager
        StyleManager.apply_theme(self.app, theme)
        
        # Store the current theme in the config
        self.config.set_setting("app/theme", theme)
    
    def run(self):
        """Run the application."""
        self.main_window.show()
        return self.app.exec()


def main():
    """Main entry point for the application."""
    app = YouTubeDownloaderApp()
    sys.exit(app.run())


if __name__ == "__main__":
    main()