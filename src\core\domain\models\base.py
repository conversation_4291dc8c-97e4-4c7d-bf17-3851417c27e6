from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
import uuid
from pathlib import Path

class Priority(str, Enum):
    """Task priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class Status(str, Enum):
    """General status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class SizeInfo(BaseModel):
    """Represents file size information"""
    bytes: int = Field(ge=0, description="Size in bytes")
    
    @property
    def kb(self) -> float:
        return self.bytes / 1024
    
    @property
    def mb(self) -> float:
        return self.bytes / (1024 * 1024)
    
    @property
    def gb(self) -> float:
        return self.bytes / (1024 * 1024 * 1024)
    
    def format_size(self) -> str:
        """Format size in human-readable format"""
        if self.gb >= 1:
            return f"{self.gb:.2f} GB"
        elif self.mb >= 1:
            return f"{self.mb:.2f} MB"
        elif self.kb >= 1:
            return f"{self.kb:.2f} KB"
        else:
            return f"{self.bytes} bytes"

class ErrorInfo(BaseModel):
    """Represents error information"""
    code: str = Field(description="Error code")
    message: str = Field(description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="When the error occurred")

class Metadata(BaseModel):
    """Base metadata for all entities"""
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    version: int = Field(default=1, ge=1)
    tags: List[str] = Field(default_factory=list)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)

class BaseEntity(BaseModel):
    """Base class for all domain entities"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    metadata: Metadata = Field(default_factory=Metadata)
    
    class Config:
        validate_assignment = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Path: lambda v: str(v)
        }
    
    def update_timestamp(self):
        """Update the last modified timestamp"""
        self.metadata.updated_at = datetime.now()
        self.metadata.version += 1
    
    def add_tag(self, tag: str):
        """Add a tag to the entity"""
        if tag not in self.metadata.tags:
            self.metadata.tags.append(tag)
            self.update_timestamp()
    
    def remove_tag(self, tag: str):
        """Remove a tag from the entity"""
        if tag in self.metadata.tags:
            self.metadata.tags.remove(tag)
            self.update_timestamp()

class WindowsPathMixin:
    """Mixin for Windows-specific path operations"""
    
    @staticmethod
    def sanitize_windows_filename(filename: str) -> str:
        """Sanitize filename for Windows compatibility"""
        # Remove invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove trailing dots and spaces
        filename = filename.rstrip('. ')
        
        # Handle reserved names
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        
        name_without_ext = filename.rsplit('.', 1)[0] if '.' in filename else filename
        if name_without_ext.upper() in reserved_names:
            filename = f"_{filename}"
        
        # Limit length (Windows has 260 character path limit)
        if len(filename) > 200:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:200-len(ext)-1] + ('.' + ext if ext else '')
        
        return filename
    
    @staticmethod
    def validate_windows_path(path: Path) -> bool:
        """Validate if path is valid for Windows"""
        try:
            # Check path length
            if len(str(path)) > 260:
                return False
            
            # Check for invalid characters in path components
            invalid_chars = '<>:"|?*'
            for part in path.parts:
                if any(char in part for char in invalid_chars):
                    return False
            
            return True
        except Exception:
            return False