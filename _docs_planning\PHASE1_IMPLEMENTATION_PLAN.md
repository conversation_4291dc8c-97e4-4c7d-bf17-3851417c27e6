# Phase 1 Implementation Plan: Foundation and Configuration

## Overview
Phase 1 focuses on establishing the foundation for the YouTube Downloader V3 application with an emphasis on a robust configuration system. This phase will set up the project structure, implement core configuration components, and establish the groundwork for subsequent phases.

## Goals
- Create a well-structured project foundation
- Implement a type-safe configuration system using Pydantic
- Establish cross-platform path management
- Create environment-specific configuration profiles
- Implement configuration persistence and validation
- Set up logging infrastructure

## File Structure to Create

```
src/
├── __init__.py
├── config/
│   ├── __init__.py
│   ├── base.py
│   ├── settings.py
│   └── paths.py
├── utils/
│   ├── __init__.py
│   └── logging_utils.py
tests/
├── __init__.py
└── test_config/
    ├── __init__.py
    ├── test_base.py
    ├── test_settings.py
    └── test_paths.py
```

## Enhanced Implementation Workflow

### 1. Workflow Overview

```mermaid
graph TD
    A[Define Requirements] --> B[Create Project Structure]
    B --> C[Implement Configuration Models]
    C --> D[Implement Settings Manager]
    D --> E[Implement Path Manager]
    E --> F[Create Tests]
    F --> G[Validate Implementation]
    
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style B fill:#eeeeee,stroke:#333,stroke-width:2px
    style C fill:#d5f9e5,stroke:#333,stroke-width:2px
    style D fill:#e5d5f9,stroke:#333,stroke-width:2px
    style E fill:#f9e5d5,stroke:#333,stroke-width:2px
    style F fill:#d5e5f9,stroke:#333,stroke-width:2px
    style G fill:#f5f5f5,stroke:#333,stroke-width:2px
```

### 2. Quality Gates

#### Entry Criteria for Phase 1
- Project requirements defined
- Development environment configured
- Required dependencies available

#### Exit Criteria for Phase 1
- All configuration models implemented and tested
- Settings manager functioning correctly
- Path manager handling cross-platform paths
- Unit test coverage > 90%
- Documentation complete
- Code quality checks passing

## Detailed Implementation Plan

### 1. Configuration Models (`src/config/base.py`)

#### 1.1 Core Enumerations
```python
from enum import Enum, auto
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from pathlib import Path

class LogLevel(str, Enum):
    """Log level options"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class DownloadQuality(str, Enum):
    """Video quality options"""
    AUDIO_ONLY = "audio_only"
    LOW = "360p"
    MEDIUM = "720p"
    HIGH = "1080p"
    ULTRA = "2160p"
    BEST = "best"

class VideoFormat(str, Enum):
    """Video format options"""
    MP4 = "mp4"
    MKV = "mkv"
    WEBM = "webm"

class AudioFormat(str, Enum):
    """Audio format options"""
    MP3 = "mp3"
    AAC = "aac"
    OGG = "ogg"
    M4A = "m4a"
```

#### 1.2 Configuration Models
```python
class AppConfig(BaseModel):
    """Application-level settings"""
    name: str = "YouTube Downloader V3"
    version: str = "3.0.0"
    log_level: LogLevel = LogLevel.INFO
    max_concurrent_downloads: int = Field(default=3, ge=1, le=10)
    auto_update_check: bool = True
    user_agent: Optional[str] = None

class PathConfig(BaseModel):
    """Directory and file paths"""
    downloads_dir: Path = Field(default=Path.home() / "Downloads" / "YouTubeDownloader")
    config_dir: Path = Field(default=Path.home() / ".youtube_downloader")
    temp_dir: Path = Field(default=Path.home() / ".youtube_downloader" / "temp")
    log_dir: Path = Field(default=Path.home() / ".youtube_downloader" / "logs")
    
    @validator("*")
    def validate_paths(cls, v):
        """Ensure paths are absolute"""
        if not isinstance(v, Path):
            v = Path(v)
        return v.absolute()

class DownloadConfig(BaseModel):
    """Download-related settings"""
    default_quality: DownloadQuality = DownloadQuality.HIGH
    default_video_format: VideoFormat = VideoFormat.MP4
    default_audio_format: AudioFormat = AudioFormat.MP3
    create_thumbnail: bool = True
    create_info_json: bool = False
    max_retries: int = Field(default=3, ge=0, le=10)
    timeout: int = Field(default=30, ge=5, le=300)
    rate_limit: Optional[str] = None  # e.g., "1M"

class BaseConfiguration(BaseModel):
    """Main configuration class"""
    app: AppConfig = Field(default_factory=AppConfig)
    paths: PathConfig = Field(default_factory=PathConfig)
    downloads: DownloadConfig = Field(default_factory=DownloadConfig)
    
    class Config:
        validate_assignment = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Path: lambda v: str(v)
        }
```

#### 1.3 Factory Functions
```python
def create_development_config() -> BaseConfiguration:
    """Create a configuration for development environment"""
    config = BaseConfiguration()
    config.app.log_level = LogLevel.DEBUG
    config.app.max_concurrent_downloads = 1
    config.downloads.create_info_json = True
    return config

def create_production_config() -> BaseConfiguration:
    """Create a configuration for production environment"""
    config = BaseConfiguration()
    config.app.log_level = LogLevel.INFO
    config.app.max_concurrent_downloads = 3
    return config

def create_testing_config() -> BaseConfiguration:
    """Create a configuration for testing environment"""
    config = BaseConfiguration()
    config.app.log_level = LogLevel.DEBUG
    config.paths.downloads_dir = Path("./test_downloads")
    config.paths.temp_dir = Path("./test_temp")
    config.paths.log_dir = Path("./test_logs")
    return config
```

### 2. Settings Manager (`src/config/settings.py`)

```python
import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, List, Callable, Union
from .base import BaseConfiguration, create_production_config

class ConfigurationChangedEvent:
    """Event for configuration changes"""
    def __init__(self, key: str, old_value: Any, new_value: Any):
        self.key = key
        self.old_value = old_value
        self.new_value = new_value

class SettingsManager:
    """Singleton settings manager"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SettingsManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._config = create_production_config()
        self._config_file = None
        self._change_callbacks: List[Callable[[ConfigurationChangedEvent], None]] = []
        self._initialized = True
    
    def initialize(self, config_file: Optional[Union[str, Path]] = None):
        """Initialize settings manager with optional config file"""
        if config_file:
            self._config_file = Path(config_file)
            if self._config_file.exists():
                self.load_from_file(self._config_file)
            else:
                # Create directories if they don't exist
                self._config_file.parent.mkdir(parents=True, exist_ok=True)
                self.save_to_file(self._config_file)
        
        # Create necessary directories
        self._ensure_directories_exist()
    
    def _ensure_directories_exist(self):
        """Ensure all configured directories exist"""
        for path_attr in ["downloads_dir", "config_dir", "temp_dir", "log_dir"]:
            path = getattr(self._config.paths, path_attr)
            path.mkdir(parents=True, exist_ok=True)
    
    def get_config(self) -> BaseConfiguration:
        """Get the current configuration"""
        return self._config
    
    def set_config(self, config: BaseConfiguration):
        """Set a new configuration"""
        old_config = self._config
        self._config = config
        
        # Notify about changes
        self._notify_changes("config", old_config, config)
        
        # Save if we have a config file
        if self._config_file:
            self.save_to_file(self._config_file)
    
    def get_setting(self, key: str) -> Any:
        """Get a setting using dot notation (e.g., 'app.name')"""
        parts = key.split('.')
        value = self._config
        
        for part in parts:
            if hasattr(value, part):
                value = getattr(value, part)
            else:
                raise KeyError(f"Setting '{key}' not found")
        
        return value
    
    def update_setting(self, key: str, value: Any):
        """Update a setting using dot notation"""
        parts = key.split('.')
        target = self._config
        
        # Navigate to the parent object
        for part in parts[:-1]:
            if hasattr(target, part):
                target = getattr(target, part)
            else:
                raise KeyError(f"Setting '{key}' not found")
        
        # Get the old value
        old_value = getattr(target, parts[-1]) if hasattr(target, parts[-1]) else None
        
        # Set the new value
        setattr(target, parts[-1], value)
        
        # Notify about the change
        self._notify_changes(key, old_value, value)
        
        # Save if we have a config file
        if self._config_file:
            self.save_to_file(self._config_file)
    
    def load_from_file(self, file_path: Union[str, Path]):
        """Load configuration from a JSON file"""
        file_path = Path(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            # Convert to BaseConfiguration
            old_config = self._config
            self._config = BaseConfiguration.parse_obj(config_dict)
            
            # Notify about changes
            self._notify_changes("config", old_config, self._config)
            
            return True
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return False
    
    def save_to_file(self, file_path: Union[str, Path]):
        """Save configuration to a JSON file"""
        file_path = Path(file_path)
        
        try:
            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert to dict and save
            config_dict = self._config.dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"Error saving configuration: {e}")
            return False
    
    def register_change_callback(self, callback: Callable[[ConfigurationChangedEvent], None]):
        """Register a callback for configuration changes"""
        if callback not in self._change_callbacks:
            self._change_callbacks.append(callback)
    
    def unregister_change_callback(self, callback: Callable[[ConfigurationChangedEvent], None]):
        """Unregister a callback for configuration changes"""
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)
    
    def _notify_changes(self, key: str, old_value: Any, new_value: Any):
        """Notify all registered callbacks about a configuration change"""
        event = ConfigurationChangedEvent(key, old_value, new_value)
        
        for callback in self._change_callbacks:
            try:
                callback(event)
            except Exception as e:
                print(f"Error in configuration change callback: {e}")

# Singleton instance
settings_manager = SettingsManager()
```

### 3. Path Manager (`src/config/paths.py`)

```python
import os
import shutil
import tempfile
from pathlib import Path
from typing import Optional, List, Dict, Union
import platform

class PathManager:
    """Singleton path manager for cross-platform path operations"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PathManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._system = platform.system()
        self._temp_files: List[Path] = []
        self._initialized = True
    
    def is_windows(self) -> bool:
        """Check if running on Windows"""
        return self._system == "Windows"
    
    def is_macos(self) -> bool:
        """Check if running on macOS"""
        return self._system == "Darwin"
    
    def is_linux(self) -> bool:
        """Check if running on Linux"""
        return self._system == "Linux"
    
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for cross-platform compatibility"""
        # Remove invalid characters
        invalid_chars = '<>:"\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove trailing dots and spaces
        filename = filename.rstrip('. ')
        
        # Handle Windows reserved names
        if self.is_windows():
            reserved_names = {
                'CON', 'PRN', 'AUX', 'NUL',
                'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
                'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
            }
            
            name_without_ext = filename.rsplit('.', 1)[0] if '.' in filename else filename
            if name_without_ext.upper() in reserved_names:
                filename = f"_{filename}"
        
        # Limit length (Windows has 260 character path limit)
        if len(filename) > 200:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:200-len(ext)-1] + ('.' + ext if ext else '')
        
        return filename
    
    def ensure_directory(self, path: Union[str, Path]) -> Path:
        """Ensure directory exists and return Path object"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def is_path_writable(self, path: Union[str, Path]) -> bool:
        """Check if path is writable"""
        path = Path(path)
        
        if path.exists():
            # Check if existing path is writable
            return os.access(path, os.W_OK)
        else:
            # Check if parent directory is writable
            return os.access(path.parent, os.W_OK)
    
    def get_free_space(self, path: Union[str, Path]) -> int:
        """Get free space in bytes for the given path"""
        path = Path(path)
        
        if not path.exists():
            path = path.parent
        
        if not path.exists():
            return 0
        
        try:
            if self.is_windows():
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(str(path)), None, None, ctypes.pointer(free_bytes))
                return free_bytes.value
            else:
                stats = os.statvfs(path)
                return stats.f_frsize * stats.f_bavail
        except Exception:
            return 0
    
    def create_temp_file(self, suffix: Optional[str] = None, prefix: Optional[str] = None, 
                        dir: Optional[Union[str, Path]] = None) -> Path:
        """Create a temporary file and track it for cleanup"""
        if dir is not None:
            dir = Path(dir)
            dir.mkdir(parents=True, exist_ok=True)
        
        temp_file = Path(tempfile.mktemp(suffix=suffix, prefix=prefix, dir=dir))
        self._temp_files.append(temp_file)
        return temp_file
    
    def cleanup_temp_files(self):
        """Clean up all tracked temporary files"""
        for temp_file in self._temp_files:
            try:
                if temp_file.exists():
                    if temp_file.is_file():
                        temp_file.unlink()
                    elif temp_file.is_dir():
                        shutil.rmtree(temp_file)
            except Exception:
                pass
        
        self._temp_files = []

# Singleton instance
path_manager = PathManager()
```

### 4. Module Exports (`src/config/__init__.py`)

```python
from .base import (
    LogLevel, DownloadQuality, VideoFormat, AudioFormat,
    AppConfig, PathConfig, DownloadConfig, BaseConfiguration,
    create_development_config, create_production_config, create_testing_config
)
from .settings import SettingsManager, ConfigurationChangedEvent, settings_manager
from .paths import PathManager, path_manager

__all__ = [
    'LogLevel', 'DownloadQuality', 'VideoFormat', 'AudioFormat',
    'AppConfig', 'PathConfig', 'DownloadConfig', 'BaseConfiguration',
    'create_development_config', 'create_production_config', 'create_testing_config',
    'SettingsManager', 'ConfigurationChangedEvent', 'settings_manager',
    'PathManager', 'path_manager'
]
```

### 5. Logging Utilities (`src/utils/logging_utils.py`)

```python
import logging
import sys
from pathlib import Path
from typing import Optional, Union
from datetime import datetime

from ..config import LogLevel, settings_manager

def setup_logging(log_dir: Optional[Union[str, Path]] = None, 
                 log_level: Optional[Union[str, LogLevel]] = None,
                 log_to_console: bool = True):
    """Set up logging configuration"""
    # Get settings from config if not provided
    if log_dir is None:
        log_dir = settings_manager.get_setting("paths.log_dir")
    
    if log_level is None:
        log_level = settings_manager.get_setting("app.log_level")
    
    # Ensure log directory exists
    log_dir = Path(log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Convert log level string to logging constant
    if isinstance(log_level, str):
        log_level = getattr(logging, log_level.upper())
    
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:  
        logger.removeHandler(handler)
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(levelname)s: %(message)s'
    )
    
    # Create file handler
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"youtube_downloader_{timestamp}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # Create console handler if requested
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """Get a named logger"""
    return logging.getLogger(name)
```

## Testing Strategy

### 1. Unit Tests

#### 1.1 Configuration Models Tests (`tests/test_config/test_base.py`)
- Test enum values
- Test default values
- Test validation rules
- Test factory functions

#### 1.2 Settings Manager Tests (`tests/test_config/test_settings.py`)
- Test singleton behavior
- Test configuration loading and saving
- Test setting updates
- Test change notifications

#### 1.3 Path Manager Tests (`tests/test_config/test_paths.py`)
- Test path sanitization
- Test directory creation
- Test temporary file management
- Test platform-specific behavior

### 2. Integration Tests
- Test configuration persistence across application restarts
- Test path management with real file system
- Test logging with different configurations

## Success Criteria

- [ ] All configuration models implemented
- [ ] Settings manager functioning correctly
- [ ] Path manager handling cross-platform paths
- [ ] Logging utilities implemented
- [ ] Unit test coverage > 90%
- [ ] Documentation complete
- [ ] Code quality checks passing

## Next Phase Preparation

Phase 1 completion will enable:
- Phase 2: Core Domain Models
- Phase 3: Service Layer with Enhanced Validation

## Risk Mitigation

### Technical Risks
- **Path handling issues**: Implement comprehensive path validation and sanitization
- **Configuration persistence**: Add backup and restore functionality
- **Cross-platform compatibility**: Test on multiple platforms

### Performance Risks
- **Configuration loading overhead**: Implement lazy loading
- **Path operations**: Optimize for Windows long paths
- **Logging performance**: Implement asynchronous logging

## Timeline Estimate

- **Week 1**: Project setup and configuration models
- **Week 2**: Settings and path management
- **Week 3**: Testing and documentation

This implementation plan provides a comprehensive roadmap for Phase 1, ensuring a solid foundation for the YouTube Downloader V3 application with robust configuration management and cross-platform compatibility.