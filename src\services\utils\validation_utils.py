#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Validation Utilities

This module provides validation utilities specifically for service layer operations,
including URL validation, path validation, configuration validation, and input sanitization.
"""

import os
import re
import html
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from urllib.parse import urlparse, parse_qs

from src.utils.validation_framework import ValidationResult, ValidationSeverity


class URLValidator:
    """Validator for URLs, especially YouTube URLs."""
    
    # YouTube URL patterns
    YOUTUBE_PATTERNS = [
        r'^(https?://)?(www\.)?(youtube\.com|youtu\.be)/.+$',
        r'^(https?://)?(www\.)?youtube\.com/watch\?v=[\w-]+(&[\w=]+)*$',
        r'^(https?://)?(www\.)?youtu\.be/[\w-]+(\?[\w=&]+)?$',
        r'^(https?://)?(www\.)?youtube\.com/playlist\?list=[\w-]+(&[\w=]+)*$',
        r'^(https?://)?(www\.)?youtube\.com/channel/[\w-]+/?$',
        r'^(https?://)?(www\.)?youtube\.com/c/[\w-]+/?$',
        r'^(https?://)?(www\.)?youtube\.com/@[\w-]+/?$'
    ]
    
    @classmethod
    def validate_url(cls, url: str) -> ValidationResult:
        """Validate a general URL.
        
        Args:
            url: URL to validate
            
        Returns:
            ValidationResult with validation outcome
        """
        result = ValidationResult()
        
        if not url or not url.strip():
            result.add_issue("URL cannot be empty", ValidationSeverity.ERROR)
            return result
        
        url = url.strip()
        
        # Basic URL format check
        try:
            parsed = urlparse(url)
            if not parsed.scheme:
                result.add_issue("URL must include protocol (http:// or https://)", 
                               ValidationSeverity.ERROR)
            elif parsed.scheme not in ['http', 'https']:
                result.add_issue("URL must use HTTP or HTTPS protocol", 
                               ValidationSeverity.WARNING)
            
            if not parsed.netloc:
                result.add_issue("URL must include domain name", ValidationSeverity.ERROR)
                
        except Exception as e:
            result.add_issue(f"Invalid URL format: {str(e)}", ValidationSeverity.ERROR)
        
        return result
    
    @classmethod
    def validate_youtube_url(cls, url: str) -> ValidationResult:
        """Validate a YouTube URL.
        
        Args:
            url: YouTube URL to validate
            
        Returns:
            ValidationResult with validation outcome
        """
        result = cls.validate_url(url)
        if not result.is_valid:
            return result
        
        url = url.strip()
        
        # Check if it matches YouTube patterns
        is_youtube = any(re.match(pattern, url, re.IGNORECASE) 
                        for pattern in cls.YOUTUBE_PATTERNS)
        
        if not is_youtube:
            result.add_issue("URL is not a valid YouTube URL", ValidationSeverity.ERROR)
            return result
        
        # Extract and validate video ID if it's a video URL
        video_id = cls.extract_video_id(url)
        if video_id and not cls.is_valid_video_id(video_id):
            result.add_issue(f"Invalid YouTube video ID: {video_id}", 
                           ValidationSeverity.ERROR)
        
        # Extract and validate playlist ID if it's a playlist URL
        playlist_id = cls.extract_playlist_id(url)
        if playlist_id and not cls.is_valid_playlist_id(playlist_id):
            result.add_issue(f"Invalid YouTube playlist ID: {playlist_id}", 
                           ValidationSeverity.ERROR)
        
        return result
    
    @classmethod
    def extract_video_id(cls, url: str) -> Optional[str]:
        """Extract video ID from YouTube URL.
        
        Args:
            url: YouTube URL
            
        Returns:
            Video ID if found, None otherwise
        """
        # Handle youtu.be URLs
        if 'youtu.be/' in url:
            match = re.search(r'youtu\.be/([a-zA-Z0-9_-]{11})', url)
            return match.group(1) if match else None
        
        # Handle youtube.com URLs
        if 'youtube.com/watch' in url:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            return query_params.get('v', [None])[0]
        
        return None
    
    @classmethod
    def extract_playlist_id(cls, url: str) -> Optional[str]:
        """Extract playlist ID from YouTube URL.
        
        Args:
            url: YouTube URL
            
        Returns:
            Playlist ID if found, None otherwise
        """
        if 'playlist?list=' in url:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            return query_params.get('list', [None])[0]
        
        return None
    
    @classmethod
    def is_valid_video_id(cls, video_id: str) -> bool:
        """Check if a video ID is valid format.
        
        Args:
            video_id: Video ID to check
            
        Returns:
            True if valid format, False otherwise
        """
        return bool(re.match(r'^[a-zA-Z0-9_-]{11}$', video_id))
    
    @classmethod
    def is_valid_playlist_id(cls, playlist_id: str) -> bool:
        """Check if a playlist ID is valid format.
        
        Args:
            playlist_id: Playlist ID to check
            
        Returns:
            True if valid format, False otherwise
        """
        return bool(re.match(r'^[a-zA-Z0-9_-]+$', playlist_id))


class PathValidator:
    """Validator for file and directory paths."""
    
    # Invalid characters for different operating systems
    WINDOWS_INVALID_CHARS = '<>:"|?*'
    WINDOWS_RESERVED_NAMES = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }
    
    @classmethod
    def validate_path(cls, path: Union[str, Path], must_exist: bool = False, 
                     must_be_writable: bool = False, 
                     must_be_directory: bool = False) -> ValidationResult:
        """Validate a file or directory path.
        
        Args:
            path: Path to validate
            must_exist: Whether path must exist
            must_be_writable: Whether path must be writable
            must_be_directory: Whether path must be a directory
            
        Returns:
            ValidationResult with validation outcome
        """
        result = ValidationResult()
        
        if not path:
            result.add_issue("Path cannot be empty", ValidationSeverity.ERROR)
            return result


class ConfigValidator:
    """Validator for configuration data."""

    @classmethod
    def validate_download_config(cls, config: Dict[str, Any]) -> ValidationResult:
        """Validate download configuration.

        Args:
            config: Configuration dictionary to validate

        Returns:
            ValidationResult with validation outcome
        """
        result = ValidationResult()

        # Required fields
        required_fields = ['download_dir', 'default_format', 'max_concurrent_downloads']
        for field in required_fields:
            if field not in config:
                result.add_issue(f"Missing required field: {field}",
                               ValidationSeverity.ERROR)

        # Validate download directory
        if 'download_dir' in config:
            path_result = PathValidator.validate_path(
                config['download_dir'],
                must_be_writable=True,
                must_be_directory=True
            )
            result.merge(path_result)

        # Validate max concurrent downloads
        if 'max_concurrent_downloads' in config:
            max_downloads = config['max_concurrent_downloads']
            if not isinstance(max_downloads, int):
                result.add_issue("max_concurrent_downloads must be an integer",
                               ValidationSeverity.ERROR)
            elif max_downloads < 1:
                result.add_issue("max_concurrent_downloads must be at least 1",
                               ValidationSeverity.ERROR)
            elif max_downloads > 10:
                result.add_issue("High number of concurrent downloads may affect performance",
                               ValidationSeverity.WARNING)

        # Validate default format
        if 'default_format' in config:
            format_value = config['default_format']
            valid_formats = ['best', 'worst', '720p', '1080p', '480p', '360p', '240p']
            if format_value not in valid_formats:
                result.add_issue(f"Invalid default_format: {format_value}. "
                               f"Must be one of {valid_formats}", ValidationSeverity.ERROR)

        return result


class InputSanitizer:
    """Utilities for sanitizing user input."""

    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """Sanitize a filename for cross-platform compatibility.

        Args:
            filename: Original filename

        Returns:
            Sanitized filename
        """
        if not filename:
            return "untitled"

        # Remove HTML entities
        filename = html.unescape(filename)

        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        # Remove control characters
        filename = ''.join(char for char in filename if ord(char) >= 32)

        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')

        # Handle Windows reserved names
        if os.name == 'nt':
            reserved_names = PathValidator.WINDOWS_RESERVED_NAMES
            name_without_ext = filename.split('.')[0].upper()
            if name_without_ext in reserved_names:
                filename = f"_{filename}"

        # Limit length
        if len(filename) > 200:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:200-len(ext)-1] + ('.' + ext if ext else '')

        # Ensure we have a valid filename
        if not filename or filename in ['.', '..']:
            filename = "untitled"

        return filename

    @classmethod
    def sanitize_url(cls, url: str) -> str:
        """Sanitize a URL by removing dangerous characters.

        Args:
            url: Original URL

        Returns:
            Sanitized URL
        """
        if not url:
            return ""

        # Strip whitespace
        url = url.strip()

        # Remove dangerous characters that could be used for injection
        dangerous_chars = ['<', '>', '"', "'", '`']
        for char in dangerous_chars:
            url = url.replace(char, '')

        return url

    @classmethod
    def sanitize_text_input(cls, text: str, max_length: int = 1000) -> str:
        """Sanitize general text input.

        Args:
            text: Original text
            max_length: Maximum allowed length

        Returns:
            Sanitized text
        """
        if not text:
            return ""

        # Remove HTML entities
        text = html.unescape(text)

        # Remove control characters except newlines and tabs
        text = ''.join(char for char in text
                      if ord(char) >= 32 or char in ['\n', '\t'])

        # Limit length
        if len(text) > max_length:
            text = text[:max_length]

        # Strip whitespace
        text = text.strip()

        return text


class ValidationUtils:
    """Utility class that combines all validation functionality."""

    @classmethod
    def validate_youtube_download_request(cls, url: str, download_path: str,
                                        config: Dict[str, Any]) -> ValidationResult:
        """Validate a complete YouTube download request.

        Args:
            url: YouTube URL to download
            download_path: Path where file will be downloaded
            config: Download configuration

        Returns:
            ValidationResult with validation outcome
        """
        result = ValidationResult()

        # Validate URL
        url_result = URLValidator.validate_youtube_url(url)
        result.merge(url_result)

        # Validate download path
        path_result = PathValidator.validate_path(
            download_path,
            must_be_writable=True
        )
        result.merge(path_result)

        # Validate configuration
        config_result = ConfigValidator.validate_download_config(config)
        result.merge(config_result)

        return result

    @classmethod
    def get_safe_filename_from_title(cls, title: str, video_id: str = "") -> str:
        """Generate a safe filename from video title and ID.

        Args:
            title: Video title
            video_id: Video ID for uniqueness

        Returns:
            Safe filename
        """
        if not title:
            title = "untitled"

        # Sanitize the title
        safe_title = InputSanitizer.sanitize_filename(title)

        # Add video ID for uniqueness if provided
        if video_id:
            safe_title = f"{safe_title}_{video_id}"

        return safe_title
        
        path_obj = Path(path) if isinstance(path, str) else path
        path_str = str(path_obj)
        
        # Check for invalid characters on Windows
        if os.name == 'nt':
            for char in cls.WINDOWS_INVALID_CHARS:
                if char in path_str:
                    result.add_issue(f"Path contains invalid character: {char}", 
                                   ValidationSeverity.ERROR)
        
        # Check for reserved names on Windows
        if os.name == 'nt':
            for part in path_obj.parts:
                name_without_ext = part.split('.')[0].upper()
                if name_without_ext in cls.WINDOWS_RESERVED_NAMES:
                    result.add_issue(f"Path contains reserved name: {part}", 
                                   ValidationSeverity.ERROR)
        
        # Check path length (Windows has 260 character limit)
        if os.name == 'nt' and len(path_str) > 260:
            result.add_issue("Path exceeds Windows maximum length (260 characters)", 
                           ValidationSeverity.WARNING)
        
        # Check if path exists if required
        if must_exist and not path_obj.exists():
            result.add_issue(f"Path does not exist: {path_str}", ValidationSeverity.ERROR)
        
        # Check if path is a directory if required
        if must_be_directory and path_obj.exists() and not path_obj.is_dir():
            result.add_issue(f"Path is not a directory: {path_str}", 
                           ValidationSeverity.ERROR)
        
        # Check if path is writable if required
        if must_be_writable:
            if path_obj.exists():
                if not os.access(path_obj, os.W_OK):
                    result.add_issue(f"Path is not writable: {path_str}", 
                                   ValidationSeverity.ERROR)
            else:
                # Check if parent directory is writable
                parent = path_obj.parent
                if parent.exists() and not os.access(parent, os.W_OK):
                    result.add_issue(f"Parent directory is not writable: {parent}", 
                                   ValidationSeverity.ERROR)
        
        return result
