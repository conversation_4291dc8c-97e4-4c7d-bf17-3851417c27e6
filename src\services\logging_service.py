#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Logging Service Implementation

This module implements the ILoggingService interface, providing structured logging
with different output formats and destinations.
"""

import logging
import os
import sys
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, Optional, Union, Any

from src.services.service_interfaces import ILoggingService
from src.utils.logging_service import (
    LogLevel, LogFormat, LogDestination, 
    LoggingServiceException, LoggingConfigError,
    logging_service as utils_logging_service
)


class LoggingService(ILoggingService):
    """Implementation of the logging service interface."""
    
    def __init__(self, app_name: str = "YouTubeDownloader"):
        """Initialize logging service.
        
        Args:
            app_name: Name of the application for logging context
        """
        self._logging_service = utils_logging_service
        self._logging_service.app_name = app_name
        self.initialized = False
    
    async def setup(self, 
                   log_level: str,
                   log_format: str,
                   log_destination: str,
                   log_dir: Optional[Union[str, Path]] = None) -> None:
        """Set up logging configuration.
        
        Args:
            log_level: Logging level
            log_format: Logging format (text or JSON)
            log_destination: Where to send logs (console, file, or both)
            log_dir: Directory for log files (required if destination includes file)
            
        Raises:
            LoggingConfigError: If configuration is invalid
        """
        try:
            # Convert enum values to strings if needed
            if hasattr(log_level, 'value'):
                log_level = log_level.value
                
            if hasattr(log_format, 'value'):
                log_format = log_format.value
                
            if hasattr(log_destination, 'value'):
                log_destination = log_destination.value
                
            await self._logging_service.setup(
                log_level=log_level,
                log_format=log_format,
                log_destination=log_destination,
                log_dir=log_dir
            )
            self.initialized = True
        except Exception as e:
            raise LoggingConfigError(f"Failed to set up logging: {str(e)}") from e
    
    async def shutdown(self) -> None:
        """Shutdown logging system and remove handlers."""
        await self._logging_service.shutdown()
        self.initialized = False
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a named logger.
        
        Args:
            name: Logger name, typically the module name
            
        Returns:
            Logger instance
        """
        if not self.initialized:
            # If not initialized, use a basic configuration
            logging.basicConfig(level=logging.INFO)
        
        return self._logging_service.get_logger(name)
    
    def add_context(self, **context) -> None:
        """Add context to all log records.
        
        Args:
            **context: Key-value pairs to add to log context
        """
        self._logging_service.add_context(**context)


# Example usage
async def example():
    """Example usage of LoggingService."""
    # Create logging service
    logging_service = LoggingService()
    
    # Set up logging
    await logging_service.setup(
        log_level=LogLevel.DEBUG,
        log_format=LogFormat.TEXT,
        log_destination=LogDestination.BOTH,
        log_dir="./logs"
    )
    
    # Get a logger
    logger = logging_service.get_logger("example")
    
    # Add context
    logging_service.add_context(user_id="test_user", session_id="123456")
    
    # Log messages
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    try:
        # Simulate an error
        raise ValueError("Example error")
    except Exception as e:
        logger.exception("An error occurred")
    
    # Shutdown logging
    await logging_service.shutdown()


if __name__ == "__main__":
    import asyncio
    asyncio.run(example())