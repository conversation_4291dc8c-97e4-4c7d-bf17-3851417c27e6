#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Service Validation Result Classes

This module provides specialized validation result classes for service layer operations.
"""

from enum import Enum, auto
from typing import List, Dict, Any, Optional
from datetime import datetime


class ServiceValidationSeverity(Enum):
    """Severity levels for service validation issues."""
    INFO = auto()
    WARNING = auto()
    ERROR = auto()
    CRITICAL = auto()


class ServiceValidationIssue:
    """Represents a service validation issue."""

    def __init__(self, message: str, severity: ServiceValidationSeverity,
                 service_name: Optional[str] = None, operation: Optional[str] = None,
                 field: Optional[str] = None, value: Optional[Any] = None,
                 timestamp: Optional[datetime] = None, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.severity = severity
        self.service_name = service_name
        self.operation = operation
        self.field = field
        self.value = value
        self.timestamp = timestamp or datetime.now()
        self.details = details or {}
    
    def __str__(self) -> str:
        parts = []
        if self.service_name:
            parts.append(f"[{self.service_name}]")
        if self.operation:
            parts.append(f"({self.operation})")
        parts.append(f"{self.severity.name}: {self.message}")
        return " ".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'message': self.message,
            'severity': self.severity.name,
            'service_name': self.service_name,
            'operation': self.operation,
            'field': self.field,
            'value': str(self.value) if self.value is not None else None,
            'timestamp': self.timestamp.isoformat(),
            'details': self.details
        }


class ServiceValidationResult:
    """Result of service validation operations."""

    def __init__(self, is_valid: bool = True, issues: Optional[List[ServiceValidationIssue]] = None,
                 service_name: Optional[str] = None, operation: Optional[str] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        self.is_valid = is_valid
        self.issues = issues or []
        self.service_name = service_name
        self.operation = operation
        self.metadata = metadata or {}
    
    def add_issue(self, message: str, severity: ServiceValidationSeverity = ServiceValidationSeverity.ERROR,
                  field: Optional[str] = None, value: Optional[Any] = None,
                  details: Optional[Dict[str, Any]] = None) -> 'ServiceValidationResult':
        """Add a validation issue.
        
        Args:
            message: Issue description
            severity: Issue severity level
            field: Field name if applicable
            value: Field value if applicable
            details: Additional details
            
        Returns:
            Self for method chaining
        """
        issue = ServiceValidationIssue(
            message=message,
            severity=severity,
            service_name=self.service_name,
            operation=self.operation,
            field=field,
            value=value,
            details=details or {}
        )
        
        self.issues.append(issue)
        
        # Update validity based on severity
        if severity in [ServiceValidationSeverity.ERROR, ServiceValidationSeverity.CRITICAL]:
            self.is_valid = False
        
        return self
    
    def add_info(self, message: str, **kwargs) -> 'ServiceValidationResult':
        """Add an info-level issue."""
        return self.add_issue(message, ServiceValidationSeverity.INFO, **kwargs)
    
    def add_warning(self, message: str, **kwargs) -> 'ServiceValidationResult':
        """Add a warning-level issue."""
        return self.add_issue(message, ServiceValidationSeverity.WARNING, **kwargs)
    
    def add_error(self, message: str, **kwargs) -> 'ServiceValidationResult':
        """Add an error-level issue."""
        return self.add_issue(message, ServiceValidationSeverity.ERROR, **kwargs)
    
    def add_critical(self, message: str, **kwargs) -> 'ServiceValidationResult':
        """Add a critical-level issue."""
        return self.add_issue(message, ServiceValidationSeverity.CRITICAL, **kwargs)
    
    def merge(self, other: 'ServiceValidationResult') -> 'ServiceValidationResult':
        """Merge another validation result into this one.
        
        Args:
            other: Other validation result to merge
            
        Returns:
            Self for method chaining
        """
        self.issues.extend(other.issues)
        if not other.is_valid:
            self.is_valid = False
        
        # Merge metadata
        self.metadata.update(other.metadata)
        
        return self
    
    def has_errors(self) -> bool:
        """Check if result has any errors or critical issues."""
        return any(issue.severity in [ServiceValidationSeverity.ERROR, ServiceValidationSeverity.CRITICAL]
                  for issue in self.issues)
    
    def has_warnings(self) -> bool:
        """Check if result has any warnings."""
        return any(issue.severity == ServiceValidationSeverity.WARNING for issue in self.issues)
    
    def has_critical_issues(self) -> bool:
        """Check if result has any critical issues."""
        return any(issue.severity == ServiceValidationSeverity.CRITICAL for issue in self.issues)
    
    def get_issues_by_severity(self, severity: ServiceValidationSeverity) -> List[ServiceValidationIssue]:
        """Get all issues of a specific severity level.
        
        Args:
            severity: Severity level to filter by
            
        Returns:
            List of issues with the specified severity
        """
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_error_messages(self) -> List[str]:
        """Get all error and critical issue messages.
        
        Returns:
            List of error messages
        """
        return [issue.message for issue in self.issues 
                if issue.severity in [ServiceValidationSeverity.ERROR, ServiceValidationSeverity.CRITICAL]]
    
    def get_warning_messages(self) -> List[str]:
        """Get all warning messages.
        
        Returns:
            List of warning messages
        """
        return [issue.message for issue in self.issues 
                if issue.severity == ServiceValidationSeverity.WARNING]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'is_valid': self.is_valid,
            'service_name': self.service_name,
            'operation': self.operation,
            'issues': [issue.to_dict() for issue in self.issues],
            'metadata': self.metadata,
            'summary': {
                'total_issues': len(self.issues),
                'errors': len(self.get_issues_by_severity(ServiceValidationSeverity.ERROR)),
                'warnings': len(self.get_issues_by_severity(ServiceValidationSeverity.WARNING)),
                'critical': len(self.get_issues_by_severity(ServiceValidationSeverity.CRITICAL)),
                'info': len(self.get_issues_by_severity(ServiceValidationSeverity.INFO))
            }
        }
    
    def __str__(self) -> str:
        """String representation of validation result."""
        if not self.issues:
            return f"Validation passed for {self.service_name or 'service'}"
        
        lines = []
        if self.service_name:
            lines.append(f"Validation result for {self.service_name}:")
        
        for issue in self.issues:
            lines.append(f"  {issue}")
        
        return "\n".join(lines)
    
    def __bool__(self) -> bool:
        """Boolean representation (True if valid)."""
        return self.is_valid
    
    @classmethod
    def success(cls, service_name: Optional[str] = None, 
                operation: Optional[str] = None) -> 'ServiceValidationResult':
        """Create a successful validation result.
        
        Args:
            service_name: Name of the service
            operation: Name of the operation
            
        Returns:
            Successful validation result
        """
        return cls(is_valid=True, service_name=service_name, operation=operation)
    
    @classmethod
    def failure(cls, message: str, service_name: Optional[str] = None,
                operation: Optional[str] = None, 
                severity: ServiceValidationSeverity = ServiceValidationSeverity.ERROR) -> 'ServiceValidationResult':
        """Create a failed validation result with a single issue.
        
        Args:
            message: Error message
            service_name: Name of the service
            operation: Name of the operation
            severity: Issue severity
            
        Returns:
            Failed validation result
        """
        result = cls(is_valid=False, service_name=service_name, operation=operation)
        result.add_issue(message, severity)
        return result
