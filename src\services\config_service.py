#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Config Service Implementation

This module implements the IConfigService interface to handle configuration
management, including loading, saving, and updating configuration settings.
"""

import asyncio
import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

from src.services.service_interfaces import IConfigService, DownloadConfig, VideoQuality
from src.utils.validation_framework import DownloadConfigValidator, ValidationResult


class ConfigServiceException(Exception):
    """Base exception for config service errors."""
    pass


class ConfigLoadException(ConfigServiceException):
    """Raised when configuration cannot be loaded."""
    pass


class ConfigSaveException(ConfigServiceException):
    """Raised when configuration cannot be saved."""
    pass


class ConfigValidationException(ConfigServiceException):
    """Raised when configuration validation fails."""
    pass


class ConfigService(IConfigService):
    """Implementation of the config service interface."""
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """Initialize config service.
        
        Args:
            config_path: Optional path to the configuration file
        """
        self.config_path = Path(config_path) if config_path else self._get_default_config_path()
        self.config: Optional[DownloadConfig] = None
        self.validator = DownloadConfigValidator()
        
    async def load_config(self) -> DownloadConfig:
        """Load configuration from file.
        
        Returns:
            DownloadConfig object
            
        Raises:
            ConfigLoadException: If configuration cannot be loaded
            ConfigValidationException: If configuration is invalid
        """
        try:
            # Check if config file exists
            if not self.config_path.exists():
                # Create default config
                self.config = self._get_default_config()
                await self.save_config(self.config)
                return self.config
                
            # Load config from file
            config_dict = await self._load_config_dict()
            
            # Convert to DownloadConfig object
            config = self._dict_to_download_config(config_dict)
            
            # Merge with defaults for any missing fields
            default_config = self._get_default_config()
            if not hasattr(config, 'download_dir') or not config.download_dir:
                config.download_dir = default_config.download_dir
                
            if not hasattr(config, 'max_concurrent_downloads') or config.max_concurrent_downloads <= 0:
                config.max_concurrent_downloads = default_config.max_concurrent_downloads
                
            if not hasattr(config, 'default_format') or not config.default_format:
                config.default_format = default_config.default_format
                
            # Validate config
            validation_result = self.validator.validate(config)
            if not validation_result.is_valid:
                raise ConfigValidationException(f"Invalid configuration: {validation_result.message}")
                
            self.config = config
            return config
            
        except json.JSONDecodeError as e:
            raise ConfigLoadException(f"Failed to parse configuration file: {str(e)}")
        except Exception as e:
            if isinstance(e, ConfigServiceException):
                raise
            raise ConfigLoadException(f"Failed to load configuration: {str(e)}")
    
    async def save_config(self, config: DownloadConfig) -> bool:
        """Save configuration to file.
        
        Args:
            config: DownloadConfig object to save
            
        Returns:
            True if configuration was saved successfully
            
        Raises:
            ConfigValidationException: If configuration is invalid
            ConfigSaveException: If configuration cannot be saved
        """
        try:
            # Validate config
            validation_result = self.validator.validate(config)
            if not validation_result.is_valid:
                raise ConfigValidationException(f"Invalid configuration: {validation_result.message}")
                
            # Convert to dict
            config_dict = self._download_config_to_dict(config)
            
            # Save to file
            await self._save_config_dict(config_dict)
            
            # Update current config
            self.config = config
            
            return True
            
        except Exception as e:
            if isinstance(e, ConfigServiceException):
                raise
            raise ConfigSaveException(f"Failed to save configuration: {str(e)}")
    
    async def get_setting(self, key: str) -> Any:
        """Get a configuration setting.
        
        Args:
            key: Setting key
            
        Returns:
            Setting value
            
        Raises:
            ConfigLoadException: If configuration cannot be loaded
            KeyError: If setting key does not exist
        """
        # Load config if not loaded
        if not self.config:
            await self.load_config()
            
        # Get setting
        if not hasattr(self.config, key):
            raise KeyError(f"Setting {key} does not exist")
            
        return getattr(self.config, key)
    
    async def update_setting(self, key: str, value: Any) -> bool:
        """Update a configuration setting.
        
        Args:
            key: Setting key
            value: Setting value
            
        Returns:
            True if setting was updated successfully
            
        Raises:
            ConfigLoadException: If configuration cannot be loaded
            ConfigSaveException: If configuration cannot be saved
            KeyError: If setting key does not exist
        """
        # Load config if not loaded
        if not self.config:
            await self.load_config()
            
        # Check if setting exists
        if not hasattr(self.config, key):
            raise KeyError(f"Setting {key} does not exist")
            
        # Update setting
        setattr(self.config, key, value)
        
        # Save config
        return await self.save_config(self.config)
    
    async def reset_to_defaults(self) -> bool:
        """Reset configuration to defaults.
        
        Returns:
            True if configuration was reset successfully
            
        Raises:
            ConfigSaveException: If configuration cannot be saved
        """
        # Get default config
        self.config = self._get_default_config()
        
        # Save config
        return await self.save_config(self.config)
    
    # Helper methods
    
    def _get_default_config_path(self) -> Path:
        """Get default configuration file path.
        
        Returns:
            Path to default configuration file
        """
        # Use user's home directory
        home_dir = Path.home()
        app_dir = home_dir / ".youtube_downloader"
        
        # Create app directory if it doesn't exist
        os.makedirs(app_dir, exist_ok=True)
        
        return app_dir / "config.json"
    
    def _get_default_config(self) -> DownloadConfig:
        """Get default configuration.
        
        Returns:
            Default DownloadConfig object
        """
        # Use user's home directory for downloads
        home_dir = Path.home()
        downloads_dir = home_dir / "Downloads" / "YouTubeDownloader"
        
        return DownloadConfig(
            download_dir=str(downloads_dir),
            max_concurrent_downloads=3,
            default_format=VideoQuality.HIGH
        )
    
    async def _load_config_dict(self) -> Dict[str, Any]:
        """Load configuration from file as dictionary.
        
        Returns:
            Configuration dictionary
            
        Raises:
            ConfigLoadException: If configuration cannot be loaded
        """
        try:
            # Read file asynchronously
            loop = asyncio.get_event_loop()
            with open(self.config_path, 'r') as f:
                config_str = await loop.run_in_executor(None, f.read)
                
            # Parse JSON
            return json.loads(config_str)
            
        except FileNotFoundError:
            return {}
        except json.JSONDecodeError as e:
            raise ConfigLoadException(f"Failed to parse configuration file: {str(e)}")
        except Exception as e:
            raise ConfigLoadException(f"Failed to load configuration: {str(e)}")
    
    async def _save_config_dict(self, config_dict: Dict[str, Any]) -> bool:
        """Save configuration dictionary to file.
        
        Args:
            config_dict: Configuration dictionary
            
        Returns:
            True if configuration was saved successfully
            
        Raises:
            ConfigSaveException: If configuration cannot be saved
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # Write file asynchronously
            config_str = json.dumps(config_dict, indent=4)
            loop = asyncio.get_event_loop()
            
            async def write_file():
                with open(self.config_path, 'w') as f:
                    await loop.run_in_executor(None, f.write, config_str)
                    
            await write_file()
            return True
            
        except Exception as e:
            raise ConfigSaveException(f"Failed to save configuration: {str(e)}")
    
    def _dict_to_download_config(self, config_dict: Dict[str, Any]) -> DownloadConfig:
        """Convert dictionary to DownloadConfig object.
        
        Args:
            config_dict: Configuration dictionary
            
        Returns:
            DownloadConfig object
        """
        # Extract values with defaults
        download_dir = config_dict.get('download_dir', '')
        max_concurrent_downloads = config_dict.get('max_concurrent_downloads', 3)
        
        # Handle default_format (convert string to enum if needed)
        default_format = config_dict.get('default_format', VideoQuality.HIGH)
        if isinstance(default_format, str):
            try:
                default_format = VideoQuality[default_format.upper()]
            except KeyError:
                default_format = VideoQuality.HIGH
                
        return DownloadConfig(
            download_dir=download_dir,
            max_concurrent_downloads=max_concurrent_downloads,
            default_format=default_format
        )
    
    def _download_config_to_dict(self, config: DownloadConfig) -> Dict[str, Any]:
        """Convert DownloadConfig object to dictionary.
        
        Args:
            config: DownloadConfig object
            
        Returns:
            Configuration dictionary
        """
        # Convert enum to string
        default_format = config.default_format
        if isinstance(default_format, VideoQuality):
            default_format = default_format.name
            
        return {
            'download_dir': config.download_dir,
            'max_concurrent_downloads': config.max_concurrent_downloads,
            'default_format': default_format
        }


# Example usage
def main():
    """Example usage of ConfigService."""
    async def example():
        # Create config service
        service = ConfigService()
        
        try:
            # Load config
            config = await service.load_config()
            print(f"Loaded config: {config}")
            
            # Get setting
            download_dir = await service.get_setting('download_dir')
            print(f"Download directory: {download_dir}")
            
            # Update setting
            await service.update_setting('max_concurrent_downloads', 5)
            print(f"Updated max concurrent downloads to 5")
            
            # Get updated setting
            max_downloads = await service.get_setting('max_concurrent_downloads')
            print(f"Max concurrent downloads: {max_downloads}")
            
            # Reset to defaults
            await service.reset_to_defaults()
            print("Reset to defaults")
            
            # Get default setting
            max_downloads = await service.get_setting('max_concurrent_downloads')
            print(f"Default max concurrent downloads: {max_downloads}")
            
        except ConfigServiceException as e:
            print(f"Error: {e}")
    
    # Run the async example
    asyncio.run(example())


if __name__ == "__main__":
    main()