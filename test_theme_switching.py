#!/usr/bin/env python3
"""
Theme Switching Test Script

This script tests the theme switching functionality of the YouTube Downloader V2 application.
It creates a simple window with a theme switcher component to verify that themes can be changed.
"""

import sys
from pathlib import Path

# Add parent directory to path to allow importing from other packages
sys.path.insert(0, str(Path(__file__).parent))

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
    from PyQt6.QtCore import Qt
    USE_PYQT = True
except ImportError:
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
        from PySide6.QtCore import Qt
        USE_PYQT = False
    except ImportError:
        print("Error: PyQt6 or PySide6 is required. Please install with 'pip install PyQt6' or 'pip install PySide6'")
        sys.exit(1)

from src.config.app_config import AppConfig
from src.ui.components.theme_switcher import ThemeSwitcher, ThemePreview
from src.ui.utils.style_manager import StyleManager


class ThemeTestWindow(QMainWindow):
    """Test window for theme switching functionality."""
    
    def __init__(self):
        """Initialize the test window."""
        super().__init__()
        
        # Set window properties
        self.setWindowTitle("Theme Switching Test")
        self.setMinimumSize(600, 400)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Add title label
        title_label = QLabel("YouTube Downloader V2 - Theme Test")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = title_label.font()
        font.setPointSize(16)
        title_label.setFont(font)
        layout.addWidget(title_label)
        
        # Add theme switcher
        self.theme_switcher = ThemeSwitcher()
        layout.addWidget(self.theme_switcher)
        
        # Add theme preview
        self.theme_preview = ThemePreview()
        layout.addWidget(self.theme_preview)
        
        # Connect theme switcher signal
        self.theme_switcher.theme_changed.connect(self._on_theme_changed)
    
    def _on_theme_changed(self, theme):
        """Handle theme change event.
        
        Args:
            theme: The new theme name
        """
        # Get the application instance
        app = QApplication.instance()
        
        # Apply the theme
        StyleManager.apply_theme(app, theme)
        
        # Update the preview
        self.theme_preview.update_preview()


def main():
    """Main entry point for the test script."""
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("YouTube Downloader V2 - Theme Test")
    
    # Load configuration
    config = AppConfig()
    
    # Create and show test window
    window = ThemeTestWindow()
    window.show()
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()